import 'package:flutter/material.dart';

/// Gradientes personalizados para la aplicación 3M Shop
class AppGradients {
  
  // Gradientes principales
  static const LinearGradient primary = LinearGradient(
    colors: [Color(0xFF667eea), Color(0xFF764ba2)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient secondary = LinearGradient(
    colors: [Color(0xFF4facfe), Color(0xFF00f2fe)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  // Gradientes de estado
  static const LinearGradient success = LinearGradient(
    colors: [Color(0xFF4CAF50), Color(0xFF2E7D32)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient error = LinearGradient(
    colors: [Color(0xFFE53935), Color(0xFFC62828)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient warning = LinearGradient(
    colors: [Color(0xFFFF9800), Color(0xFFE65100)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient info = LinearGradient(
    colors: [Color(0xFF2196F3), Color(0xFF0D47A1)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  // Gradientes para módulos específicos
  static const LinearGradient clientes = LinearGradient(
    colors: [Color(0xFF4CAF50), Color(0xFF66BB6A)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient facturas = LinearGradient(
    colors: [Color(0xFF2196F3), Color(0xFF42A5F5)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient modulos = LinearGradient(
    colors: [Color(0xFF9C27B0), Color(0xFFBA68C8)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient reportes = LinearGradient(
    colors: [Color(0xFFFF9800), Color(0xFFFFB74D)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient configuracion = LinearGradient(
    colors: [Color(0xFF607D8B), Color(0xFF78909C)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  // Gradientes especiales
  static const LinearGradient sunset = LinearGradient(
    colors: [Color(0xFFFF7043), Color(0xFFFF5722)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient ocean = LinearGradient(
    colors: [Color(0xFF00BCD4), Color(0xFF0097A7)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient forest = LinearGradient(
    colors: [Color(0xFF4CAF50), Color(0xFF388E3C)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient royal = LinearGradient(
    colors: [Color(0xFF673AB7), Color(0xFF512DA8)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  // Gradientes radiales
  static const RadialGradient radialPrimary = RadialGradient(
    colors: [Color(0xFF667eea), Color(0xFF764ba2)],
    center: Alignment.center,
    radius: 1.0,
  );
  
  static const RadialGradient radialSuccess = RadialGradient(
    colors: [Color(0xFF4CAF50), Color(0xFF2E7D32)],
    center: Alignment.center,
    radius: 1.0,
  );
  
  // Gradientes para fondos
  static const LinearGradient backgroundLight = LinearGradient(
    colors: [Color(0xFFF5F7FA), Color(0xFFC3CFE2)],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );
  
  static const LinearGradient backgroundDark = LinearGradient(
    colors: [Color(0xFF2C3E50), Color(0xFF34495E)],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );
  
  // Gradientes para cards
  static const LinearGradient cardLight = LinearGradient(
    colors: [Colors.white, Color(0xFFF8F9FA)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient cardDark = LinearGradient(
    colors: [Color(0xFF2C3E50), Color(0xFF34495E)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  // Gradientes animados (para usar con AnimatedContainer)
  static List<LinearGradient> get animatedGradients => [
    primary,
    secondary,
    success,
    warning,
    info,
    sunset,
    ocean,
    forest,
    royal,
  ];
  
  // Método para obtener gradiente por nombre
  static LinearGradient getGradient(String name) {
    switch (name.toLowerCase()) {
      case 'primary':
        return primary;
      case 'secondary':
        return secondary;
      case 'success':
        return success;
      case 'error':
        return error;
      case 'warning':
        return warning;
      case 'info':
        return info;
      case 'clientes':
        return clientes;
      case 'facturas':
        return facturas;
      case 'modulos':
        return modulos;
      case 'reportes':
        return reportes;
      case 'configuracion':
        return configuracion;
      case 'sunset':
        return sunset;
      case 'ocean':
        return ocean;
      case 'forest':
        return forest;
      case 'royal':
        return royal;
      default:
        return primary;
    }
  }
  
  // Método para crear gradiente personalizado
  static LinearGradient custom({
    required List<Color> colors,
    Alignment begin = Alignment.topLeft,
    Alignment end = Alignment.bottomRight,
    List<double>? stops,
  }) {
    return LinearGradient(
      colors: colors,
      begin: begin,
      end: end,
      stops: stops,
    );
  }
  
  // Método para crear gradiente con opacidad
  static LinearGradient withOpacity(LinearGradient gradient, double opacity) {
    return LinearGradient(
      colors: gradient.colors.map((color) => color.withValues(alpha: opacity)).toList(),
      begin: gradient.begin,
      end: gradient.end,
      stops: gradient.stops,
    );
  }
  
  // Gradientes para diferentes estados de botones
  static const LinearGradient buttonPrimary = LinearGradient(
    colors: [Color(0xFF667eea), Color(0xFF764ba2)],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );
  
  static const LinearGradient buttonSecondary = LinearGradient(
    colors: [Color(0xFF4facfe), Color(0xFF00f2fe)],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );
  
  static const LinearGradient buttonDisabled = LinearGradient(
    colors: [Color(0xFFBDBDBD), Color(0xFF9E9E9E)],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );
  
  // Gradientes para indicadores de progreso
  static const LinearGradient progressPrimary = LinearGradient(
    colors: [Color(0xFF667eea), Color(0xFF764ba2)],
    begin: Alignment.centerLeft,
    end: Alignment.centerRight,
  );
  
  static const LinearGradient progressSuccess = LinearGradient(
    colors: [Color(0xFF4CAF50), Color(0xFF2E7D32)],
    begin: Alignment.centerLeft,
    end: Alignment.centerRight,
  );
  
  static const LinearGradient progressWarning = LinearGradient(
    colors: [Color(0xFFFF9800), Color(0xFFE65100)],
    begin: Alignment.centerLeft,
    end: Alignment.centerRight,
  );
  
  static const LinearGradient progressError = LinearGradient(
    colors: [Color(0xFFE53935), Color(0xFFC62828)],
    begin: Alignment.centerLeft,
    end: Alignment.centerRight,
  );
}

/// Extensión para facilitar el uso de gradientes
extension GradientExtension on Container {
  Container withGradient(LinearGradient gradient) {
    return Container(
      decoration: BoxDecoration(
        gradient: gradient,
        borderRadius: (decoration as BoxDecoration?)?.borderRadius,
        border: (decoration as BoxDecoration?)?.border,
        boxShadow: (decoration as BoxDecoration?)?.boxShadow,
      ),
      child: child,
    );
  }
}
