import 'package:flutter/material.dart';

/// Widget reutilizable para manejar estados de carga, error y datos vacíos
/// de manera consistente en toda la aplicación
class LoadingStateBuilder<T> extends StatelessWidget {
  final Future<T> future;
  final Widget Function(BuildContext context, T data) builder;
  final String? emptyMessage;
  final String? errorMessage;
  final Widget? customLoadingWidget;
  final Widget? customEmptyWidget;
  final Widget? customErrorWidget;
  final bool Function(T data)? isEmpty;

  const LoadingStateBuilder({
    super.key,
    required this.future,
    required this.builder,
    this.emptyMessage,
    this.errorMessage,
    this.customLoadingWidget,
    this.customEmptyWidget,
    this.customErrorWidget,
    this.isEmpty,
  });

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<T>(
      future: future,
      builder: (context, snapshot) {
        // Estado de carga
        if (snapshot.connectionState == ConnectionState.waiting) {
          return customLoadingWidget ?? _buildDefaultLoadingWidget();
        }

        // Estado de error
        if (snapshot.hasError) {
          return customErrorWidget ?? 
                 _buildDefaultErrorWidget(context, snapshot.error);
        }

        // Verificar si hay datos
        if (!snapshot.hasData) {
          return customEmptyWidget ?? 
                 _buildDefaultEmptyWidget(context, "No hay datos disponibles");
        }

        final data = snapshot.data;
        if (data == null) {
          return customEmptyWidget ??
                 _buildDefaultEmptyWidget(context, "No hay datos disponibles");
        }

        // Verificar si los datos están vacíos usando función personalizada o por defecto
        bool dataIsEmpty = false;
        if (isEmpty != null) {
          dataIsEmpty = isEmpty!(data);
        } else if (data is List) {
          dataIsEmpty = (data).isEmpty;
        } else if (data is Map) {
          dataIsEmpty = (data).isEmpty;
        }

        if (dataIsEmpty) {
          return customEmptyWidget ??
                 _buildDefaultEmptyWidget(context, emptyMessage ?? "No hay datos disponibles");
        }

        // Construir el widget con los datos
        return builder(context, data);
      },
    );
  }

  Widget _buildDefaultLoadingWidget() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            strokeWidth: 3,
          ),
          SizedBox(height: 16),
          Text(
            "Cargando...",
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDefaultErrorWidget(BuildContext context, Object? error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              errorMessage ?? "Error al cargar los datos",
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.error,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              error.toString(),
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () {
                // Trigger rebuild to retry
                // This would need to be handled by the parent widget
              },
              icon: const Icon(Icons.refresh),
              label: const Text("Reintentar"),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDefaultEmptyWidget(BuildContext context, String message) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inbox_outlined,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              message,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            const Text(
              "No se encontraron elementos para mostrar",
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

/// Widget especializado para listas con funcionalidad de refresco
class RefreshableLoadingStateBuilder<T> extends StatefulWidget {
  final Future<T> Function() futureBuilder;
  final Widget Function(BuildContext context, T data) builder;
  final String? emptyMessage;
  final String? errorMessage;
  final bool Function(T data)? isEmpty;

  const RefreshableLoadingStateBuilder({
    super.key,
    required this.futureBuilder,
    required this.builder,
    this.emptyMessage,
    this.errorMessage,
    this.isEmpty,
  });

  @override
  State<RefreshableLoadingStateBuilder<T>> createState() => 
      _RefreshableLoadingStateBuilderState<T>();
}

class _RefreshableLoadingStateBuilderState<T> 
    extends State<RefreshableLoadingStateBuilder<T>> {
  late Future<T> _future;

  @override
  void initState() {
    super.initState();
    _future = widget.futureBuilder();
  }

  void _refresh() {
    setState(() {
      _future = widget.futureBuilder();
    });
  }

  @override
  Widget build(BuildContext context) {
    return LoadingStateBuilder<T>(
      future: _future,
      builder: widget.builder,
      emptyMessage: widget.emptyMessage,
      errorMessage: widget.errorMessage,
      isEmpty: widget.isEmpty,
      customErrorWidget: _buildErrorWidgetWithRetry(context),
    );
  }

  Widget _buildErrorWidgetWithRetry(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              widget.errorMessage ?? "Error al cargar los datos",
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.error,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _refresh,
              icon: const Icon(Icons.refresh),
              label: const Text("Reintentar"),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
