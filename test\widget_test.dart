// Tests para la aplicación Shop 3M
//
// Este archivo contiene tests de widgets básicos para verificar
// que la aplicación se inicializa correctamente.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shop_3m/main.dart';

void main() {
  group('App Initialization Tests', () {
    testWidgets('App should initialize and show home screen', (WidgetTester tester) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(const MyApp());

      // Verify that the app title is correct
      expect(find.text('Menú Principal'), findsOneWidget);

      // Verify that main menu options are present
      expect(find.text('Clientes'), findsOneWidget);
      expect(find.text('Facturas'), findsOneWidget);
      expect(find.text('Módulos'), findsOneWidget);
      expect(find.text('Reportes'), findsOneWidget);
      // Note: Configuración might not be visible in the test environment
    });

    testWidgets('Navigation to Clientes screen should work', (WidgetTester tester) async {
      await tester.pumpWidget(const MyApp());

      // Tap on Clientes card
      await tester.tap(find.text('Clientes'));
      await tester.pumpAndSettle();

      // Verify that we navigated to Clientes screen
      expect(find.text('Gestión de Clientes'), findsOneWidget);
    });

    testWidgets('Navigation to Facturas screen should work', (WidgetTester tester) async {
      await tester.pumpWidget(const MyApp());

      // Tap on Facturas card
      await tester.tap(find.text('Facturas'));
      await tester.pumpAndSettle();

      // Verify that we navigated to Facturas screen
      expect(find.text('Facturas'), findsWidgets); // Title in AppBar
    });

    testWidgets('App should have correct theme configuration', (WidgetTester tester) async {
      await tester.pumpWidget(const MyApp());

      final MaterialApp app = tester.widget(find.byType(MaterialApp));

      // Verify app title
      expect(app.title, '3M Shop');

      // Verify theme mode is system
      expect(app.themeMode, ThemeMode.system);

      // Verify that both light and dark themes are configured
      expect(app.theme, isNotNull);
      expect(app.darkTheme, isNotNull);
    });
  });
}
