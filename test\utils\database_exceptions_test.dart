// Tests unitarios para las excepciones de base de datos

import 'package:flutter_test/flutter_test.dart';
import 'package:shop_3m/utils/database_exceptions.dart';

void main() {
  group('Database Exceptions Tests', () {
    group('DatabaseException Base Class', () {
      test('should create exception with message only', () {
        // Arrange & Act
        const exception = DatabaseInitializationException('Test message');

        // Assert
        expect(exception.message, 'Test message');
        expect(exception.details, isNull);
        expect(exception.originalException, isNull);
      });

      test('should create exception with message and details', () {
        // Arrange & Act
        const exception = DatabaseInitializationException(
          'Test message',
          details: 'Additional details',
        );

        // Assert
        expect(exception.message, 'Test message');
        expect(exception.details, 'Additional details');
        expect(exception.originalException, isNull);
      });

      test('should create exception with all parameters', () {
        // Arrange
        final originalException = Exception('Original error');

        // Act
        final exception = DatabaseInitializationException(
          'Test message',
          details: 'Additional details',
          originalException: originalException,
        );

        // Assert
        expect(exception.message, 'Test message');
        expect(exception.details, 'Additional details');
        expect(exception.originalException, originalException);
      });
    });

    group('DatabaseInitializationException', () {
      test('toString should return formatted message without details', () {
        // Arrange
        const exception = DatabaseInitializationException('Init failed');

        // Act
        final string = exception.toString();

        // Assert
        expect(string, 'DatabaseException: Init failed');
      });

      test('toString should return formatted message with details', () {
        // Arrange
        const exception = DatabaseInitializationException(
          'Init failed',
          details: 'File not found',
        );

        // Act
        final string = exception.toString();

        // Assert
        expect(string, contains('DatabaseException: Init failed'));
        expect(string, contains('Detalles: File not found'));
      });
    });

    group('DatabaseInsertException', () {
      test('should store table name correctly', () {
        // Arrange & Act
        const exception = DatabaseInsertException(
          'Insert failed',
          'Cliente',
        );

        // Assert
        expect(exception.tableName, 'Cliente');
        expect(exception.message, 'Insert failed');
      });

      test('toString should include table name', () {
        // Arrange
        const exception = DatabaseInsertException(
          'Insert failed',
          'Cliente',
        );

        // Act
        final string = exception.toString();

        // Assert
        expect(string, 'Error al insertar en tabla Cliente: Insert failed');
      });
    });

    group('DatabaseUpdateException', () {
      test('should store table name correctly', () {
        // Arrange & Act
        const exception = DatabaseUpdateException(
          'Update failed',
          'Factura',
        );

        // Assert
        expect(exception.tableName, 'Factura');
        expect(exception.message, 'Update failed');
      });

      test('toString should include table name', () {
        // Arrange
        const exception = DatabaseUpdateException(
          'Update failed',
          'Factura',
        );

        // Act
        final string = exception.toString();

        // Assert
        expect(string, 'Error al actualizar tabla Factura: Update failed');
      });
    });

    group('DatabaseDeleteException', () {
      test('should store table name correctly', () {
        // Arrange & Act
        const exception = DatabaseDeleteException(
          'Delete failed',
          'DetalleFactura',
        );

        // Assert
        expect(exception.tableName, 'DetalleFactura');
        expect(exception.message, 'Delete failed');
      });

      test('toString should include table name', () {
        // Arrange
        const exception = DatabaseDeleteException(
          'Delete failed',
          'DetalleFactura',
        );

        // Act
        final string = exception.toString();

        // Assert
        expect(string, 'Error al eliminar de tabla DetalleFactura: Delete failed');
      });
    });

    group('DatabaseQueryException', () {
      test('should store table name correctly', () {
        // Arrange & Act
        const exception = DatabaseQueryException(
          'Query failed',
          'Modulo',
        );

        // Assert
        expect(exception.tableName, 'Modulo');
        expect(exception.message, 'Query failed');
      });

      test('toString should include table name', () {
        // Arrange
        const exception = DatabaseQueryException(
          'Query failed',
          'Modulo',
        );

        // Act
        final string = exception.toString();

        // Assert
        expect(string, 'Error al consultar tabla Modulo: Query failed');
      });
    });

    group('DatabaseConstraintException', () {
      test('should store constraint type correctly', () {
        // Arrange & Act
        const exception = DatabaseConstraintException(
          'Constraint violated',
          'UNIQUE',
        );

        // Assert
        expect(exception.constraintType, 'UNIQUE');
        expect(exception.message, 'Constraint violated');
      });

      test('toString should include constraint type', () {
        // Arrange
        const exception = DatabaseConstraintException(
          'Constraint violated',
          'FOREIGN KEY',
        );

        // Act
        final string = exception.toString();

        // Assert
        expect(string, 'Violación de restricción FOREIGN KEY: Constraint violated');
      });
    });

    group('RecordNotFoundException', () {
      test('should store table name and search criteria correctly', () {
        // Arrange & Act
        const exception = RecordNotFoundException(
          'Cliente',
          'IdCliente = 123',
        );

        // Assert
        expect(exception.tableName, 'Cliente');
        expect(exception.searchCriteria, 'IdCliente = 123');
        expect(exception.message, 'Registro no encontrado en Cliente con criterio: IdCliente = 123');
      });

      test('should handle details parameter', () {
        // Arrange & Act
        const exception = RecordNotFoundException(
          'Factura',
          'IdFactura = 456',
          details: 'Additional context',
        );

        // Assert
        expect(exception.details, 'Additional context');
      });

      test('toString should return formatted message', () {
        // Arrange
        const exception = RecordNotFoundException(
          'Cliente',
          'Email = <EMAIL>',
        );

        // Act
        final string = exception.toString();

        // Assert
        expect(string, 'DatabaseException: Registro no encontrado en Cliente con criterio: Email = <EMAIL>');
      });
    });

    group('DuplicateRecordException', () {
      test('should store table name and field correctly', () {
        // Arrange & Act
        const exception = DuplicateRecordException(
          'Cliente',
          'email',
        );

        // Assert
        expect(exception.tableName, 'Cliente');
        expect(exception.field, 'email');
        expect(exception.message, 'Ya existe un registro en Cliente con el mismo email');
      });

      test('should handle details parameter', () {
        // Arrange & Act
        const exception = DuplicateRecordException(
          'Modulo',
          'nombre',
          details: 'Duplicate module name detected',
        );

        // Assert
        expect(exception.details, 'Duplicate module name detected');
      });

      test('toString should return formatted message', () {
        // Arrange
        const exception = DuplicateRecordException(
          'Cliente',
          'email',
        );

        // Act
        final string = exception.toString();

        // Assert
        expect(string, 'DatabaseException: Ya existe un registro en Cliente con el mismo email');
      });
    });

    group('Exception Inheritance', () {
      test('all exceptions should implement Exception', () {
        // Arrange & Act
        const initException = DatabaseInitializationException('test');
        const insertException = DatabaseInsertException('test', 'table');
        const updateException = DatabaseUpdateException('test', 'table');
        const deleteException = DatabaseDeleteException('test', 'table');
        const queryException = DatabaseQueryException('test', 'table');
        const constraintException = DatabaseConstraintException('test', 'constraint');
        const notFoundException = RecordNotFoundException('table', 'criteria');
        const duplicateException = DuplicateRecordException('table', 'field');

        // Assert
        expect(initException, isA<Exception>());
        expect(insertException, isA<Exception>());
        expect(updateException, isA<Exception>());
        expect(deleteException, isA<Exception>());
        expect(queryException, isA<Exception>());
        expect(constraintException, isA<Exception>());
        expect(notFoundException, isA<Exception>());
        expect(duplicateException, isA<Exception>());
      });

      test('all exceptions should extend DatabaseException', () {
        // Arrange & Act
        const initException = DatabaseInitializationException('test');
        const insertException = DatabaseInsertException('test', 'table');
        const updateException = DatabaseUpdateException('test', 'table');
        const deleteException = DatabaseDeleteException('test', 'table');
        const queryException = DatabaseQueryException('test', 'table');
        const constraintException = DatabaseConstraintException('test', 'constraint');
        const notFoundException = RecordNotFoundException('table', 'criteria');
        const duplicateException = DuplicateRecordException('table', 'field');

        // Assert
        expect(initException, isA<DatabaseException>());
        expect(insertException, isA<DatabaseException>());
        expect(updateException, isA<DatabaseException>());
        expect(deleteException, isA<DatabaseException>());
        expect(queryException, isA<DatabaseException>());
        expect(constraintException, isA<DatabaseException>());
        expect(notFoundException, isA<DatabaseException>());
        expect(duplicateException, isA<DatabaseException>());
      });
    });
  });
}
