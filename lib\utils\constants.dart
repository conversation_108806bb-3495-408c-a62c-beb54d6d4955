import 'package:flutter/material.dart';

/// Constantes globales para la aplicación 3M Shop
class AppConstants {
  
  // Información de la aplicación
  static const String appName = '3M Shop';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'Sistema de gestión de facturas y módulos';
  
  // Configuración de paginación
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // Límites de validación
  static const int minPasswordLength = 6;
  static const int maxNameLength = 50;
  static const int maxDescriptionLength = 500;
  
  // Timeouts
  static const Duration networkTimeout = Duration(seconds: 30);
  static const Duration debounceDelay = Duration(milliseconds: 500);
  
  // Colores del tema
  static const Color primaryColor = Colors.indigo;
  static const Color secondaryColor = Colors.teal;
  static const Color errorColor = Colors.red;
  static const Color successColor = Colors.green;
  static const Color warningColor = Colors.orange;
  
  // Gradientes
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primaryColor, secondaryColor],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  // Breakpoints responsivos
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 900;
  static const double desktopBreakpoint = 1200;
  
  // Espaciado
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;
  static const double paddingXLarge = 32.0;
  
  // Border radius
  static const double borderRadiusSmall = 8.0;
  static const double borderRadiusMedium = 12.0;
  static const double borderRadiusLarge = 16.0;
  
  // Elevaciones
  static const double elevationLow = 2.0;
  static const double elevationMedium = 4.0;
  static const double elevationHigh = 8.0;
  
  // Mensajes de error comunes
  static const String errorGeneral = 'Ha ocurrido un error inesperado';
  static const String errorNetwork = 'Error de conexión. Verifica tu internet';
  static const String errorPermissions = 'No tienes permisos para esta acción';
  static const String errorNotFound = 'Elemento no encontrado';
  
  // Mensajes de éxito
  static const String successSaved = 'Guardado exitosamente';
  static const String successDeleted = 'Eliminado exitosamente';
  static const String successUpdated = 'Actualizado exitosamente';
  
  // Regex patterns
  static const String emailPattern = r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$';
  static const String phonePattern = r'^\+?[1-9]\d{1,14}$';
  
  // Formatos de fecha
  static const String dateFormat = 'dd/MM/yyyy';
  static const String dateTimeFormat = 'dd/MM/yyyy HH:mm';
  static const String timeFormat = 'HH:mm';
}
