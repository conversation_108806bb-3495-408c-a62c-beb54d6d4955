plugins {
    id("com.android.application")
    id("kotlin-android")
    // 🔹 Flutter Gradle Plugin siempre después de Android y Kotlin
    id("dev.flutter.flutter-gradle-plugin")
}

// Configuración de keystore directa

android {
    namespace = "com.example.shop_3m"
    compileSdk = flutter.compileSdkVersion

    // ✅ Corrección: Colocar `ndkVersion` en el lugar correcto
    ndkVersion = "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
        isCoreLibraryDesugaringEnabled = true
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_11.toString()
    }

    defaultConfig {
        applicationId = "com.example.shop_3m"
        minSdk = 23  // Requerido por Firebase Auth
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }

    signingConfigs {
        create("release") {
            keyAlias = "shop3m"
            keyPassword = "shop3m123"
            storeFile = file("../shop3m-release-key.keystore")
            storePassword = "shop3m123"
        }
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            isShrinkResources = false
            signingConfig = signingConfigs.getByName("release")
        }
    }
}

dependencies {
    coreLibraryDesugaring("com.android.tools:desugar_jdk_libs:2.0.4")
}

flutter {
    source = "../.."
}