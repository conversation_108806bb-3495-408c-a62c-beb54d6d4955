import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../services/factura_service.dart';

class DebugFacturasScreen extends StatefulWidget {
  const DebugFacturasScreen({super.key});

  @override
  State<DebugFacturasScreen> createState() => _DebugFacturasScreenState();
}

class _DebugFacturasScreenState extends State<DebugFacturasScreen> {
  final List<String> _debugLogs = [];
  bool _isLoading = false;

  void _addLog(String message) {
    setState(() {
      _debugLogs.add('${DateTime.now().toIso8601String()}: $message');
    });
    log(message);
  }

  Future<void> _verificarFacturasDirectas() async {
    setState(() {
      _isLoading = true;
      _debugLogs.clear();
    });

    _addLog('🔍 Verificando facturas directamente en Firebase...');

    try {
      // Consulta directa a Firebase
      final snapshot =
          await FirebaseFirestore.instance.collection('facturas').get();

      _addLog(
          '📊 Documentos encontrados en colección "facturas": ${snapshot.docs.length}');

      for (var doc in snapshot.docs) {
        final data = doc.data();
        _addLog('📄 Documento ${doc.id}: $data');
      }

      if (snapshot.docs.isEmpty) {
        _addLog('⚠️ No hay documentos en la colección "facturas"');
      }
    } catch (e) {
      _addLog('❌ Error consultando Firebase directamente: $e');
    }

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _verificarFacturasServicio() async {
    setState(() {
      _isLoading = true;
    });

    _addLog('🔍 Verificando facturas usando FacturaService...');

    try {
      // Usar el servicio
      final facturas = await FacturaService.obtenerFacturasPaginadas(
        limit: 50,
        offset: 0,
      );

      _addLog('📊 Facturas obtenidas por servicio: ${facturas.length}');

      for (var factura in facturas) {
        _addLog('📄 Factura procesada: $factura');
      }

      if (facturas.isEmpty) {
        _addLog('⚠️ El servicio no devolvió facturas');
      }
    } catch (e) {
      _addLog('❌ Error usando FacturaService: $e');
    }

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _contarFacturas() async {
    setState(() {
      _isLoading = true;
    });

    _addLog('🔢 Contando facturas...');

    try {
      final count = await FacturaService.contarFacturas();
      _addLog('📊 Total de facturas según servicio: $count');
    } catch (e) {
      _addLog('❌ Error contando facturas: $e');
    }

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _crearFacturaPrueba() async {
    setState(() {
      _isLoading = true;
    });

    _addLog('🧪 Creando facturas de prueba...');

    try {
      // Crear múltiples facturas de prueba
      for (int i = 1; i <= 3; i++) {
        final idFactura =
            'FAC_DEBUG_${DateTime.now().millisecondsSinceEpoch}_$i';

        await FirebaseFirestore.instance
            .collection('facturas')
            .doc(idFactura)
            .set({
          'idFactura': idFactura,
          'clienteId': 'CLI_DEBUG_$i',
          'clienteNombre': 'Cliente Debug $i',
          'metodoPago': i == 1
              ? 'Efectivo'
              : i == 2
                  ? 'Tarjeta'
                  : 'Transferencia',
          'total': (50000.0 * i),
          'estado': 'completada',
          'fechaCreacion': FieldValue.serverTimestamp(),
          'comprobante': '',
        });

        _addLog('✅ Factura de prueba creada: $idFactura');

        // Pequeña pausa entre creaciones
        await Future.delayed(const Duration(milliseconds: 500));
      }

      // Verificar que se crearon
      await Future.delayed(const Duration(seconds: 2));
      await _verificarFacturasDirectas();
    } catch (e) {
      _addLog('❌ Error creando facturas de prueba: $e');
    }

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _corregirNombresClientes() async {
    setState(() {
      _isLoading = true;
    });

    _addLog('🔧 Corrigiendo nombres de clientes en facturas existentes...');

    try {
      // Obtener todas las facturas
      final facturasSnapshot =
          await FirebaseFirestore.instance.collection('facturas').get();

      _addLog('📊 Facturas encontradas: ${facturasSnapshot.docs.length}');

      int corregidas = 0;
      for (var facturaDoc in facturasSnapshot.docs) {
        final facturaData = facturaDoc.data();
        final clienteId = facturaData['clienteId'];
        final clienteNombreActual = facturaData['clienteNombre'];

        // Si el clienteNombre es igual al clienteId, necesita corrección
        if (clienteId != null && clienteNombreActual == clienteId) {
          try {
            // Obtener el nombre real del cliente
            final clienteDoc = await FirebaseFirestore.instance
                .collection('clientes')
                .doc(clienteId)
                .get();

            if (clienteDoc.exists) {
              final clienteData = clienteDoc.data() as Map<String, dynamic>;
              final nombreReal = clienteData['nombre'];

              if (nombreReal != null && nombreReal != clienteId) {
                // Actualizar la factura con el nombre real
                await facturaDoc.reference.update({
                  'clienteNombre': nombreReal,
                });

                _addLog('✅ Factura ${facturaDoc.id}: $clienteId → $nombreReal');
                corregidas++;
              }
            }
          } catch (e) {
            _addLog('❌ Error corrigiendo factura ${facturaDoc.id}: $e');
          }
        } else {
          _addLog(
              'ℹ️ Factura ${facturaDoc.id} ya tiene nombre correcto: $clienteNombreActual');
        }
      }

      _addLog('🎉 Proceso completado. Facturas corregidas: $corregidas');

      // Verificar resultado
      await Future.delayed(const Duration(seconds: 1));
      await _verificarFacturasServicio();
    } catch (e) {
      _addLog('❌ Error corrigiendo nombres de clientes: $e');
    }

    setState(() {
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Debug Facturas'),
        backgroundColor: Colors.red.shade700,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // Botones de acción
          Container(
            padding: const EdgeInsets.all(16),
            child: Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _verificarFacturasDirectas,
                  icon: const Icon(Icons.search),
                  label: const Text('Verificar Firebase'),
                ),
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _verificarFacturasServicio,
                  icon: const Icon(Icons.api),
                  label: const Text('Verificar Servicio'),
                ),
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _contarFacturas,
                  icon: const Icon(Icons.numbers),
                  label: const Text('Contar Facturas'),
                ),
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _crearFacturaPrueba,
                  icon: const Icon(Icons.add),
                  label: const Text('Crear Prueba'),
                ),
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _corregirNombresClientes,
                  icon: const Icon(Icons.person_search, color: Colors.blue),
                  label: const Text('Corregir Nombres'),
                ),
                ElevatedButton.icon(
                  onPressed: () {
                    setState(() {
                      _debugLogs.clear();
                    });
                  },
                  icon: const Icon(Icons.clear),
                  label: const Text('Limpiar'),
                ),
              ],
            ),
          ),

          // Indicador de carga
          if (_isLoading) const LinearProgressIndicator(),

          // Logs
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.black87,
                borderRadius: BorderRadius.circular(8),
              ),
              child: ListView.builder(
                itemCount: _debugLogs.length,
                itemBuilder: (context, index) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 2),
                    child: Text(
                      _debugLogs[index],
                      style: const TextStyle(
                        color: Colors.green,
                        fontFamily: 'monospace',
                        fontSize: 12,
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
