import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../widgets/gradient_background.dart';
import '../widgets/responsive_container.dart';
import '../widgets/imagen_avatar.dart';
import '../widgets/modern_charts.dart';
import '../widgets/modern_loading.dart';
import '../models/cliente_model.dart';
import '../services/notification_service.dart';
import '../services/cliente_service.dart';
import '../utils/error_handler.dart';
import '../theme/app_gradients.dart';

class ReportesScreen extends StatefulWidget {
  const ReportesScreen({super.key});

  @override
  State<ReportesScreen> createState() => _ReportesScreenState();
}

class _ReportesScreenState extends State<ReportesScreen> {
  List<Cliente> _clientes = [];
  bool _isLoading = true;
  String _filtroSeleccionado = 'todos';

  @override
  void initState() {
    super.initState();
    _cargarClientes();
    NotificationService.initialize();
  }

  Future<void> _cargarClientes() async {
    setState(() => _isLoading = true);

    try {
      // Usar ClienteService en lugar de acceso directo a Firebase
      final todosLosClientes = await ClienteService.obtenerClientes();

      // Debug logs comentados para producción
      // print('🔍 Total clientes cargados: ${todosLosClientes.length}');
      // for (var cliente in todosLosClientes) {
      //   print('👤 Cliente: ${cliente.nombre}');
      //   print('   - fechaVencimientoSuscripcion: ${cliente.fechaVencimientoSuscripcion}');
      //   print('   - tieneSuscripcion: ${cliente.tieneSuscripcion}');
      //   print('   - estadoSuscripcion: ${cliente.estadoSuscripcion}');
      //   print('   - montoSuscripcion: ${cliente.montoSuscripcion}');
      //   print('   - tipoSuscripcion: ${cliente.tipoSuscripcion}');
      //   print('   - fechaInicioSuscripcion: ${cliente.fechaInicioSuscripcion}');
      // }

      final clientes = todosLosClientes
          .where((cliente) => cliente.tieneSuscripcion)
          .toList();

      // print('📊 Clientes con suscripción: ${clientes.length}');

      // Ordenar por fecha de vencimiento
      clientes.sort((a, b) {
        if (a.fechaVencimientoSuscripcion == null) return 1;
        if (b.fechaVencimientoSuscripcion == null) return -1;
        return a.fechaVencimientoSuscripcion!
            .compareTo(b.fechaVencimientoSuscripcion!);
      });

      setState(() {
        _clientes = clientes;
        _isLoading = false;
      });
    } catch (e) {
      ErrorHandler.logError('ReportesScreen._cargarClientes', e);
      if (mounted) {
        ErrorHandler.showError(context, 'Error al cargar los reportes');
      }
      setState(() => _isLoading = false);
    }
  }

  List<Cliente> get _clientesFiltrados {
    switch (_filtroSeleccionado) {
      case 'vencidas':
        return _clientes.where((c) => c.suscripcionVencida).toList();
      case 'por_vencer':
        return _clientes.where((c) => c.necesitaNotificacion).toList();
      case 'activas':
        return _clientes.where((c) => c.suscripcionActiva).toList();
      default:
        return _clientes;
    }
  }

  @override
  Widget build(BuildContext context) {
    return GradientBackground(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: AppBar(
          title: const Text(
            'Reportes de Suscripciones',
            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
          backgroundColor: Colors.transparent,
          elevation: 0,
          actions: [
            IconButton(
              icon: const Icon(Icons.notifications_active, color: Colors.white),
              onPressed: () async {
                final ctx = context;
                await NotificationService.verificarSuscripcionesYNotificar();
                if (mounted && ctx.mounted) {
                  ErrorHandler.showSuccess(
                      ctx, 'Verificación de notificaciones completada');
                }
              },
              tooltip: 'Verificar notificaciones',
            ),
            IconButton(
              icon: const Icon(Icons.refresh, color: Colors.white),
              onPressed: _cargarClientes,
              tooltip: 'Actualizar',
            ),
          ],
        ),
        body: ResponsiveContainer(
          child: _isLoading ? _buildLoading() : _buildContent(),
        ),
      ),
    );
  }

  Widget _buildLoading() {
    return const Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
      ),
    );
  }

  Widget _buildContent() {
    return Column(
      children: [
        _buildResumenCards(),
        const SizedBox(height: 16),
        _buildGraficosSection(),
        const SizedBox(height: 16),
        _buildFiltros(),
        const SizedBox(height: 16),
        Expanded(child: _buildListaClientes()),
      ],
    );
  }

  Widget _buildResumenCards() {
    final vencidas = _clientes.where((c) => c.suscripcionVencida).length;
    final porVencer = _clientes.where((c) => c.necesitaNotificacion).length;
    final activas = _clientes.where((c) => c.suscripcionActiva).length;
    final total = _clientes.length;

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(child: _buildCard('Total', total, Colors.blue)),
          const SizedBox(width: 8),
          Expanded(child: _buildCard('Activas', activas, Colors.green)),
          const SizedBox(width: 8),
          Expanded(child: _buildCard('Por Vencer', porVencer, Colors.orange)),
          const SizedBox(width: 8),
          Expanded(child: _buildCard('Vencidas', vencidas, Colors.red)),
        ],
      ),
    );
  }

  Widget _buildCard(String titulo, int cantidad, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Text(
            cantidad.toString(),
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            titulo,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.white70,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFiltros() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          const Text(
            'Filtrar:',
            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  _buildFiltroChip('Todos', 'todos'),
                  _buildFiltroChip('Activas', 'activas'),
                  _buildFiltroChip('Por Vencer', 'por_vencer'),
                  _buildFiltroChip('Vencidas', 'vencidas'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFiltroChip(String label, String valor) {
    final isSelected = _filtroSeleccionado == valor;
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.indigo.shade900 : Colors.white,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
            fontSize: 13,
          ),
        ),
        selected: isSelected,
        onSelected: (selected) {
          setState(() {
            _filtroSeleccionado = valor;
          });
        },
        backgroundColor: Colors.white.withValues(alpha: 0.15),
        selectedColor: Colors.white.withValues(alpha: 0.9),
        side: BorderSide(
          color: isSelected
              ? Colors.white.withValues(alpha: 0.8)
              : Colors.white.withValues(alpha: 0.4),
          width: isSelected ? 2 : 1,
        ),
        elevation: isSelected ? 4 : 1,
        shadowColor: Colors.black.withValues(alpha: 0.3),
      ),
    );
  }

  Widget _buildListaClientes() {
    final clientesFiltrados = _clientesFiltrados;

    if (clientesFiltrados.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inbox_outlined,
              size: 64,
              color: Colors.white.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No hay clientes con suscripciones',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.7),
                fontSize: 16,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: clientesFiltrados.length,
      itemBuilder: (context, index) {
        final cliente = clientesFiltrados[index];
        return _buildClienteCard(cliente);
      },
    );
  }

  Widget _buildClienteCard(Cliente cliente) {
    final diasRestantes = cliente.diasParaVencer;
    Color colorEstado;
    IconData iconoEstado;
    String textoEstado;

    if (cliente.suscripcionVencida) {
      colorEstado = Colors.red;
      iconoEstado = Icons.error;
      textoEstado = 'Vencida hace ${-diasRestantes} días';
    } else if (cliente.necesitaNotificacion) {
      colorEstado = Colors.orange;
      iconoEstado = Icons.warning;
      textoEstado =
          diasRestantes == 0 ? 'Vence HOY' : 'Vence en $diasRestantes días';
    } else {
      colorEstado = Colors.green;
      iconoEstado = Icons.check_circle;
      textoEstado = 'Vence en $diasRestantes días';
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      color: Colors.white.withValues(alpha: 0.1),
      child: ListTile(
        leading: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            AvatarCliente(
              imagenUrl: cliente.imagenUrl,
              nombre: cliente.nombre,
              size: 45,
            ),
            const SizedBox(width: 8),
            Icon(iconoEstado, color: colorEstado),
          ],
        ),
        title: Text(
          cliente.nombre,
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              textoEstado,
              style: TextStyle(color: colorEstado, fontWeight: FontWeight.w500),
            ),
            if (cliente.fechaVencimientoSuscripcion != null)
              Text(
                'Vence: ${_formatearFecha(cliente.fechaVencimientoSuscripcion!)}',
                style: const TextStyle(color: Colors.white70),
              ),
            if (cliente.montoSuscripcion != null)
              Text(
                'Monto: \$${cliente.montoSuscripcion!.toStringAsFixed(2)}',
                style: const TextStyle(color: Colors.white70),
              ),
          ],
        ),
        trailing: cliente.necesitaNotificacion
            ? IconButton(
                icon: const Icon(Icons.notifications, color: Colors.orange),
                onPressed: () => _enviarNotificacionManual(cliente),
                tooltip: 'Enviar notificación',
              )
            : null,
      ),
    );
  }

  String _formatearFecha(DateTime fecha) {
    return '${fecha.day.toString().padLeft(2, '0')}/${fecha.month.toString().padLeft(2, '0')}/${fecha.year}';
  }

  Future<void> _enviarNotificacionManual(Cliente cliente) async {
    // Aquí podrías implementar el envío de notificación manual
    ErrorHandler.showSuccess(
        context, 'Notificación enviada a ${cliente.nombre}');
  }

  Widget _buildGraficosSection() {

    // Datos para gráfico de líneas (suscripciones por mes)
    final lineData = _generateLineChartData();

    // Datos para gráfico de barras (tipos de suscripción)
    final barData = _generateBarChartData();

    // Datos para gráfico circular (estados de suscripción)
    final pieData = _generatePieChartData();

    return SizedBox(
      height: 300,
      child: PageView(
        children: [
          // Gráfico de líneas
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: ModernLineChart(
              data: lineData,
              title: 'Tendencia de Suscripciones',
              color: const Color(0xFF2196F3),
              xLabels: ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun'],
            ),
          ),

          // Gráfico de barras
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: ModernBarChart(
              data: barData,
              title: 'Suscripciones por Tipo',
              color: const Color(0xFF4CAF50),
              xLabels: ['Básica', 'Premium', 'Pro', 'Enterprise'],
            ),
          ),

          // Gráfico circular
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: ModernPieChart(
              data: pieData,
              title: 'Estados de Suscripción',
            ),
          ),
        ],
      ),
    );
  }

  List<FlSpot> _generateLineChartData() {
    // Datos simulados para el gráfico de líneas
    return [
      const FlSpot(0, 3),
      const FlSpot(1, 4),
      const FlSpot(2, 6),
      const FlSpot(3, 8),
      const FlSpot(4, 7),
      const FlSpot(5, 9),
    ];
  }

  List<BarChartGroupData> _generateBarChartData() {
    final clientesFiltrados = _clientesFiltrados;

    // Contar tipos de suscripción
    final Map<String, int> tiposCounts = {};
    for (final cliente in clientesFiltrados) {
      final tipo = cliente.tipoSuscripcion ?? 'Básica';
      tiposCounts[tipo] = (tiposCounts[tipo] ?? 0) + 1;
    }

    return [
      BarChartGroupData(x: 0, barRods: [BarChartRodData(toY: (tiposCounts['Básica'] ?? 0).toDouble())]),
      BarChartGroupData(x: 1, barRods: [BarChartRodData(toY: (tiposCounts['Premium'] ?? 0).toDouble())]),
      BarChartGroupData(x: 2, barRods: [BarChartRodData(toY: (tiposCounts['Pro'] ?? 0).toDouble())]),
      BarChartGroupData(x: 3, barRods: [BarChartRodData(toY: (tiposCounts['Enterprise'] ?? 0).toDouble())]),
    ];
  }

  List<PieChartSectionData> _generatePieChartData() {
    final clientesFiltrados = _clientesFiltrados;
    final total = clientesFiltrados.length.toDouble();

    if (total == 0) {
      return [
        PieChartSectionData(
          color: Colors.grey,
          value: 100,
          title: 'Sin datos',
        ),
      ];
    }

    final activas = clientesFiltrados.where((c) => c.suscripcionActiva).length;
    final vencidas = clientesFiltrados.where((c) => c.suscripcionVencida).length;
    final porVencer = clientesFiltrados.where((c) => c.necesitaNotificacion).length;

    return [
      PieChartSectionData(
        color: const Color(0xFF4CAF50),
        value: (activas / total) * 100,
        title: 'Activas',
      ),
      PieChartSectionData(
        color: const Color(0xFFFF9800),
        value: (porVencer / total) * 100,
        title: 'Por vencer',
      ),
      PieChartSectionData(
        color: const Color(0xFFE53935),
        value: (vencidas / total) * 100,
        title: 'Vencidas',
      ),
    ];
  }
}
