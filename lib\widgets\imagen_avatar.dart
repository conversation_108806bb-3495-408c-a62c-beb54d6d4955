import 'package:flutter/material.dart';

class ImagenAvatar extends StatelessWidget {
  final String? imagenUrl;
  final String nombre;
  final double size;
  final IconData? iconoPorDefecto;
  final bool esCircular;
  final Color? colorFondo;

  const ImagenAvatar({
    super.key,
    this.imagenUrl,
    required this.nombre,
    this.size = 50,
    this.iconoPorDefecto,
    this.esCircular = true,
    this.colorFondo,
  });

  @override
  Widget build(BuildContext context) {
    // Si hay imagen URL, intentar cargarla
    if (imagenUrl != null && imagenUrl!.isNotEmpty) {
      return _buildImagenConFallback();
    }
    
    // Si no hay imagen, mostrar avatar con iniciales o icono
    return _buildAvatarPorDefecto();
  }

  Widget _buildImagenConFallback() {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: esCircular ? BoxShape.circle : BoxShape.rectangle,
        borderRadius: esCircular ? null : BorderRadius.circular(8),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 2,
        ),
      ),
      child: ClipRRect(
        borderRadius: esCircular 
          ? BorderRadius.circular(size / 2) 
          : BorderRadius.circular(6),
        child: Image.network(
          imagenUrl!,
          width: size,
          height: size,
          fit: BoxFit.cover,
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return _buildCargando();
          },
          errorBuilder: (context, error, stackTrace) {
            return _buildAvatarPorDefecto();
          },
        ),
      ),
    );
  }

  Widget _buildAvatarPorDefecto() {
    final iniciales = _obtenerIniciales(nombre);
    final colorFinal = colorFondo ?? _generarColorDesdeNombre(nombre);
    
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: colorFinal,
        shape: esCircular ? BoxShape.circle : BoxShape.rectangle,
        borderRadius: esCircular ? null : BorderRadius.circular(8),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 2,
        ),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            colorFinal,
            colorFinal.withValues(alpha: 0.8),
          ],
        ),
      ),
      child: iconoPorDefecto != null
          ? Icon(
              iconoPorDefecto,
              size: size * 0.5,
              color: Colors.white,
            )
          : Center(
              child: Text(
                iniciales,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: size * 0.35,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
    );
  }

  Widget _buildCargando() {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.3),
        shape: esCircular ? BoxShape.circle : BoxShape.rectangle,
        borderRadius: esCircular ? null : BorderRadius.circular(8),
      ),
      child: Center(
        child: SizedBox(
          width: size * 0.3,
          height: size * 0.3,
          child: const CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
          ),
        ),
      ),
    );
  }

  String _obtenerIniciales(String nombre) {
    if (nombre.isEmpty) return '?';
    
    final palabras = nombre.trim().split(' ');
    if (palabras.length == 1) {
      return palabras[0].substring(0, 1).toUpperCase();
    } else {
      return (palabras[0].substring(0, 1) + palabras[1].substring(0, 1))
          .toUpperCase();
    }
  }

  Color _generarColorDesdeNombre(String nombre) {
    // Generar un color consistente basado en el nombre
    final hash = nombre.hashCode;
    final colores = [
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.teal,
      Colors.indigo,
      Colors.pink,
      Colors.cyan,
      Colors.amber,
      Colors.deepOrange,
    ];
    
    return colores[hash.abs() % colores.length];
  }
}

// Widget específico para clientes
class AvatarCliente extends StatelessWidget {
  final String? imagenUrl;
  final String nombre;
  final double size;

  const AvatarCliente({
    super.key,
    this.imagenUrl,
    required this.nombre,
    this.size = 50,
  });

  @override
  Widget build(BuildContext context) {
    return ImagenAvatar(
      imagenUrl: imagenUrl,
      nombre: nombre,
      size: size,
      iconoPorDefecto: Icons.person,
      esCircular: true,
    );
  }
}

// Widget específico para módulos
class AvatarModulo extends StatelessWidget {
  final String? imagenUrl;
  final String nombre;
  final double size;

  const AvatarModulo({
    super.key,
    this.imagenUrl,
    required this.nombre,
    this.size = 50,
  });

  @override
  Widget build(BuildContext context) {
    return ImagenAvatar(
      imagenUrl: imagenUrl,
      nombre: nombre,
      size: size,
      iconoPorDefecto: Icons.extension,
      esCircular: false,
      colorFondo: Colors.indigo.withValues(alpha: 0.8),
    );
  }
}
