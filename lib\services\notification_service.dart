import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:developer';
import '../models/cliente_model.dart';
import '../utils/error_handler.dart';

/// Servicio para manejar notificaciones push y locales
class NotificationService {
  static final FirebaseMessaging _firebaseMessaging =
      FirebaseMessaging.instance;
  static final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();

  static bool _initialized = false;

  /// Inicializar el servicio de notificaciones
  static Future<void> initialize() async {
    if (_initialized) return;

    try {
      // Configurar notificaciones locales
      await _initializeLocalNotifications();

      // Configurar Firebase Messaging
      await _initializeFirebaseMessaging();

      _initialized = true;
      log('✅ Servicio de notificaciones inicializado');
    } catch (e) {
      ErrorHandler.logError('NotificationService.initialize', e);
    }
  }

  /// Configurar notificaciones locales
  static Future<void> _initializeLocalNotifications() async {
    const androidSettings =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );
  }

  /// Configurar Firebase Messaging
  static Future<void> _initializeFirebaseMessaging() async {
    // Solicitar permisos
    NotificationSettings settings = await _firebaseMessaging.requestPermission(
      alert: true,
      badge: true,
      sound: true,
      provisional: false,
    );

    if (settings.authorizationStatus == AuthorizationStatus.authorized) {
      log('✅ Permisos de notificación concedidos');

      // Obtener token FCM
      String? token = await _firebaseMessaging.getToken();
      if (token != null) {
        log('📱 Token FCM: $token');
        // Aquí podrías guardar el token en Firebase para enviar notificaciones desde el servidor
      }

      // Configurar handlers
      FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
      FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);
    } else {
      log('❌ Permisos de notificación denegados');
    }
  }

  /// Manejar notificaciones cuando la app está en primer plano
  static void _handleForegroundMessage(RemoteMessage message) {
    log('📨 Notificación recibida en primer plano: ${message.notification?.title}');

    if (message.notification != null) {
      _showLocalNotification(
        title: message.notification!.title ?? 'Notificación',
        body: message.notification!.body ?? '',
        payload: message.data.toString(),
      );
    }
  }

  /// Manejar tap en notificación
  static void _handleNotificationTap(RemoteMessage message) {
    log('👆 Notificación tocada: ${message.notification?.title}');
    // Aquí puedes navegar a una pantalla específica
  }

  /// Manejar tap en notificación local
  static void _onNotificationTapped(NotificationResponse response) {
    log('👆 Notificación local tocada: ${response.payload}');
    // Aquí puedes navegar a la pantalla de reportes
  }

  /// Mostrar notificación local
  static Future<void> _showLocalNotification({
    required String title,
    required String body,
    String? payload,
  }) async {
    const androidDetails = AndroidNotificationDetails(
      'suscripciones_channel',
      'Notificaciones de Suscripciones',
      channelDescription: 'Notificaciones sobre vencimientos de suscripciones',
      importance: Importance.high,
      priority: Priority.high,
      icon: '@mipmap/ic_launcher',
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.show(
      DateTime.now().millisecondsSinceEpoch ~/ 1000,
      title,
      body,
      details,
      payload: payload,
    );
  }

  /// Verificar suscripciones y enviar notificaciones
  static Future<void> verificarSuscripcionesYNotificar() async {
    try {
      log('🔍 Verificando suscripciones para notificaciones...');

      // Obtener todos los clientes
      final snapshot =
          await FirebaseFirestore.instance.collection('clientes').get();

      int notificacionesEnviadas = 0;

      for (var doc in snapshot.docs) {
        try {
          final cliente = Cliente.fromMap(doc.data());

          if (cliente.necesitaNotificacion) {
            await _enviarNotificacionVencimiento(cliente);
            notificacionesEnviadas++;
          }
        } catch (e) {
          ErrorHandler.logError('verificarSuscripcionesYNotificar.cliente', e);
        }
      }

      log('📨 Notificaciones enviadas: $notificacionesEnviadas');
    } catch (e) {
      ErrorHandler.logError('verificarSuscripcionesYNotificar', e);
    }
  }

  /// Enviar notificación de vencimiento
  static Future<void> _enviarNotificacionVencimiento(Cliente cliente) async {
    final diasRestantes = cliente.diasParaVencer;
    String titulo;
    String mensaje;

    if (diasRestantes == 0) {
      titulo = '⚠️ Suscripción vence HOY';
      mensaje = 'La suscripción de ${cliente.nombre} vence hoy';
    } else if (diasRestantes == 1) {
      titulo = '⚠️ Suscripción vence MAÑANA';
      mensaje = 'La suscripción de ${cliente.nombre} vence mañana';
    } else {
      titulo = '⚠️ Suscripción por vencer';
      mensaje =
          'La suscripción de ${cliente.nombre} vence en $diasRestantes días';
    }

    await _showLocalNotification(
      title: titulo,
      body: mensaje,
      payload: 'cliente_${cliente.idCliente}',
    );

    log('📨 Notificación enviada para ${cliente.nombre} ($diasRestantes días)');
  }

  /// Programar verificación diaria
  static Future<void> programarVerificacionDiaria() async {
    // Esta función se puede llamar desde un cron job o background task
    // Por ahora, la llamaremos manualmente desde la app
    await verificarSuscripcionesYNotificar();
  }

  /// Mostrar notificación de prueba
  static Future<void> mostrarNotificacionPrueba() async {
    await _showLocalNotification(
      title: '🧪 Notificación de Prueba',
      body: 'El sistema de notificaciones está funcionando correctamente',
      payload: 'test',
    );
  }
}
