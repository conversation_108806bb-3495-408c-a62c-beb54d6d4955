# 🔑 Solución: API Key No Válida

## 🚨 Error Actual
```
An internal error has occurred[API key not valid. please pass a valid api key
```

## 📋 Información de tu Proyecto Firebase
- **Project ID:** `m-shop-58899`
- **Project Number:** `741892752866`
- **API Key:** `AIzaSyDWIW0ASD6GGocBsjYs_O1c245iH04yTTo`
- **Package Name:** `com.example.shop_3m`

## 🛠️ Solución Paso a Paso

### 1. Habilitar APIs en Google Cloud Console

Ve a [Google Cloud Console](https://console.cloud.google.com/) y sigue estos pasos:

#### A. Seleccionar el Proyecto
1. Ve a https://console.cloud.google.com/
2. En la parte superior, selecciona el proyecto `m-shop-58899`
3. Si no aparece, haz clic en el dropdown y búscalo

#### B. Habilitar APIs Necesarias
Ve a **APIs & Services > Library** y habilita estas APIs:

1. **Firebase Authentication API**
   - Busca: "Firebase Authentication API"
   - Haz clic en "ENABLE"

2. **Cloud Firestore API**
   - Busca: "Cloud Firestore API"
   - Haz clic en "ENABLE"

3. **Firebase Storage API**
   - Busca: "Firebase Storage API"
   - Haz clic en "ENABLE"

4. **Firebase Management API**
   - Busca: "Firebase Management API"
   - Haz clic en "ENABLE"

5. **Google Analytics Reporting API** (opcional)
   - Busca: "Google Analytics Reporting API"
   - Haz clic en "ENABLE"

### 2. Verificar Restricciones de API Key

#### A. Ir a Credenciales
1. Ve a **APIs & Services > Credentials**
2. Busca la API Key: `AIzaSyDWIW0ASD6GGocBsjYs_O1c245iH04yTTo`
3. Haz clic en el ícono de editar (lápiz)

#### B. Configurar Restricciones
1. **Application restrictions:**
   - Selecciona "Android apps"
   - Agrega:
     - Package name: `com.example.shop_3m`
     - SHA-1: `35402eaf8d708ab99ca405bfa31f09d725b03415` (debug)
     - SHA-1: `c995d9210102f34357268a12ce6968e4c7eb99a8` (release)

2. **API restrictions:**
   - Selecciona "Restrict key"
   - Habilita estas APIs:
     - Firebase Authentication API
     - Cloud Firestore API
     - Firebase Storage API
     - Firebase Management API

3. Haz clic en **SAVE**

### 3. Verificar Configuración en Firebase Console

Ve a [Firebase Console](https://console.firebase.google.com/):

#### A. Verificar Proyecto
1. Selecciona el proyecto `m-shop-58899`
2. Ve a **Project Settings** (⚙️)

#### B. Verificar App Android
1. En "Your apps", verifica que existe la app Android
2. Package name debe ser: `com.example.shop_3m`
3. Verifica que los SHA-1 estén agregados:
   - `35402eaf8d708ab99ca405bfa31f09d725b03415`
   - `c995d9210102f34357268a12ce6968e4c7eb99a8`

#### C. Habilitar Servicios
1. **Authentication:**
   - Ve a Authentication > Sign-in method
   - Habilita "Email/Password"

2. **Firestore:**
   - Ve a Firestore Database
   - Si no existe, crea la base de datos en modo "test"

3. **Storage:**
   - Ve a Storage
   - Si no existe, configúralo

### 4. Limpiar y Recompilar

Ejecuta estos comandos:

```bash
flutter clean
flutter pub get
flutter run -d android
```

### 5. Probar la Conexión

1. Ejecuta la app en Android
2. Ve a "Firebase Test"
3. Haz clic en "Ejecutar Pruebas de Firebase"
4. Verifica que todas las pruebas pasen

## 🔗 Enlaces Directos

### Google Cloud Console
- **Proyecto:** https://console.cloud.google.com/home/<USER>
- **APIs:** https://console.cloud.google.com/apis/library?project=m-shop-58899
- **Credenciales:** https://console.cloud.google.com/apis/credentials?project=m-shop-58899

### Firebase Console
- **Proyecto:** https://console.firebase.google.com/project/m-shop-58899
- **Settings:** https://console.firebase.google.com/project/m-shop-58899/settings/general
- **Authentication:** https://console.firebase.google.com/project/m-shop-58899/authentication
- **Firestore:** https://console.firebase.google.com/project/m-shop-58899/firestore

## ⚡ Solución Rápida

Si tienes prisa, ejecuta estos pasos mínimos:

1. Ve a https://console.cloud.google.com/apis/library?project=m-shop-58899
2. Busca y habilita: "Firebase Authentication API"
3. Busca y habilita: "Cloud Firestore API"
4. Ejecuta: `flutter clean && flutter run -d android`

## 🆘 Si Sigue Sin Funcionar

1. **Regenerar API Key:**
   - Ve a Google Cloud Console > Credentials
   - Crea una nueva API Key
   - Actualiza `google-services.json`

2. **Recrear App en Firebase:**
   - Elimina la app Android en Firebase Console
   - Crea una nueva con el mismo package name
   - Descarga nuevo `google-services.json`

3. **Verificar Logs:**
   ```bash
   flutter logs
   ```
   Busca errores específicos de Firebase

## 📞 Información de Contacto

Si necesitas ayuda adicional, proporciona:
- Logs completos de `flutter logs`
- Screenshots de errores
- Confirmación de qué APIs están habilitadas
