// Tests unitarios para el modelo DetalleFactura

import 'package:flutter_test/flutter_test.dart';
import 'package:shop_3m/models/detalle_factura_model.dart';

void main() {
  group('DetalleFactura Model Tests', () {
    group('Constructor Tests', () {
      test('should create a valid DetalleFactura with correct data', () {
        // Arrange & Act
        final detalle = DetalleFactura(
          idDetalle: '123',
          idFactura: '456',
          cantidad: 2,
          modulo: 'Módulo Test',
          suscripcion: true,
          precioModulo: 100.0,
        );

        // Assert
        expect(detalle.idDetalle, '123');
        expect(detalle.idFactura, '456');
        expect(detalle.cantidad, 2);
        expect(detalle.modulo, 'Módulo Test');
        expect(detalle.suscripcion, isTrue);
        expect(detalle.precioModulo, 100.0);
      });

      test('should calculate total correctly', () {
        // Arrange
        final detalle = DetalleFactura(
          idDetalle: '123',
          idFactura: '456',
          cantidad: 3,
          modulo: 'Módulo Test',
          suscripcion: true,
          precioModulo: 50.0,
        );

        // Act & Assert
        expect(detalle.total, 150.0);
      });
    });

    group('Serialization Tests', () {
      test('toMap should return correct Map with integer suscripcion', () {
        // Arrange
        final detalle = DetalleFactura(
          idDetalle: '123',
          idFactura: '456',
          cantidad: 2,
          modulo: 'Módulo Test',
          suscripcion: true,
          precioModulo: 100.0,
        );

        // Act
        final map = detalle.toMap();

        // Assert
        expect(map['IdDetalle'], '123');
        expect(map['IdFactura'], '456');
        expect(map['Cantidad'], 2);
        expect(map['Modulo'], 'Módulo Test');
        expect(map['Suscripcion'], 1); // Should be integer 1 for true
        expect(map['PrecioModulo'], 100.0);
      });

      test('toMap should convert false suscripcion to 0', () {
        // Arrange
        final detalle = DetalleFactura(
          idDetalle: '123',
          idFactura: '456',
          cantidad: 1,
          modulo: 'Módulo Test',
          suscripcion: false,
          precioModulo: 50.0,
        );

        // Act
        final map = detalle.toMap();

        // Assert
        expect(map['Suscripcion'], 0); // Should be integer 0 for false
      });

      test('fromMap should create correct DetalleFactura from integer suscripcion', () {
        // Arrange
        final map = {
          'IdDetalle': '123',
          'IdFactura': '456',
          'Cantidad': 2,
          'Modulo': 'Módulo Test',
          'Suscripcion': 1, // Integer 1 for true
          'PrecioModulo': 100.0,
        };

        // Act
        final detalle = DetalleFactura.fromMap(map);

        // Assert
        expect(detalle.idDetalle, '123');
        expect(detalle.idFactura, '456');
        expect(detalle.cantidad, 2);
        expect(detalle.modulo, 'Módulo Test');
        expect(detalle.suscripcion, isTrue);
        expect(detalle.precioModulo, 100.0);
      });

      test('fromMap should handle 0 as false for suscripcion', () {
        // Arrange
        final map = {
          'IdDetalle': '123',
          'IdFactura': '456',
          'Cantidad': 1,
          'Modulo': 'Módulo Test',
          'Suscripcion': 0, // Integer 0 for false
          'PrecioModulo': 50.0,
        };

        // Act
        final detalle = DetalleFactura.fromMap(map);

        // Assert
        expect(detalle.suscripcion, isFalse);
      });

      test('fromMap should handle string "true" for suscripcion', () {
        // Arrange
        final map = {
          'IdDetalle': '123',
          'IdFactura': '456',
          'Cantidad': 1,
          'Modulo': 'Módulo Test',
          'Suscripcion': 'true', // String "true"
          'PrecioModulo': 50.0,
        };

        // Act
        final detalle = DetalleFactura.fromMap(map);

        // Assert
        expect(detalle.suscripcion, isTrue);
      });

      test('fromMap should handle boolean true for suscripcion', () {
        // Arrange
        final map = {
          'IdDetalle': '123',
          'IdFactura': '456',
          'Cantidad': 1,
          'Modulo': 'Módulo Test',
          'Suscripcion': true, // Boolean true
          'PrecioModulo': 50.0,
        };

        // Act
        final detalle = DetalleFactura.fromMap(map);

        // Assert
        expect(detalle.suscripcion, isTrue);
      });

      test('fromMap should handle missing values with defaults', () {
        // Arrange
        final map = <String, dynamic>{};

        // Act
        final detalle = DetalleFactura.fromMap(map);

        // Assert
        expect(detalle.idDetalle, '');
        expect(detalle.idFactura, '');
        expect(detalle.cantidad, 0);
        expect(detalle.modulo, '');
        expect(detalle.suscripcion, isFalse);
        expect(detalle.precioModulo, 0.0);
      });

      test('fromMap should handle integer PrecioModulo', () {
        // Arrange
        final map = {
          'IdDetalle': '123',
          'IdFactura': '456',
          'Cantidad': 1,
          'Modulo': 'Módulo Test',
          'Suscripcion': 1,
          'PrecioModulo': 100, // Integer instead of double
        };

        // Act
        final detalle = DetalleFactura.fromMap(map);

        // Assert
        expect(detalle.precioModulo, 100.0);
      });
    });

    group('Utility Methods Tests', () {
      test('copyWith should create new instance with updated values', () {
        // Arrange
        final original = DetalleFactura(
          idDetalle: '123',
          idFactura: '456',
          cantidad: 2,
          modulo: 'Módulo Original',
          suscripcion: true,
          precioModulo: 100.0,
        );

        // Act
        final updated = original.copyWith(
          modulo: 'Módulo Actualizado',
          cantidad: 3,
        );

        // Assert
        expect(updated.idDetalle, original.idDetalle);
        expect(updated.idFactura, original.idFactura);
        expect(updated.cantidad, 3);
        expect(updated.modulo, 'Módulo Actualizado');
        expect(updated.suscripcion, original.suscripcion);
        expect(updated.precioModulo, original.precioModulo);
      });

      test('copyWith should keep original values when no parameters provided', () {
        // Arrange
        final original = DetalleFactura(
          idDetalle: '123',
          idFactura: '456',
          cantidad: 2,
          modulo: 'Módulo Test',
          suscripcion: true,
          precioModulo: 100.0,
        );

        // Act
        final copy = original.copyWith();

        // Assert
        expect(copy.idDetalle, original.idDetalle);
        expect(copy.idFactura, original.idFactura);
        expect(copy.cantidad, original.cantidad);
        expect(copy.modulo, original.modulo);
        expect(copy.suscripcion, original.suscripcion);
        expect(copy.precioModulo, original.precioModulo);
      });

      test('toString should return formatted string', () {
        // Arrange
        final detalle = DetalleFactura(
          idDetalle: '123',
          idFactura: '456',
          cantidad: 2,
          modulo: 'Módulo Test',
          suscripcion: true,
          precioModulo: 100.0,
        );

        // Act
        final string = detalle.toString();

        // Assert
        expect(string, contains('DetalleFactura'));
        expect(string, contains('123'));
        expect(string, contains('456'));
        expect(string, contains('Módulo Test'));
        expect(string, contains('200.0')); // total
      });
    });

    group('Business Logic Tests', () {
      test('total should be calculated correctly for different scenarios', () {
        // Test case 1: Normal calculation
        final detalle1 = DetalleFactura(
          idDetalle: '1',
          idFactura: '1',
          cantidad: 5,
          modulo: 'Test',
          suscripcion: true,
          precioModulo: 20.0,
        );
        expect(detalle1.total, 100.0);

        // Test case 2: Zero quantity
        final detalle2 = DetalleFactura(
          idDetalle: '2',
          idFactura: '2',
          cantidad: 0,
          modulo: 'Test',
          suscripcion: true,
          precioModulo: 50.0,
        );
        expect(detalle2.total, 0.0);

        // Test case 3: Decimal price
        final detalle3 = DetalleFactura(
          idDetalle: '3',
          idFactura: '3',
          cantidad: 3,
          modulo: 'Test',
          suscripcion: true,
          precioModulo: 33.33,
        );
        expect(detalle3.total, closeTo(99.99, 0.01));
      });
    });
  });
}
