import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../widgets/gradient_background.dart';
import '../widgets/imagen_avatar.dart';
import '../widgets/advanced_visual_effects.dart';
import '../services/cliente_service.dart';
import '../models/cliente_model.dart';
import '../routes/app_routes.dart';

class ClientesScreen extends StatefulWidget {
  const ClientesScreen({super.key});

  @override
  State<ClientesScreen> createState() => _ClientesScreenState();
}

enum ClientesViewMode { list, grid, cards }

class _ClientesScreenState extends State<ClientesScreen>
    with TickerProviderStateMixin {
  ClientesViewMode _viewMode = ClientesViewMode.cards;
  String _sortBy = 'nombre';
  bool _sortAscending = true;
  String _filterStatus = 'todos';
  late AnimationController _fabController;
  late AnimationController _headerController;
  late Animation<double> _fabAnimation;
  late Animation<Offset> _headerAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _fabController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _headerController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fabAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fabController, curve: Curves.elasticOut),
    );

    _headerAnimation = Tween<Offset>(
      begin: const Offset(0, -1),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _headerController, curve: Curves.easeOutCubic));

    // Iniciar animaciones
    _headerController.forward();
    _fabController.forward();
  }

  @override
  void dispose() {
    _fabController.dispose();
    _headerController.dispose();
    super.dispose();
  }

  Future<List<Cliente>> _cargarClientesPaginados({
    int limit = 20,
    int offset = 0,
    String? searchQuery,
  }) async {
    final clientesData = await ClienteService.obtenerClientesPaginados(
      limit: limit,
      offset: offset,
      searchQuery: searchQuery,
    );

    return clientesData
        .map((data) => Cliente(
              idCliente: data['IdCliente'],
              nombre: data['Nombre'],
              telefono: data['Telefono'],
              email: data['Email'],
            ))
        .toList();
  }

  Future<int> _contarClientes({String? searchQuery}) {
    return ClienteService.contarClientes(searchQuery: searchQuery);
  }

  void _eliminarCliente(String idCliente) async {
    final confirmacion = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text("Eliminar Cliente"),
        content: const Text("¿Seguro que deseas eliminar este cliente?"),
        actions: [
          TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text("Cancelar")),
          TextButton(
              onPressed: () => Navigator.pop(context, true),
              child: const Text("Eliminar")),
        ],
      ),
    );

    if (confirmacion == true) {
      await ClienteService.eliminarCliente(idCliente);
      // Forzar recarga de la lista paginada
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GradientBackground(
        child: NestedScrollView(
          headerSliverBuilder: (context, innerBoxIsScrolled) {
            return [
              _buildAnimatedAppBar(innerBoxIsScrolled),
              _buildFilterHeader(),
            ];
          },
          body: _buildClientesList(),
        ),
      ),
      floatingActionButton: _buildAnimatedFAB(),
    );
  }

  Widget _buildAnimatedAppBar(bool innerBoxIsScrolled) {
    return SliverAppBar(
      expandedHeight: 120,
      floating: false,
      pinned: true,
      backgroundColor: Theme.of(context).colorScheme.primary,
      foregroundColor: Colors.white,
      flexibleSpace: FlexibleSpaceBar(
        title: SlideTransition(
          position: _headerAnimation,
          child: const Text(
            'Clientes',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 24,
            ),
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Theme.of(context).colorScheme.primary,
                Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
              ],
            ),
          ),
          child: FloatingParticles(
            particleCount: 15,
            particleColor: Colors.white.withValues(alpha: 0.3),
            child: const SizedBox.expand(),
          ),
        ),
      ),
      actions: [
        _buildViewModeToggle(),
        _buildSortButton(),
        const SizedBox(width: 8),
      ],
    );
  }

  Widget _buildViewModeToggle() {
    return PopupMenuButton<ClientesViewMode>(
      icon: Icon(_getViewModeIcon()),
      onSelected: (mode) {
        setState(() => _viewMode = mode);
        HapticFeedback.selectionClick();
      },
      itemBuilder: (context) => [
        PopupMenuItem(
          value: ClientesViewMode.list,
          child: Row(
            children: [
              Icon(Icons.list, color: _viewMode == ClientesViewMode.list ? Colors.blue : null),
              const SizedBox(width: 8),
              const Text('Lista'),
            ],
          ),
        ),
        PopupMenuItem(
          value: ClientesViewMode.grid,
          child: Row(
            children: [
              Icon(Icons.grid_view, color: _viewMode == ClientesViewMode.grid ? Colors.blue : null),
              const SizedBox(width: 8),
              const Text('Cuadrícula'),
            ],
          ),
        ),
        PopupMenuItem(
          value: ClientesViewMode.cards,
          child: Row(
            children: [
              Icon(Icons.view_agenda, color: _viewMode == ClientesViewMode.cards ? Colors.blue : null),
              const SizedBox(width: 8),
              const Text('Tarjetas'),
            ],
          ),
        ),
      ],
    );
  }

  IconData _getViewModeIcon() {
    switch (_viewMode) {
      case ClientesViewMode.list:
        return Icons.list;
      case ClientesViewMode.grid:
        return Icons.grid_view;
      case ClientesViewMode.cards:
        return Icons.view_agenda;
    }
  }

  Widget _buildSortButton() {
    return PopupMenuButton<String>(
      icon: const Icon(Icons.sort),
      onSelected: (sortBy) {
        setState(() {
          if (_sortBy == sortBy) {
            _sortAscending = !_sortAscending;
          } else {
            _sortBy = sortBy;
            _sortAscending = true;
          }
        });
        HapticFeedback.selectionClick();
      },
      itemBuilder: (context) => [
        PopupMenuItem(
          value: 'nombre',
          child: Row(
            children: [
              Icon(Icons.person, color: _sortBy == 'nombre' ? Colors.blue : null),
              const SizedBox(width: 8),
              const Text('Nombre'),
              if (_sortBy == 'nombre') ...[
                const Spacer(),
                Icon(_sortAscending ? Icons.arrow_upward : Icons.arrow_downward, size: 16),
              ],
            ],
          ),
        ),
        PopupMenuItem(
          value: 'email',
          child: Row(
            children: [
              Icon(Icons.email, color: _sortBy == 'email' ? Colors.blue : null),
              const SizedBox(width: 8),
              const Text('Email'),
              if (_sortBy == 'email') ...[
                const Spacer(),
                Icon(_sortAscending ? Icons.arrow_upward : Icons.arrow_downward, size: 16),
              ],
            ],
          ),
        ),
        PopupMenuItem(
          value: 'suscripcion',
          child: Row(
            children: [
              Icon(Icons.subscriptions, color: _sortBy == 'suscripcion' ? Colors.blue : null),
              const SizedBox(width: 8),
              const Text('Suscripción'),
              if (_sortBy == 'suscripcion') ...[
                const Spacer(),
                Icon(_sortAscending ? Icons.arrow_upward : Icons.arrow_downward, size: 16),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildFilterHeader() {
    return SliverToBoxAdapter(
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Expanded(child: _buildSearchBar()),
            const SizedBox(width: 12),
            _buildFilterChips(),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    return GlassmorphicContainer(
      width: double.infinity,
      height: 50,
      blur: 10,
      opacity: 0.1,
      borderRadius: BorderRadius.circular(25),
      child: TextField(
        decoration: InputDecoration(
          hintText: 'Buscar clientes...',
          prefixIcon: const Icon(Icons.search, color: Colors.grey),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 15),
        ),
        onChanged: (value) {
          // Implementar búsqueda en tiempo real
          setState(() {});
        },
      ),
    );
  }

  Widget _buildFilterChips() {
    return PopupMenuButton<String>(
      icon: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(20),
        ),
        child: const Icon(Icons.filter_list, color: Colors.white),
      ),
      onSelected: (filter) {
        setState(() => _filterStatus = filter);
        HapticFeedback.selectionClick();
      },
      itemBuilder: (context) => [
        PopupMenuItem(
          value: 'todos',
          child: Row(
            children: [
              Icon(Icons.all_inclusive, color: _filterStatus == 'todos' ? Colors.blue : null),
              const SizedBox(width: 8),
              const Text('Todos'),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'activos',
          child: Row(
            children: [
              Icon(Icons.check_circle, color: _filterStatus == 'activos' ? Colors.green : null),
              const SizedBox(width: 8),
              const Text('Activos'),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'inactivos',
          child: Row(
            children: [
              Icon(Icons.cancel, color: _filterStatus == 'inactivos' ? Colors.red : null),
              const SizedBox(width: 8),
              const Text('Inactivos'),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'vencidos',
          child: Row(
            children: [
              Icon(Icons.warning, color: _filterStatus == 'vencidos' ? Colors.orange : null),
              const SizedBox(width: 8),
              const Text('Vencidos'),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildClientesList() {
    return FutureBuilder<List<Cliente>>(
      future: _cargarClientesPaginados(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error, size: 64, color: Colors.red),
                const SizedBox(height: 16),
                Text('Error: ${snapshot.error}'),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => setState(() {}),
                  child: const Text('Reintentar'),
                ),
              ],
            ),
          );
        }

        final clientes = snapshot.data ?? [];

        if (clientes.isEmpty) {
          return _buildEmptyState();
        }

        return _buildClientesView(clientes);
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          BreathingWidget(
            child: Icon(
              Icons.people_outline,
              size: 120,
              color: Colors.grey.shade400,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'No hay clientes registrados',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Agrega tu primer cliente para comenzar',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade500,
            ),
          ),
          const SizedBox(height: 32),
          ElevatedButton.icon(
            onPressed: () async {
              await Navigator.pushNamed(context, AppRoutes.agregarCliente);
              setState(() {});
            },
            icon: const Icon(Icons.add),
            label: const Text('Agregar Cliente'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildClientesView(List<Cliente> clientes) {
    switch (_viewMode) {
      case ClientesViewMode.list:
        return _buildListView(clientes);
      case ClientesViewMode.grid:
        return _buildGridView(clientes);
      case ClientesViewMode.cards:
        return _buildCardsView(clientes);
    }
  }

  Widget _buildListView(List<Cliente> clientes) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: clientes.length,
      itemBuilder: (context, index) {
        final cliente = clientes[index];
        return TweenAnimationBuilder<double>(
          duration: Duration(milliseconds: 300 + (index * 100)),
          tween: Tween(begin: 0.0, end: 1.0),
          builder: (context, value, child) {
            return Transform.translate(
              offset: Offset(0, 50 * (1 - value)),
              child: Opacity(
                opacity: value,
                child: _buildClienteListTile(cliente),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildGridView(List<Cliente> clientes) {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.8,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: clientes.length,
      itemBuilder: (context, index) {
        final cliente = clientes[index];
        return TweenAnimationBuilder<double>(
          duration: Duration(milliseconds: 300 + (index * 100)),
          tween: Tween(begin: 0.0, end: 1.0),
          builder: (context, value, child) {
            return Transform.scale(
              scale: value,
              child: _buildClienteGridCard(cliente),
            );
          },
        );
      },
    );
  }

  Widget _buildCardsView(List<Cliente> clientes) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: clientes.length,
      itemBuilder: (context, index) {
        final cliente = clientes[index];
        return TweenAnimationBuilder<double>(
          duration: Duration(milliseconds: 300 + (index * 100)),
          tween: Tween(begin: 0.0, end: 1.0),
          builder: (context, value, child) {
            return Transform.translate(
              offset: Offset(100 * (1 - value), 0),
              child: Opacity(
                opacity: value,
                child: _buildClienteCard(cliente),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildAnimatedFAB() {
    return ScaleTransition(
      scale: _fabAnimation,
      child: FloatingActionButton.extended(
        onPressed: () async {
          await Navigator.pushNamed(context, AppRoutes.agregarCliente);
          setState(() {});
        },
        icon: const Icon(Icons.add),
        label: const Text('Nuevo Cliente'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: 8,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
    );
  }

  Widget _buildClienteListTile(Cliente cliente) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () => _verDetalleCliente(cliente),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                AvatarCliente(
                  imagenUrl: cliente.imagenUrl,
                  nombre: cliente.nombre,
                  size: 50,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        cliente.nombre,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        cliente.email,
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 14,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    _buildStatusChip(cliente),
                    const SizedBox(height: 4),
                    Text(
                      cliente.tipoSuscripcion ?? 'Sin suscripción',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade500,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildClienteGridCard(Cliente cliente) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () => _verDetalleCliente(cliente),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                AvatarCliente(
                  imagenUrl: cliente.imagenUrl,
                  nombre: cliente.nombre,
                  size: 60,
                ),
                const SizedBox(height: 12),
                Text(
                  cliente.nombre,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
                _buildStatusChip(cliente),
                const Spacer(),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    IconButton(
                      icon: const Icon(Icons.edit, size: 20),
                      onPressed: () => _editarCliente(cliente),
                      color: Colors.blue,
                    ),
                    IconButton(
                      icon: const Icon(Icons.delete, size: 20),
                      onPressed: () => _eliminarCliente(cliente.idCliente),
                      color: Colors.red,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildClienteCard(Cliente cliente) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: () => _verDetalleCliente(cliente),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    AvatarCliente(
                      imagenUrl: cliente.imagenUrl,
                      nombre: cliente.nombre,
                      size: 60,
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            cliente.nombre,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 18,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            cliente.email,
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 14,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                    _buildStatusChip(cliente),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Icon(Icons.phone, color: Colors.grey.shade600, size: 16),
                    const SizedBox(width: 8),
                    Text(
                      cliente.telefono,
                      style: TextStyle(color: Colors.grey.shade600, fontSize: 14),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(Icons.subscriptions, color: Colors.grey.shade600, size: 16),
                    const SizedBox(width: 8),
                    Text(
                      cliente.tipoSuscripcion ?? 'Sin suscripción',
                      style: TextStyle(color: Colors.grey.shade600, fontSize: 14),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    ElevatedButton.icon(
                      onPressed: () => _editarCliente(cliente),
                      icon: const Icon(Icons.edit, size: 16),
                      label: const Text('Editar'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                        minimumSize: const Size(80, 35),
                      ),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton.icon(
                      onPressed: () => _eliminarCliente(cliente.idCliente),
                      icon: const Icon(Icons.delete, size: 16),
                      label: const Text('Eliminar'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                        minimumSize: const Size(80, 35),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Métodos auxiliares
  Color _getStatusColor(Cliente cliente) {
    if (cliente.estadoSuscripcion == null) return Colors.grey;

    switch (cliente.estadoSuscripcion!.toLowerCase()) {
      case 'activa':
        return Colors.green;
      case 'vencida':
        return Colors.red;
      case 'suspendida':
        return Colors.orange;
      case 'cancelada':
        return Colors.grey;
      default:
        return Colors.blue;
    }
  }

  Widget _buildStatusChip(Cliente cliente) {
    final color = _getStatusColor(cliente);
    final status = cliente.estadoSuscripcion ?? 'Sin estado';

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        status,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  void _verDetalleCliente(Cliente cliente) {
    Navigator.pushNamed(
      context,
      AppRoutes.editarCliente,
      arguments: cliente,
    );
  }

  void _editarCliente(Cliente cliente) {
    Navigator.pushNamed(
      context,
      AppRoutes.editarCliente,
      arguments: cliente,
    ).then((_) => setState(() {}));
  }
}
