import 'package:flutter/material.dart';
import '../widgets/gradient_background.dart';
import '../widgets/paginated_list_builder.dart';
import '../widgets/imagen_avatar.dart';
import '../services/cliente_service.dart';
import '../models/cliente_model.dart';
import '../routes/app_routes.dart';

class ClientesScreen extends StatefulWidget {
  const ClientesScreen({super.key});

  @override
  State<ClientesScreen> createState() => _ClientesScreenState();
}

class _ClientesScreenState extends State<ClientesScreen> {
  Future<List<Cliente>> _cargarClientesPaginados({
    int limit = 20,
    int offset = 0,
    String? searchQuery,
  }) async {
    final clientesData = await ClienteService.obtenerClientesPaginados(
      limit: limit,
      offset: offset,
      searchQuery: searchQuery,
    );

    return clientesData
        .map((data) => Cliente(
              idCliente: data['IdCliente'],
              nombre: data['Nombre'],
              telefono: data['Telefono'],
              email: data['Email'],
            ))
        .toList();
  }

  Future<int> _contarClientes({String? searchQuery}) {
    return ClienteService.contarClientes(searchQuery: searchQuery);
  }

  void _eliminarCliente(String idCliente) async {
    final confirmacion = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text("Eliminar Cliente"),
        content: const Text("¿Seguro que deseas eliminar este cliente?"),
        actions: [
          TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text("Cancelar")),
          TextButton(
              onPressed: () => Navigator.pop(context, true),
              child: const Text("Eliminar")),
        ],
      ),
    );

    if (confirmacion == true) {
      await ClienteService.eliminarCliente(idCliente);
      // Forzar recarga de la lista paginada
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text("Gestión de Clientes")),
      body: GradientBackground(
        child: PaginatedListBuilder<Cliente>(
          dataLoader: _cargarClientesPaginados,
          countLoader: _contarClientes,
          searchHint: "Buscar clientes por nombre, email o teléfono...",
          emptyMessage: "No hay clientes registrados",
          itemBuilder: (context, cliente, index) {
            return Card(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              color: Colors.white.withAlpha(242),
              elevation: 6,
              margin: const EdgeInsets.symmetric(vertical: 8),
              child: ListTile(
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                title: Text(
                  cliente.nombre,
                  style: Theme.of(context)
                      .textTheme
                      .titleMedium
                      ?.copyWith(fontWeight: FontWeight.bold),
                ),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 4),
                    Text(
                      cliente.email,
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                    Text(
                      cliente.telefono,
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
                leading: AvatarCliente(
                  imagenUrl: cliente.imagenUrl,
                  nombre: cliente.nombre,
                  size: 50,
                ),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: const Icon(Icons.edit, color: Colors.indigo),
                      onPressed: () async {
                        await context.pushNamed(
                          AppRoutes.editarCliente,
                          arguments: cliente,
                        );
                        setState(() {}); // Refrescar la lista
                      },
                    ),
                    IconButton(
                      icon: const Icon(Icons.delete, color: Colors.red),
                      onPressed: () => _eliminarCliente(cliente.idCliente),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          await context.pushNamed(AppRoutes.agregarCliente);
          setState(() {}); // Refrescar la lista
        },
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        backgroundColor: Theme.of(context).colorScheme.primary,
        elevation: 6,
        child: const Icon(Icons.add, size: 30),
      ),
    );
  }
}
