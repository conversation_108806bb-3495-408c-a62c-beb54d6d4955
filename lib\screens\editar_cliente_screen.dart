import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../widgets/gradient_background.dart';
import '../widgets/advanced_visual_effects.dart';
import '../widgets/modern_form_fields.dart';
import '../widgets/imagen_avatar.dart';
import '../widgets/theme_toggle_button.dart';
import '../services/cliente_service.dart';
import '../models/cliente_model.dart';
import 'dart:math' as math;

class EditarClienteScreen extends StatefulWidget {
  final Cliente cliente;

  const EditarClienteScreen({super.key, required this.cliente});

  @override
  State<EditarClienteScreen> createState() => _EditarClienteScreenState();
}

class _EditarClienteScreenState extends State<EditarClienteScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController nombreController;
  late TextEditingController telefonoController;
  late TextEditingController emailController;

  // Controladores de animación
  late AnimationController _slideController;
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late AnimationController _avatarController;
  late AnimationController _successController;

  // Animaciones
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _avatarAnimation;
  late Animation<double> _successAnimation;

  // Variables para manejar errores en tiempo real
  String? nombreError;
  String? telefonoError;
  String? emailError;
  bool _isLoading = false;
  bool _showSuccess = false;

  // Focus nodes para navegación
  final FocusNode _nombreFocus = FocusNode();
  final FocusNode _telefonoFocus = FocusNode();
  final FocusNode _emailFocus = FocusNode();

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _initializeAnimations();
    _startEntryAnimations();
  }

  void _initializeControllers() {
    nombreController = TextEditingController(text: widget.cliente.nombre);
    telefonoController = TextEditingController(text: widget.cliente.telefono);
    emailController = TextEditingController(text: widget.cliente.email);
  }

  void _initializeAnimations() {
    // Controladores de animación
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _avatarController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _successController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    // Animaciones
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    _avatarAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _avatarController,
      curve: Curves.bounceOut,
    ));

    _successAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _successController,
      curve: Curves.elasticOut,
    ));
  }

  void _startEntryAnimations() {
    Future.delayed(const Duration(milliseconds: 100), () {
      _fadeController.forward();
    });

    Future.delayed(const Duration(milliseconds: 200), () {
      _scaleController.forward();
    });

    Future.delayed(const Duration(milliseconds: 300), () {
      _slideController.forward();
    });

    Future.delayed(const Duration(milliseconds: 500), () {
      _avatarController.forward();
    });
  }

  @override
  void dispose() {
    // Dispose controllers
    nombreController.dispose();
    telefonoController.dispose();
    emailController.dispose();

    // Dispose focus nodes
    _nombreFocus.dispose();
    _telefonoFocus.dispose();
    _emailFocus.dispose();

    // Dispose animation controllers
    _slideController.dispose();
    _fadeController.dispose();
    _scaleController.dispose();
    _avatarController.dispose();
    _successController.dispose();

    super.dispose();
  }

  // Métodos de validación en tiempo real
  void _validateNombre(String value) {
    setState(() {
      if (value.isEmpty) {
        nombreError = 'El nombre es requerido';
      } else if (!Cliente.isValidNombre(value)) {
        nombreError = 'Nombre debe tener al menos 2 caracteres';
      } else {
        nombreError = null;
      }
    });
  }

  void _validateTelefono(String value) {
    setState(() {
      if (value.isEmpty) {
        telefonoError = 'El teléfono es requerido';
      } else if (!Cliente.isValidTelefono(value)) {
        telefonoError = 'Formato de teléfono inválido';
      } else {
        telefonoError = null;
      }
    });
  }

  void _validateEmail(String value) {
    setState(() {
      if (value.isEmpty) {
        emailError = 'El email es requerido';
      } else if (!Cliente.isValidEmail(value)) {
        emailError = 'Formato de email inválido';
      } else {
        emailError = null;
      }
    });
  }

  Future<void> _guardarEdicion() async {
    if (!_formKey.currentState!.validate()) {
      _shakeForm();
      return;
    }

    setState(() {
      _isLoading = true;
    });

    // Animación de carga
    _scaleController.reverse().then((_) {
      _scaleController.forward();
    });

    try {
      final clienteActualizado = Cliente(
        idCliente: widget.cliente.idCliente,
        nombre: nombreController.text.trim(),
        telefono: telefonoController.text.trim(),
        email: emailController.text.trim(),
      );

      await ClienteService.actualizarCliente(clienteActualizado);

      // Animación de éxito
      await _showSuccessAnimation();

      if (!mounted) return;
      Navigator.pop(context, clienteActualizado);
    } catch (e) {
      _mostrarMensaje("Error al actualizar cliente: $e", isError: true);
      _shakeForm();
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _shakeForm() {
    HapticFeedback.heavyImpact();
    _scaleController.reverse().then((_) {
      _scaleController.forward();
    });
  }

  Future<void> _showSuccessAnimation() async {
    setState(() {
      _showSuccess = true;
    });

    HapticFeedback.mediumImpact();
    await _successController.forward();

    await Future.delayed(const Duration(milliseconds: 800));

    if (mounted) {
      setState(() {
        _showSuccess = false;
      });
    }
  }

  void _mostrarMensaje(String mensaje, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              isError ? Icons.error : Icons.check_circle,
              color: Colors.white,
            ),
            const SizedBox(width: 8),
            Expanded(child: Text(mensaje)),
          ],
        ),
        backgroundColor: isError ? Colors.red : Colors.green,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      body: GradientBackground(
        child: Stack(
          children: [
            // Partículas de fondo
            FloatingParticles(
              particleCount: 20,
              particleColor: theme.primaryColor.withValues(alpha: 0.1),
              child: const SizedBox.expand(),
            ),

            // Contenido principal
            SafeArea(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    _buildHeader(),
                    const SizedBox(height: 30),
                    _buildMainForm(),
                  ],
                ),
              ),
            ),

            // Overlay de éxito
            if (_showSuccess) _buildSuccessOverlay(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Column(
        children: [
          // Botón de regreso animado
          Row(
            children: [
              ScaleTransition(
                scale: _scaleAnimation,
                child: Container(
                  decoration: BoxDecoration(
                    color: Theme.of(context).cardColor,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [context.adaptiveShadow],
                  ),
                  child: IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.arrow_back),
                    tooltip: 'Volver',
                  ),
                ),
              ),
              const Spacer(),
              // Toggle de tema
              ThemeToggleButton(
                onThemeChanged: (theme) {
                  // Implementar cambio de tema si es necesario
                },
                currentTheme: ThemeMode.system,
              ),
            ],
          ),

          const SizedBox(height: 30),

          // Avatar del cliente con animación
          ScaleTransition(
            scale: _avatarAnimation,
            child: BreathingWidget(
              child: GlowContainer(
                glowColor: Theme.of(context).primaryColor,
                child: AvatarCliente(
                  imagenUrl: widget.cliente.imagenUrl,
                  nombre: widget.cliente.nombre,
                  size: 120,
                ),
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Título animado
          SlideTransition(
            position: _slideAnimation,
            child: Column(
              children: [
                Text(
                  'Editar Cliente',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Actualiza la información del cliente',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: context.adaptiveTextColor.withValues(alpha: 0.7),
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMainForm() {
    return ScaleTransition(
      scale: _scaleAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [context.adaptiveShadow],
          ),
          child: Form(
            key: _formKey,
            child: Column(
              children: [
                _buildFormField(
                  controller: nombreController,
                  focusNode: _nombreFocus,
                  nextFocusNode: _telefonoFocus,
                  label: 'Nombre completo',
                  icon: Icons.person,
                  validator: _validateNombre,
                  keyboardType: TextInputType.name,
                  errorText: nombreError,
                ),

                const SizedBox(height: 20),

                _buildFormField(
                  controller: telefonoController,
                  focusNode: _telefonoFocus,
                  nextFocusNode: _emailFocus,
                  label: 'Teléfono',
                  icon: Icons.phone,
                  validator: _validateTelefono,
                  keyboardType: TextInputType.phone,
                  errorText: telefonoError,
                ),

                const SizedBox(height: 20),

                _buildFormField(
                  controller: emailController,
                  focusNode: _emailFocus,
                  label: 'Correo electrónico',
                  icon: Icons.email,
                  validator: _validateEmail,
                  keyboardType: TextInputType.emailAddress,
                  errorText: emailError,
                  isLast: true,
                ),

                const SizedBox(height: 30),

                _buildSaveButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFormField({
    required TextEditingController controller,
    required FocusNode focusNode,
    FocusNode? nextFocusNode,
    required String label,
    required IconData icon,
    required Function(String) validator,
    required TextInputType keyboardType,
    String? errorText,
    bool isLast = false,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).shadowColor.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: controller,
        focusNode: focusNode,
        keyboardType: keyboardType,
        onChanged: validator,
        onFieldSubmitted: (_) {
          if (isLast) {
            _guardarEdicion();
          } else if (nextFocusNode != null) {
            FocusScope.of(context).requestFocus(nextFocusNode);
          }
        },
        validator: (value) {
          if (value == null || value.isEmpty) {
            return '$label es requerido';
          }

          switch (label) {
            case 'Nombre completo':
              return Cliente.isValidNombre(value) ? null : 'Nombre debe tener al menos 2 caracteres';
            case 'Teléfono':
              return Cliente.isValidTelefono(value) ? null : 'Formato de teléfono inválido';
            case 'Correo electrónico':
              return Cliente.isValidEmail(value) ? null : 'Formato de email inválido';
            default:
              return null;
          }
        },
        decoration: InputDecoration(
          labelText: label,
          errorText: errorText,
          prefixIcon: Icon(icon),
          filled: true,
          fillColor: Theme.of(context).cardColor,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide.none,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(
              color: Theme.of(context).dividerColor.withValues(alpha: 0.3),
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(
              color: Theme.of(context).primaryColor,
              width: 2,
            ),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: const BorderSide(
              color: Colors.red,
              width: 2,
            ),
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 16,
          ),
        ),
      ),
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: ElevatedButton(
              onPressed: _isLoading ? null : _guardarEdicion,
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).primaryColor,
                foregroundColor: Colors.white,
                elevation: 8,
                shadowColor: Theme.of(context).primaryColor.withValues(alpha: 0.3),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
              child: _isLoading
                  ? Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        ),
                        const SizedBox(width: 12),
                        const Text('Guardando...'),
                      ],
                    )
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.save),
                        const SizedBox(width: 8),
                        const Text(
                          'Guardar Cambios',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildSuccessOverlay() {
    return AnimatedBuilder(
      animation: _successAnimation,
      builder: (context, child) {
        return Container(
          color: Colors.black.withValues(alpha: 0.5 * _successAnimation.value),
          child: Center(
            child: Transform.scale(
              scale: _successAnimation.value,
              child: Container(
                padding: const EdgeInsets.all(32),
                decoration: BoxDecoration(
                  color: Colors.green,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.green.withValues(alpha: 0.3),
                      blurRadius: 20,
                      spreadRadius: 5,
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.check_circle,
                      color: Colors.white,
                      size: 64,
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      '¡Cliente actualizado!',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Los cambios se han guardado correctamente',
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}