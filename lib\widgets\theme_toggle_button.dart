import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Widget para cambiar entre modo claro y oscuro rápidamente
class ThemeToggleButton extends StatefulWidget {
  final Function(ThemeMode) onThemeChanged;
  final ThemeMode currentTheme;

  const ThemeToggleButton({
    super.key,
    required this.onThemeChanged,
    required this.currentTheme,
  });

  @override
  State<ThemeToggleButton> createState() => _ThemeToggleButtonState();
}

class _ThemeToggleButtonState extends State<ThemeToggleButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _rotationAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.elasticOut));

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _toggleTheme() {
    HapticFeedback.mediumImpact();
    _controller.forward().then((_) {
      _controller.reverse();
    });

    final newTheme = widget.currentTheme == ThemeMode.light 
        ? ThemeMode.dark 
        : ThemeMode.light;
    
    widget.onThemeChanged(newTheme);
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Transform.rotate(
            angle: _rotationAnimation.value * 6.28, // 2π radianes
            child: Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: isDark
                      ? [Colors.indigo.shade800, Colors.purple.shade800]
                      : [Colors.orange.shade400, Colors.yellow.shade400],
                ),
                boxShadow: [
                  BoxShadow(
                    color: (isDark ? Colors.purple : Colors.orange).withValues(alpha: 0.3),
                    blurRadius: 12,
                    spreadRadius: 2,
                  ),
                ],
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(25),
                  onTap: _toggleTheme,
                  child: Container(
                    width: 50,
                    height: 50,
                    child: Icon(
                      isDark ? Icons.light_mode : Icons.dark_mode,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Widget flotante para cambio rápido de tema
class FloatingThemeToggle extends StatelessWidget {
  final Function(ThemeMode) onThemeChanged;
  final ThemeMode currentTheme;

  const FloatingThemeToggle({
    super.key,
    required this.onThemeChanged,
    required this.currentTheme,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: MediaQuery.of(context).padding.top + 10,
      right: 16,
      child: ThemeToggleButton(
        onThemeChanged: onThemeChanged,
        currentTheme: currentTheme,
      ),
    );
  }
}

/// Widget de demostración de temas
class ThemeDemo extends StatefulWidget {
  const ThemeDemo({super.key});

  @override
  State<ThemeDemo> createState() => _ThemeDemoState();
}

class _ThemeDemoState extends State<ThemeDemo> {
  ThemeMode _themeMode = ThemeMode.light;

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Theme Demo',
      theme: ThemeData.light(),
      darkTheme: ThemeData.dark(),
      themeMode: _themeMode,
      home: Scaffold(
        body: Stack(
          children: [
            // Contenido principal
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text(
                    'Demostración de Temas',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 20),
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        children: [
                          const Text('Esta es una tarjeta de ejemplo'),
                          const SizedBox(height: 10),
                          ElevatedButton(
                            onPressed: () {},
                            child: const Text('Botón de ejemplo'),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            // Toggle flotante
            FloatingThemeToggle(
              onThemeChanged: (theme) {
                setState(() {
                  _themeMode = theme;
                });
              },
              currentTheme: _themeMode,
            ),
          ],
        ),
      ),
    );
  }
}

/// Mixin para agregar funcionalidad de cambio de tema a cualquier widget
mixin ThemeToggleMixin<T extends StatefulWidget> on State<T> {
  ThemeMode _themeMode = ThemeMode.system;
  
  ThemeMode get themeMode => _themeMode;
  
  void toggleTheme() {
    setState(() {
      _themeMode = _themeMode == ThemeMode.light 
          ? ThemeMode.dark 
          : ThemeMode.light;
    });
    HapticFeedback.mediumImpact();
  }
  
  void setTheme(ThemeMode mode) {
    setState(() {
      _themeMode = mode;
    });
  }
  
  Widget buildThemeToggleButton() {
    return ThemeToggleButton(
      onThemeChanged: setTheme,
      currentTheme: _themeMode,
    );
  }
}

/// Extension para agregar métodos de tema a BuildContext
extension ThemeExtension on BuildContext {
  /// Verifica si el tema actual es oscuro
  bool get isDarkMode => Theme.of(this).brightness == Brightness.dark;
  
  /// Obtiene un color adaptativo basado en el tema
  Color adaptiveColor({
    required Color lightColor,
    required Color darkColor,
  }) {
    return isDarkMode ? darkColor : lightColor;
  }
  
  /// Obtiene un color de texto adaptativo
  Color get adaptiveTextColor {
    return Theme.of(this).textTheme.bodyMedium?.color ?? 
           (isDarkMode ? Colors.white : Colors.black);
  }
  
  /// Obtiene un color de fondo adaptativo
  Color get adaptiveBackgroundColor {
    return Theme.of(this).scaffoldBackgroundColor;
  }
  
  /// Obtiene un color de tarjeta adaptativo
  Color get adaptiveCardColor {
    return Theme.of(this).cardColor;
  }
  
  /// Obtiene una sombra adaptativa
  BoxShadow get adaptiveShadow {
    return BoxShadow(
      color: isDarkMode 
          ? Colors.black.withValues(alpha: 0.3)
          : Colors.black.withValues(alpha: 0.1),
      blurRadius: 8,
      offset: const Offset(0, 2),
    );
  }
}
