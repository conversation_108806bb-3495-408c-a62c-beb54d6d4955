import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import '../services/imagen_service.dart';
import '../widgets/imagen_avatar.dart';

class SelectorImagen extends StatefulWidget {
  final String? imagenUrl;
  final String nombre;
  final String carpeta; // 'clientes' o 'modulos'
  final String id;
  final Function(String?) onImagenCambiada;
  final double size;
  final bool esCircular;
  final IconData? iconoPorDefecto;

  const SelectorImagen({
    super.key,
    this.imagenUrl,
    required this.nombre,
    required this.carpeta,
    required this.id,
    required this.onImagenCambiada,
    this.size = 100,
    this.esCircular = true,
    this.iconoPorDefecto,
  });

  @override
  State<SelectorImagen> createState() => _SelectorImagenState();
}

class _SelectorImagenState extends State<SelectorImagen> {
  bool _subiendo = false;
  String? _imagenUrlActual;

  @override
  void initState() {
    super.initState();
    _imagenUrlActual = widget.imagenUrl;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Stack(
          children: [
            // Avatar/Imagen
            ImagenAvatar(
              imagenUrl: _imagenUrlActual,
              nombre: widget.nombre,
              size: widget.size,
              esCircular: widget.esCircular,
              iconoPorDefecto: widget.iconoPorDefecto,
            ),
            
            // Overlay de carga
            if (_subiendo)
              Container(
                width: widget.size,
                height: widget.size,
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.6),
                  shape: widget.esCircular ? BoxShape.circle : BoxShape.rectangle,
                  borderRadius: widget.esCircular ? null : BorderRadius.circular(8),
                ),
                child: const Center(
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
              ),
            
            // Botón de editar
            if (!_subiendo)
              Positioned(
                bottom: 0,
                right: 0,
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.indigo,
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.white, width: 2),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.3),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: IconButton(
                    icon: const Icon(Icons.camera_alt, color: Colors.white, size: 20),
                    onPressed: _mostrarOpcionesImagen,
                    padding: const EdgeInsets.all(8),
                    constraints: const BoxConstraints(
                      minWidth: 36,
                      minHeight: 36,
                    ),
                  ),
                ),
              ),
          ],
        ),
        
        const SizedBox(height: 8),
        
        // Botones de acción
        if (!_subiendo && _imagenUrlActual != null)
          TextButton.icon(
            onPressed: _eliminarImagen,
            icon: const Icon(Icons.delete, size: 16),
            label: const Text('Eliminar imagen'),
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
              textStyle: const TextStyle(fontSize: 12),
            ),
          ),
      ],
    );
  }

  void _mostrarOpcionesImagen() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            
            const Text(
              'Seleccionar imagen',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildOpcionImagen(
                  icono: Icons.photo_library,
                  titulo: 'Galería',
                  onTap: () => _seleccionarImagen(ImageSource.gallery),
                ),
                _buildOpcionImagen(
                  icono: Icons.camera_alt,
                  titulo: 'Cámara',
                  onTap: () => _seleccionarImagen(ImageSource.camera),
                ),
              ],
            ),
            
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildOpcionImagen({
    required IconData icono,
    required String titulo,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Icon(icono, size: 40, color: Colors.indigo),
            const SizedBox(height: 8),
            Text(
              titulo,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _seleccionarImagen(ImageSource source) async {
    Navigator.pop(context); // Cerrar bottom sheet
    
    setState(() => _subiendo = true);
    
    try {
      final XFile? imagen = await ImagenService.seleccionarImagen(source: source);
      if (imagen == null) {
        setState(() => _subiendo = false);
        return;
      }

      // Validar imagen
      if (!ImagenService.esImagenValida(imagen)) {
        _mostrarError('El archivo seleccionado no es una imagen válida');
        setState(() => _subiendo = false);
        return;
      }

      // Validar tamaño
      if (!await ImagenService.validarTamanoMaximo(imagen)) {
        _mostrarError('La imagen es demasiado grande (máximo 5MB)');
        setState(() => _subiendo = false);
        return;
      }

      // Subir imagen
      final String? nuevaUrl = await ImagenService.subirImagen(
        imagen: imagen,
        carpeta: widget.carpeta,
        id: widget.id,
      );

      if (nuevaUrl != null) {
        // Eliminar imagen anterior si existe
        if (_imagenUrlActual != null) {
          await ImagenService.eliminarImagen(_imagenUrlActual!);
        }
        
        setState(() {
          _imagenUrlActual = nuevaUrl;
          _subiendo = false;
        });
        
        widget.onImagenCambiada(nuevaUrl);
        _mostrarExito('Imagen actualizada correctamente');
      } else {
        setState(() => _subiendo = false);
        _mostrarError('Error al subir la imagen');
      }
    } catch (e) {
      setState(() => _subiendo = false);
      _mostrarError('Error inesperado: $e');
    }
  }

  Future<void> _eliminarImagen() async {
    if (_imagenUrlActual == null) return;

    final bool? confirmar = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Eliminar imagen'),
        content: const Text('¿Estás seguro de que quieres eliminar esta imagen?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancelar'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Eliminar'),
          ),
        ],
      ),
    );

    if (confirmar == true) {
      setState(() => _subiendo = true);
      
      final bool eliminada = await ImagenService.eliminarImagen(_imagenUrlActual!);
      
      if (eliminada) {
        setState(() {
          _imagenUrlActual = null;
          _subiendo = false;
        });
        widget.onImagenCambiada(null);
        _mostrarExito('Imagen eliminada correctamente');
      } else {
        setState(() => _subiendo = false);
        _mostrarError('Error al eliminar la imagen');
      }
    }
  }

  void _mostrarError(String mensaje) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(mensaje),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _mostrarExito(String mensaje) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(mensaje),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
