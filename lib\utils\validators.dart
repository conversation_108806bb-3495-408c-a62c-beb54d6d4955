import '../utils/constants.dart';

/// Sistema de validaciones centralizado para la aplicación
class Validators {
  
  /// Validar email
  static String? email(String? value) {
    if (value == null || value.isEmpty) {
      return 'El email es requerido';
    }
    
    final emailRegex = RegExp(AppConstants.emailPattern);
    if (!emailRegex.hasMatch(value)) {
      return 'Ingresa un email válido';
    }
    
    return null;
  }
  
  /// Validar contraseña
  static String? password(String? value) {
    if (value == null || value.isEmpty) {
      return 'La contraseña es requerida';
    }
    
    if (value.length < AppConstants.minPasswordLength) {
      return 'La contraseña debe tener al menos ${AppConstants.minPasswordLength} caracteres';
    }
    
    // Verificar que tenga al menos una letra y un número
    if (!RegExp(r'^(?=.*[A-Za-z])(?=.*\d)').hasMatch(value)) {
      return 'La contraseña debe contener al menos una letra y un número';
    }
    
    return null;
  }
  
  /// Validar confirmación de contraseña
  static String? confirmPassword(String? value, String? originalPassword) {
    if (value == null || value.isEmpty) {
      return 'Confirma tu contraseña';
    }
    
    if (value != originalPassword) {
      return 'Las contraseñas no coinciden';
    }
    
    return null;
  }
  
  /// Validar nombre
  static String? name(String? value, {String fieldName = 'nombre'}) {
    if (value == null || value.isEmpty) {
      return 'El $fieldName es requerido';
    }
    
    if (value.trim().length < 2) {
      return 'El $fieldName debe tener al menos 2 caracteres';
    }
    
    if (value.length > AppConstants.maxNameLength) {
      return 'El $fieldName no puede exceder ${AppConstants.maxNameLength} caracteres';
    }
    
    // Verificar que solo contenga letras, espacios y algunos caracteres especiales
    if (!RegExp(r'^[a-zA-ZáéíóúÁÉÍÓÚñÑ\s\-\.]+$').hasMatch(value)) {
      return 'El $fieldName solo puede contener letras, espacios, guiones y puntos';
    }
    
    return null;
  }
  
  /// Validar teléfono
  static String? phone(String? value) {
    if (value == null || value.isEmpty) {
      return 'El teléfono es requerido';
    }
    
    // Remover espacios y caracteres especiales para validación
    final cleanPhone = value.replaceAll(RegExp(r'[\s\-\(\)]'), '');
    
    // Validar formato colombiano (10 dígitos) o internacional
    if (!RegExp(r'^(\+57)?[3][0-9]{9}$').hasMatch(cleanPhone) && 
        !RegExp(r'^[0-9]{10}$').hasMatch(cleanPhone)) {
      return 'Ingresa un número de teléfono válido (ej: 3001234567)';
    }
    
    return null;
  }
  
  /// Validar precio/monto
  static String? price(String? value, {String fieldName = 'precio'}) {
    if (value == null || value.isEmpty) {
      return 'El $fieldName es requerido';
    }
    
    final doubleValue = double.tryParse(value.replaceAll(',', ''));
    if (doubleValue == null) {
      return 'Ingresa un $fieldName válido';
    }
    
    if (doubleValue < 0) {
      return 'El $fieldName no puede ser negativo';
    }
    
    if (doubleValue > 999999999) {
      return 'El $fieldName es demasiado alto';
    }
    
    return null;
  }
  
  /// Validar cantidad
  static String? quantity(String? value) {
    if (value == null || value.isEmpty) {
      return 'La cantidad es requerida';
    }
    
    final intValue = int.tryParse(value);
    if (intValue == null) {
      return 'Ingresa una cantidad válida';
    }
    
    if (intValue <= 0) {
      return 'La cantidad debe ser mayor a 0';
    }
    
    if (intValue > 10000) {
      return 'La cantidad es demasiado alta';
    }
    
    return null;
  }
  
  /// Validar descripción
  static String? description(String? value, {bool required = false}) {
    if (required && (value == null || value.isEmpty)) {
      return 'La descripción es requerida';
    }
    
    if (value != null && value.length > AppConstants.maxDescriptionLength) {
      return 'La descripción no puede exceder ${AppConstants.maxDescriptionLength} caracteres';
    }
    
    return null;
  }
  
  /// Validar ID único
  static String? uniqueId(String? value, {String fieldName = 'ID'}) {
    if (value == null || value.isEmpty) {
      return 'El $fieldName es requerido';
    }
    
    if (value.length < 3) {
      return 'El $fieldName debe tener al menos 3 caracteres';
    }
    
    if (value.length > 20) {
      return 'El $fieldName no puede exceder 20 caracteres';
    }
    
    // Solo letras, números, guiones y guiones bajos
    if (!RegExp(r'^[a-zA-Z0-9_\-]+$').hasMatch(value)) {
      return 'El $fieldName solo puede contener letras, números, guiones y guiones bajos';
    }
    
    return null;
  }
  
  /// Validar campo requerido genérico
  static String? required(String? value, {String fieldName = 'campo'}) {
    if (value == null || value.trim().isEmpty) {
      return 'El $fieldName es requerido';
    }
    return null;
  }
  
  /// Validar longitud mínima
  static String? minLength(String? value, int minLength, {String fieldName = 'campo'}) {
    if (value == null || value.length < minLength) {
      return 'El $fieldName debe tener al menos $minLength caracteres';
    }
    return null;
  }
  
  /// Validar longitud máxima
  static String? maxLength(String? value, int maxLength, {String fieldName = 'campo'}) {
    if (value != null && value.length > maxLength) {
      return 'El $fieldName no puede exceder $maxLength caracteres';
    }
    return null;
  }
  
  /// Validar rango numérico
  static String? numberRange(String? value, double min, double max, {String fieldName = 'valor'}) {
    if (value == null || value.isEmpty) {
      return 'El $fieldName es requerido';
    }
    
    final doubleValue = double.tryParse(value);
    if (doubleValue == null) {
      return 'Ingresa un $fieldName válido';
    }
    
    if (doubleValue < min || doubleValue > max) {
      return 'El $fieldName debe estar entre $min y $max';
    }
    
    return null;
  }
  
  /// Combinar múltiples validadores
  static String? Function(String?) combine(List<String? Function(String?)> validators) {
    return (String? value) {
      for (final validator in validators) {
        final result = validator(value);
        if (result != null) return result;
      }
      return null;
    };
  }
}

/// Validadores específicos para la aplicación
class AppValidators {
  
  /// Validar ID de cliente
  static String? clienteId(String? value) {
    return Validators.combine([
      (v) => Validators.required(v, fieldName: 'ID de cliente'),
      (v) => Validators.uniqueId(v, fieldName: 'ID de cliente'),
    ])(value);
  }
  
  /// Validar nombre de cliente
  static String? clienteNombre(String? value) {
    return Validators.name(value, fieldName: 'nombre del cliente');
  }
  
  /// Validar teléfono de cliente
  static String? clienteTelefono(String? value) {
    return Validators.phone(value);
  }
  
  /// Validar email de cliente
  static String? clienteEmail(String? value) {
    return Validators.email(value);
  }
  
  /// Validar nombre de módulo
  static String? moduloNombre(String? value) {
    return Validators.name(value, fieldName: 'nombre del módulo');
  }
  
  /// Validar precio de módulo
  static String? moduloPrecio(String? value) {
    return Validators.price(value, fieldName: 'precio del módulo');
  }
  
  /// Validar descripción de módulo
  static String? moduloDescripcion(String? value) {
    return Validators.description(value);
  }
  
  /// Validar ID de factura
  static String? facturaId(String? value) {
    return Validators.combine([
      (v) => Validators.required(v, fieldName: 'ID de factura'),
      (v) => Validators.uniqueId(v, fieldName: 'ID de factura'),
    ])(value);
  }
  
  /// Validar método de pago
  static String? metodoPago(String? value) {
    if (value == null || value.isEmpty) {
      return 'El método de pago es requerido';
    }
    
    final metodosValidos = ['Efectivo', 'Tarjeta', 'Transferencia', 'Cheque'];
    if (!metodosValidos.contains(value)) {
      return 'Selecciona un método de pago válido';
    }
    
    return null;
  }
  
  /// Validar cantidad en detalle de factura
  static String? detalleCantidad(String? value) {
    return Validators.quantity(value);
  }
}
