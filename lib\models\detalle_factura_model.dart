class DetalleFactura {
  String idDetalle;
  String idFactura;
  int cantidad;
  String modulo;
  bool suscripcion;
  double precioModulo;

  DetalleFactura({
    required this.idDetalle,
    required this.idFactura,
    required this.cantidad,
    required this.modulo,
    required this.suscripcion,
    required this.precioModulo,
  });

  // Método para calcular el total de este detalle
  double get total => cantidad * precioModulo;

  // Método copyWith para inmutabilidad
  DetalleFactura copyWith({
    String? idDetalle,
    String? idFactura,
    int? cantidad,
    String? modulo,
    bool? suscripcion,
    double? precioModulo,
  }) {
    return DetalleFactura(
      idDetalle: idDetalle ?? this.idDetalle,
      idFactura: idFactura ?? this.idFactura,
      cantidad: cantidad ?? this.cantidad,
      modulo: modulo ?? this.modulo,
      suscripcion: suscripcion ?? this.suscripcion,
      precioModulo: precioModulo ?? this.precioModulo,
    );
  }

  // Método toString para debugging
  @override
  String toString() {
    return 'DetalleFactura(idDetalle: $idDetalle, idFactura: $idFactura, '
           'cantidad: $cantidad, modulo: $modulo, suscripcion: $suscripcion, '
           'precioModulo: $precioModulo, total: $total)';
  }

  factory DetalleFactura.fromMap(Map<String, dynamic> json) {
    return DetalleFactura(
      idDetalle: json["IdDetalle"] ?? "",
      idFactura: json["IdFactura"] ?? "",
      cantidad: json["Cantidad"] ?? 0,
      modulo: json["Modulo"] ?? "",
      // Manejo consistente: INTEGER en DB (0=false, 1=true)
      suscripcion: (json["Suscripcion"] == 1) ||
                   (json["Suscripcion"] == true) ||
                   (json["Suscripcion"] == "true"),
      precioModulo: (json["PrecioModulo"] is int)
          ? (json["PrecioModulo"] as int).toDouble()
          : (json["PrecioModulo"] as double? ?? 0.0),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      "IdDetalle": idDetalle,
      "IdFactura": idFactura,
      "Cantidad": cantidad,
      "Modulo": modulo,
      // Convertir bool a INTEGER para consistencia con DB
      "Suscripcion": suscripcion ? 1 : 0,
      "PrecioModulo": precioModulo,
    };
  }
}
