import 'package:flutter/material.dart';
import '../utils/responsive_utils.dart';
import 'responsive_container.dart';

class SearchFilterBar extends StatefulWidget {
  final String? initialSearchQuery;
  final Function(String) onSearchChanged;
  final VoidCallback? onFilterPressed;
  final String searchHint;
  final bool showFilterButton;
  final Widget? customFilterWidget;

  const SearchFilterBar({
    super.key,
    this.initialSearchQuery,
    required this.onSearchChanged,
    this.onFilterPressed,
    this.searchHint = "Buscar...",
    this.showFilterButton = true,
    this.customFilterWidget,
  });

  @override
  State<SearchFilterBar> createState() => _SearchFilterBarState();
}

class _SearchFilterBarState extends State<SearchFilterBar> {
  late TextEditingController _searchController;
  
  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController(text: widget.initialSearchQuery ?? '');
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: widget.searchHint,
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          widget.onSearchChanged('');
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: Colors.white.withAlpha(242),
              ),
              onChanged: widget.onSearchChanged,
            ),
          ),
          if (widget.showFilterButton) ...[
            const SizedBox(width: 12),
            Container(
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary,
                borderRadius: BorderRadius.circular(12),
              ),
              child: IconButton(
                icon: const Icon(Icons.filter_list, color: Colors.white),
                onPressed: widget.onFilterPressed,
                tooltip: 'Filtros',
              ),
            ),
          ],
          if (widget.customFilterWidget != null) ...[
            const SizedBox(width: 12),
            widget.customFilterWidget!,
          ],
        ],
      ),
    );
  }
}

class PaginationControls extends StatelessWidget {
  final int currentPage;
  final int totalPages;
  final int totalItems;
  final int itemsPerPage;
  final Function(int) onPageChanged;
  final Function(int) onItemsPerPageChanged;

  const PaginationControls({
    super.key,
    required this.currentPage,
    required this.totalPages,
    required this.totalItems,
    required this.itemsPerPage,
    required this.onPageChanged,
    required this.onItemsPerPageChanged,
  });

  @override
  Widget build(BuildContext context) {
    final isMobile = ResponsiveUtils.isMobile(context);

    return Container(
      padding: ResponsiveUtils.getAdaptivePadding(context, mobile: 12, tablet: 16, desktop: 20),
      child: isMobile ? _buildMobileLayout(context) : _buildDesktopLayout(context),
    );
  }

  Widget _buildMobileLayout(BuildContext context) {
    return Column(
      children: [
        // Información de elementos
        ResponsiveText(
          'Mostrando ${_getStartItem()}-${_getEndItem()} de $totalItems',
          mobileFontSize: 12,
          tabletFontSize: 13,
          desktopFontSize: 14,
          color: Theme.of(context).textTheme.bodySmall?.color,
        ),

        const SizedBox(height: 12),

        // Selector de elementos por página
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ResponsiveText(
              'Por página:',
              mobileFontSize: 14,
              tabletFontSize: 15,
              desktopFontSize: 16,
            ),
            const SizedBox(width: 8),
            DropdownButton<int>(
              value: itemsPerPage,
              items: [10, 20, 50, 100].map((int value) {
                return DropdownMenuItem<int>(
                  value: value,
                  child: Text('$value'),
                );
              }).toList(),
              onChanged: (int? newValue) {
                if (newValue != null) {
                  onItemsPerPageChanged(newValue);
                }
              },
              underline: Container(),
            ),
          ],
        ),

        const SizedBox(height: 12),

        // Controles de navegación
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            IconButton(
              icon: const Icon(Icons.first_page),
              onPressed: currentPage > 1 ? () => onPageChanged(1) : null,
            ),
            IconButton(
              icon: const Icon(Icons.chevron_left),
              onPressed: currentPage > 1 ? () => onPageChanged(currentPage - 1) : null,
            ),

            // Indicador de página actual
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(8),
              ),
              child: ResponsiveText(
                '$currentPage de $totalPages',
                mobileFontSize: 14,
                tabletFontSize: 15,
                desktopFontSize: 16,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onPrimaryContainer,
              ),
            ),

            IconButton(
              icon: const Icon(Icons.chevron_right),
              onPressed: currentPage < totalPages ? () => onPageChanged(currentPage + 1) : null,
            ),
            IconButton(
              icon: const Icon(Icons.last_page),
              onPressed: currentPage < totalPages ? () => onPageChanged(totalPages) : null,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDesktopLayout(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Información de elementos
        ResponsiveText(
          'Mostrando ${_getStartItem()}-${_getEndItem()} de $totalItems',
          mobileFontSize: 12,
          tabletFontSize: 13,
          desktopFontSize: 14,
          color: Theme.of(context).textTheme.bodySmall?.color,
        ),

        // Controles de paginación
        Row(
          children: [
            // Selector de elementos por página
            DropdownButton<int>(
              value: itemsPerPage,
              items: [10, 20, 50, 100].map((int value) {
                return DropdownMenuItem<int>(
                  value: value,
                  child: Text('$value por página'),
                );
              }).toList(),
              onChanged: (int? newValue) {
                if (newValue != null) {
                  onItemsPerPageChanged(newValue);
                }
              },
              underline: Container(),
            ),

            const SizedBox(width: 16),

            // Botones de navegación
            IconButton(
              icon: const Icon(Icons.first_page),
              onPressed: currentPage > 1 ? () => onPageChanged(1) : null,
            ),
            IconButton(
              icon: const Icon(Icons.chevron_left),
              onPressed: currentPage > 1 ? () => onPageChanged(currentPage - 1) : null,
            ),

            // Indicador de página actual
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(8),
              ),
              child: ResponsiveText(
                '$currentPage de $totalPages',
                mobileFontSize: 14,
                tabletFontSize: 15,
                desktopFontSize: 16,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onPrimaryContainer,
              ),
            ),

            IconButton(
              icon: const Icon(Icons.chevron_right),
              onPressed: currentPage < totalPages ? () => onPageChanged(currentPage + 1) : null,
            ),
            IconButton(
              icon: const Icon(Icons.last_page),
              onPressed: currentPage < totalPages ? () => onPageChanged(totalPages) : null,
            ),
          ],
        ),
      ],
    );
  }

  int _getStartItem() {
    if (totalItems == 0) return 0;
    return ((currentPage - 1) * itemsPerPage) + 1;
  }

  int _getEndItem() {
    final endItem = currentPage * itemsPerPage;
    return endItem > totalItems ? totalItems : endItem;
  }
}

class CustomFilterChip extends StatelessWidget {
  final String label;
  final bool isSelected;
  final VoidCallback onTap;
  final VoidCallback? onRemove;

  const CustomFilterChip({
    super.key,
    required this.label,
    required this.isSelected,
    required this.onTap,
    this.onRemove,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (_) => onTap(),
        deleteIcon: onRemove != null ? const Icon(Icons.close, size: 16) : null,
        onDeleted: onRemove,
        backgroundColor: Colors.white.withAlpha(242),
        selectedColor: Theme.of(context).colorScheme.primaryContainer,
      ),
    );
  }
}
