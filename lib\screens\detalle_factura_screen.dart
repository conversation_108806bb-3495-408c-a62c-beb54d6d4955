import 'package:flutter/material.dart';
import '../services/factura_service.dart';
import '../models/detalle_factura_model.dart';
import '../widgets/loading_state_builder.dart';

class DetalleFacturaScreen extends StatefulWidget {
  final String idFactura;

  const DetalleFacturaScreen({super.key, required this.idFactura});

  @override
  State<DetalleFacturaScreen> createState() => _DetalleFacturaScreenState();
}

class _DetalleFacturaScreenState extends State<DetalleFacturaScreen> {
  Future<List<DetalleFactura>> _cargarDetalles() {
    return FacturaService.obtenerDetalleFactura(widget.idFactura);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text("Detalles de Factura #${widget.idFactura}")),
      body: LoadingStateBuilder<List<DetalleFactura>>(
        future: _cargarDetalles(),
        emptyMessage: "No hay detalles registrados para esta factura",
        errorMessage: "Error al cargar los detalles",
        builder: (context, detalles) {
          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: detalles.length,
            itemBuilder: (context, index) {
              final detalle = detalles[index];
              return Card(
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                color: Colors.white.withAlpha(242),
                elevation: 6,
                margin: const EdgeInsets.symmetric(vertical: 8),
                child: ListTile(
                  contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  title: Text(
                    "Módulo: ${detalle.modulo}",
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 4),
                      Text(
                        "Suscripción: ${detalle.suscripcion ? 'Sí' : 'No'}",
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                      Text(
                        "Cantidad: ${detalle.cantidad}",
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                      Text(
                        "Precio: \$${detalle.precioModulo.toStringAsFixed(2)}",
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(fontWeight: FontWeight.w600),
                      ),
                    ],
                  ),
                  leading: CircleAvatar(
                    backgroundColor: detalle.suscripcion
                        ? Colors.green
                        : Theme.of(context).colorScheme.primary,
                    child: Icon(
                      detalle.suscripcion ? Icons.subscriptions : Icons.extension,
                      color: Colors.white,
                    ),
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }
}