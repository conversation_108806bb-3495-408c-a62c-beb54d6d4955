import 'package:flutter/material.dart';
import 'dart:math' as math;

/// Widget simple para mostrar gráficos de barras
class SimpleBar<PERSON>hart extends StatefulWidget {
  final Map<String, double> data;
  final String title;
  final Color color;
  final double height;

  const SimpleBarChart({
    super.key,
    required this.data,
    required this.title,
    this.color = Colors.blue,
    this.height = 200,
  });

  @override
  State<SimpleBarChart> createState() => _SimpleBarChartState();
}

class _SimpleBarChartState extends State<SimpleBarChart>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
    
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.data.isEmpty) {
      return Container(
        height: widget.height,
        decoration: BoxDecoration(
          color: Colors.grey.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.bar_chart,
                size: 48,
                color: Colors.grey.shade400,
              ),
              const SizedBox(height: 8),
              Text(
                'No hay datos para mostrar',
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          height: widget.height,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return CustomPaint(
                size: Size.infinite,
                painter: BarChartPainter(
                  data: widget.data,
                  color: widget.color,
                  progress: _animation.value,
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}

class BarChartPainter extends CustomPainter {
  final Map<String, double> data;
  final Color color;
  final double progress;

  BarChartPainter({
    required this.data,
    required this.color,
    required this.progress,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (data.isEmpty) return;

    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
    );

    // Encontrar el valor máximo para escalar
    final maxValue = data.values.reduce(math.max);
    if (maxValue == 0) return;

    // Calcular dimensiones
    final barWidth = (size.width - 32) / data.length;
    final chartHeight = size.height - 40; // Espacio para etiquetas
    
    int index = 0;
    for (var entry in data.entries) {
      final barHeight = (entry.value / maxValue) * chartHeight * progress;
      final x = 16 + (index * barWidth) + (barWidth * 0.1);
      final y = size.height - 20 - barHeight;
      final width = barWidth * 0.8;

      // Dibujar barra
      final rect = Rect.fromLTWH(x, y, width, barHeight);
      canvas.drawRRect(
        RRect.fromRectAndRadius(rect, const Radius.circular(4)),
        paint,
      );

      // Dibujar etiqueta del eje X
      textPainter.text = TextSpan(
        text: entry.key,
        style: TextStyle(
          color: Colors.grey.shade600,
          fontSize: 10,
        ),
      );
      textPainter.layout();
      
      final textX = x + (width / 2) - (textPainter.width / 2);
      final textY = size.height - 15;
      textPainter.paint(canvas, Offset(textX, textY));

      // Dibujar valor encima de la barra
      if (barHeight > 20) {
        textPainter.text = TextSpan(
          text: '\$${entry.value.toStringAsFixed(0)}',
          style: TextStyle(
            color: Colors.white,
            fontSize: 10,
            fontWeight: FontWeight.bold,
          ),
        );
        textPainter.layout();
        
        final valueX = x + (width / 2) - (textPainter.width / 2);
        final valueY = y + 5;
        textPainter.paint(canvas, Offset(valueX, valueY));
      }

      index++;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// Widget simple para mostrar gráfico de líneas
class SimpleLineChart extends StatefulWidget {
  final Map<String, double> data;
  final String title;
  final Color color;
  final double height;

  const SimpleLineChart({
    super.key,
    required this.data,
    required this.title,
    this.color = Colors.green,
    this.height = 200,
  });

  @override
  State<SimpleLineChart> createState() => _SimpleLineChartState();
}

class _SimpleLineChartState extends State<SimpleLineChart>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
    
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.data.isEmpty) {
      return Container(
        height: widget.height,
        decoration: BoxDecoration(
          color: Colors.grey.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.show_chart,
                size: 48,
                color: Colors.grey.shade400,
              ),
              const SizedBox(height: 8),
              Text(
                'No hay datos para mostrar',
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          height: widget.height,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return CustomPaint(
                size: Size.infinite,
                painter: LineChartPainter(
                  data: widget.data,
                  color: widget.color,
                  progress: _animation.value,
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}

class LineChartPainter extends CustomPainter {
  final Map<String, double> data;
  final Color color;
  final double progress;

  LineChartPainter({
    required this.data,
    required this.color,
    required this.progress,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (data.isEmpty || data.length < 2) return;

    final paint = Paint()
      ..color = color
      ..strokeWidth = 3
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    final pointPaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    // Encontrar el valor máximo para escalar
    final maxValue = data.values.reduce(math.max);
    final minValue = data.values.reduce(math.min);
    if (maxValue == minValue) return;

    // Calcular dimensiones
    final chartWidth = size.width - 32;
    final chartHeight = size.height - 40;
    final stepX = chartWidth / (data.length - 1);
    
    final path = Path();
    final points = <Offset>[];
    
    int index = 0;
    for (var entry in data.entries) {
      final x = 16 + (index * stepX);
      final normalizedValue = (entry.value - minValue) / (maxValue - minValue);
      final y = size.height - 20 - (normalizedValue * chartHeight);
      
      points.add(Offset(x, y));
      
      if (index == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
      
      index++;
    }

    // Dibujar línea con progreso
    final pathMetrics = path.computeMetrics();
    for (var pathMetric in pathMetrics) {
      final extractedPath = pathMetric.extractPath(
        0.0,
        pathMetric.length * progress,
      );
      canvas.drawPath(extractedPath, paint);
    }

    // Dibujar puntos
    for (int i = 0; i < points.length * progress; i++) {
      if (i < points.length) {
        canvas.drawCircle(points[i], 4, pointPaint);
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
