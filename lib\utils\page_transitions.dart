import 'package:flutter/material.dart';

/// Transiciones personalizadas para navegación entre pantallas
class PageTransitions {
  
  /// Transición de deslizamiento desde la derecha
  static Route<T> slideFromRight<T extends Object?>(Widget page) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(milliseconds: 300),
      reverseTransitionDuration: const Duration(milliseconds: 250),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(1.0, 0.0);
        const end = Offset.zero;
        const curve = Curves.easeInOut;

        var tween = Tween(begin: begin, end: end).chain(
          CurveTween(curve: curve),
        );

        return SlideTransition(
          position: animation.drive(tween),
          child: child,
        );
      },
    );
  }

  /// Transición de deslizamiento desde abajo
  static Route<T> slideFromBottom<T extends Object?>(Widget page) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(milliseconds: 400),
      reverseTransitionDuration: const Duration(milliseconds: 300),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(0.0, 1.0);
        const end = Offset.zero;
        const curve = Curves.easeOutCubic;

        var tween = Tween(begin: begin, end: end).chain(
          CurveTween(curve: curve),
        );

        return SlideTransition(
          position: animation.drive(tween),
          child: child,
        );
      },
    );
  }

  /// Transición de escala con fade
  static Route<T> scaleWithFade<T extends Object?>(Widget page) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(milliseconds: 350),
      reverseTransitionDuration: const Duration(milliseconds: 250),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const curve = Curves.easeInOut;
        
        var scaleTween = Tween(begin: 0.8, end: 1.0).chain(
          CurveTween(curve: curve),
        );
        
        var fadeTween = Tween(begin: 0.0, end: 1.0).chain(
          CurveTween(curve: curve),
        );

        return ScaleTransition(
          scale: animation.drive(scaleTween),
          child: FadeTransition(
            opacity: animation.drive(fadeTween),
            child: child,
          ),
        );
      },
    );
  }

  /// Transición de rotación con escala
  static Route<T> rotateWithScale<T extends Object?>(Widget page) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(milliseconds: 500),
      reverseTransitionDuration: const Duration(milliseconds: 350),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const curve = Curves.elasticOut;
        
        var rotateTween = Tween(begin: 0.0, end: 1.0).chain(
          CurveTween(curve: curve),
        );
        
        var scaleTween = Tween(begin: 0.0, end: 1.0).chain(
          CurveTween(curve: curve),
        );

        return RotationTransition(
          turns: animation.drive(rotateTween),
          child: ScaleTransition(
            scale: animation.drive(scaleTween),
            child: child,
          ),
        );
      },
    );
  }

  /// Transición de flip horizontal
  static Route<T> flipHorizontal<T extends Object?>(Widget page) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(milliseconds: 600),
      reverseTransitionDuration: const Duration(milliseconds: 400),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return AnimatedBuilder(
          animation: animation,
          builder: (context, child) {
            if (animation.value < 0.5) {
              return Transform(
                alignment: Alignment.center,
                transform: Matrix4.identity()
                  ..setEntry(3, 2, 0.001)
                  ..rotateY(animation.value * 3.14159),
                child: Container(),
              );
            } else {
              return Transform(
                alignment: Alignment.center,
                transform: Matrix4.identity()
                  ..setEntry(3, 2, 0.001)
                  ..rotateY((1 - animation.value) * 3.14159),
                child: child,
              );
            }
          },
          child: child,
        );
      },
    );
  }

  /// Transición personalizada para módulos específicos
  static Route<T> moduleTransition<T extends Object?>(Widget page, String moduleType) {
    switch (moduleType.toLowerCase()) {
      case 'clientes':
        return slideFromRight(page);
      case 'facturas':
        return slideFromBottom(page);
      case 'modulos':
        return scaleWithFade(page);
      case 'reportes':
        return rotateWithScale(page);
      case 'configuracion':
        return flipHorizontal(page);
      default:
        return slideFromRight(page);
    }
  }

  /// Transición con efecto de ondas
  static Route<T> waveTransition<T extends Object?>(Widget page) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(milliseconds: 800),
      reverseTransitionDuration: const Duration(milliseconds: 500),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return AnimatedBuilder(
          animation: animation,
          builder: (context, child) {
            return ClipPath(
              clipper: WaveClipper(animation.value),
              child: child,
            );
          },
          child: child,
        );
      },
    );
  }
}

/// Clipper personalizado para efecto de ondas
class WaveClipper extends CustomClipper<Path> {
  final double animationValue;

  WaveClipper(this.animationValue);

  @override
  Path getClip(Size size) {
    final path = Path();
    final waveHeight = 50.0 * (1 - animationValue);
    final waveLength = size.width / 4;

    path.moveTo(0, size.height);
    
    for (double x = 0; x <= size.width; x += waveLength) {
      path.quadraticBezierTo(
        x + waveLength / 2,
        size.height - waveHeight,
        x + waveLength,
        size.height,
      );
    }
    
    path.lineTo(size.width, 0);
    path.lineTo(0, 0);
    path.close();

    return path;
  }

  @override
  bool shouldReclip(WaveClipper oldClipper) {
    return oldClipper.animationValue != animationValue;
  }
}

/// Extensión para facilitar el uso de transiciones
extension NavigationExtension on BuildContext {
  
  /// Navegar con transición personalizada
  Future<T?> pushWithTransition<T extends Object?>(
    Widget page, {
    String transitionType = 'slide',
    String? moduleType,
  }) {
    Route<T> route;
    
    if (moduleType != null) {
      route = PageTransitions.moduleTransition<T>(page, moduleType);
    } else {
      switch (transitionType) {
        case 'slide':
          route = PageTransitions.slideFromRight<T>(page);
          break;
        case 'slideBottom':
          route = PageTransitions.slideFromBottom<T>(page);
          break;
        case 'scale':
          route = PageTransitions.scaleWithFade<T>(page);
          break;
        case 'rotate':
          route = PageTransitions.rotateWithScale<T>(page);
          break;
        case 'flip':
          route = PageTransitions.flipHorizontal<T>(page);
          break;
        case 'wave':
          route = PageTransitions.waveTransition<T>(page);
          break;
        default:
          route = PageTransitions.slideFromRight<T>(page);
      }
    }
    
    return Navigator.of(this).push(route);
  }

  /// Reemplazar con transición
  Future<T?> pushReplacementWithTransition<T extends Object?, TO extends Object?>(
    Widget page, {
    String transitionType = 'slide',
    TO? result,
  }) {
    Route<T> route;
    
    switch (transitionType) {
      case 'slide':
        route = PageTransitions.slideFromRight<T>(page);
        break;
      case 'scale':
        route = PageTransitions.scaleWithFade<T>(page);
        break;
      default:
        route = PageTransitions.slideFromRight<T>(page);
    }
    
    return Navigator.of(this).pushReplacement(route, result: result);
  }
}

/// Widget para preview de transiciones (útil para desarrollo)
class TransitionPreview extends StatefulWidget {
  final List<Widget> pages;
  final List<String> transitionTypes;

  const TransitionPreview({
    super.key,
    required this.pages,
    required this.transitionTypes,
  });

  @override
  State<TransitionPreview> createState() => _TransitionPreviewState();
}

class _TransitionPreviewState extends State<TransitionPreview> {
  int currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Preview de Transiciones'),
        actions: [
          IconButton(
            icon: const Icon(Icons.skip_next),
            onPressed: _nextTransition,
          ),
        ],
      ),
      body: widget.pages[currentIndex],
      floatingActionButton: FloatingActionButton(
        onPressed: _nextTransition,
        child: const Icon(Icons.play_arrow),
      ),
    );
  }

  void _nextTransition() {
    final nextIndex = (currentIndex + 1) % widget.pages.length;
    final transitionType = widget.transitionTypes[nextIndex % widget.transitionTypes.length];
    
    context.pushWithTransition(
      widget.pages[nextIndex],
      transitionType: transitionType,
    );
    
    setState(() {
      currentIndex = nextIndex;
    });
  }
}
