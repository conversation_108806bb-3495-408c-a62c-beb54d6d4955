import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Campo de texto moderno con validación visual
class ModernTextField extends StatefulWidget {
  final String label;
  final String? hint;
  final IconData? prefixIcon;
  final IconData? suffixIcon;
  final VoidCallback? onSuffixTap;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final TextInputType? keyboardType;
  final bool obscureText;
  final bool enabled;
  final int? maxLines;
  final int? maxLength;
  final List<TextInputFormatter>? inputFormatters;
  final void Function(String)? onChanged;
  final void Function()? onTap;
  final bool readOnly;
  final Color? accentColor;

  const ModernTextField({
    super.key,
    required this.label,
    this.hint,
    this.prefixIcon,
    this.suffixIcon,
    this.onSuffixTap,
    this.controller,
    this.validator,
    this.keyboardType,
    this.obscureText = false,
    this.enabled = true,
    this.maxLines = 1,
    this.maxLength,
    this.inputFormatters,
    this.onChanged,
    this.onTap,
    this.readOnly = false,
    this.accentColor,
  });

  @override
  State<ModernTextField> createState() => _ModernTextFieldState();
}

class _ModernTextFieldState extends State<ModernTextField>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _focusAnimation;
  late Animation<Color?> _colorAnimation;
  
  final FocusNode _focusNode = FocusNode();
  String? _errorText;
  bool _isValid = true;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _focusAnimation = Tween<double>(begin: 1.0, end: 1.02).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    final accentColor = widget.accentColor ?? Theme.of(context).primaryColor;
    _colorAnimation = ColorTween(
      begin: Colors.grey.shade300,
      end: accentColor,
    ).animate(_animationController);

    _focusNode.addListener(() {
      if (_focusNode.hasFocus) {
        _animationController.forward();
      } else {
        _animationController.reverse();
        _validateField();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _validateField() {
    if (widget.validator != null) {
      final error = widget.validator!(widget.controller?.text);
      setState(() {
        _errorText = error;
        _isValid = error == null;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final accentColor = widget.accentColor ?? Theme.of(context).primaryColor;
    
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _focusAnimation.value,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: _isValid 
                        ? _colorAnimation.value ?? Colors.grey.shade300
                        : Colors.red,
                    width: _focusNode.hasFocus ? 2 : 1,
                  ),
                  boxShadow: _focusNode.hasFocus
                      ? [
                          BoxShadow(
                            color: accentColor.withValues(alpha: 0.2),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ]
                      : null,
                ),
                child: TextFormField(
                  controller: widget.controller,
                  focusNode: _focusNode,
                  validator: widget.validator,
                  keyboardType: widget.keyboardType,
                  obscureText: widget.obscureText,
                  enabled: widget.enabled,
                  maxLines: widget.maxLines,
                  maxLength: widget.maxLength,
                  inputFormatters: widget.inputFormatters,
                  onChanged: (value) {
                    widget.onChanged?.call(value);
                    if (_errorText != null) {
                      _validateField();
                    }
                  },
                  onTap: widget.onTap,
                  readOnly: widget.readOnly,
                  decoration: InputDecoration(
                    labelText: widget.label,
                    hintText: widget.hint,
                    prefixIcon: widget.prefixIcon != null
                        ? Icon(
                            widget.prefixIcon,
                            color: _focusNode.hasFocus 
                                ? accentColor 
                                : Colors.grey.shade600,
                          )
                        : null,
                    suffixIcon: widget.suffixIcon != null
                        ? IconButton(
                            icon: Icon(
                              widget.suffixIcon,
                              color: _focusNode.hasFocus 
                                  ? accentColor 
                                  : Colors.grey.shade600,
                            ),
                            onPressed: widget.onSuffixTap,
                          )
                        : null,
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 16,
                    ),
                    labelStyle: TextStyle(
                      color: _focusNode.hasFocus 
                          ? accentColor 
                          : Colors.grey.shade600,
                    ),
                    counterStyle: const TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                ),
              ),
              
              // Mensaje de error animado
              AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                height: _errorText != null ? 24 : 0,
                child: _errorText != null
                    ? Padding(
                        padding: const EdgeInsets.only(top: 4, left: 12),
                        child: Row(
                          children: [
                            Icon(
                              Icons.error_outline,
                              size: 16,
                              color: Colors.red,
                            ),
                            const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                _errorText!,
                                style: const TextStyle(
                                  color: Colors.red,
                                  fontSize: 12,
                                ),
                              ),
                            ),
                          ],
                        ),
                      )
                    : null,
              ),
            ],
          ),
        );
      },
    );
  }
}

/// Dropdown moderno con validación visual
class ModernDropdown<T> extends StatefulWidget {
  final String label;
  final T? value;
  final List<DropdownMenuItem<T>> items;
  final void Function(T?)? onChanged;
  final String? Function(T?)? validator;
  final IconData? prefixIcon;
  final bool enabled;
  final Color? accentColor;

  const ModernDropdown({
    super.key,
    required this.label,
    required this.items,
    this.value,
    this.onChanged,
    this.validator,
    this.prefixIcon,
    this.enabled = true,
    this.accentColor,
  });

  @override
  State<ModernDropdown<T>> createState() => _ModernDropdownState<T>();
}

class _ModernDropdownState<T> extends State<ModernDropdown<T>>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  
  String? _errorText;
  bool _isValid = true;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.02).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _validateField() {
    if (widget.validator != null) {
      final error = widget.validator!(widget.value);
      setState(() {
        _errorText = error;
        _isValid = error == null;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final accentColor = widget.accentColor ?? Theme.of(context).primaryColor;
    
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: _isValid ? Colors.grey.shade300 : Colors.red,
                    width: 1,
                  ),
                ),
                child: DropdownButtonFormField<T>(
                  value: widget.value,
                  items: widget.items,
                  onChanged: widget.enabled ? (value) {
                    widget.onChanged?.call(value);
                    if (_errorText != null) {
                      _validateField();
                    }
                  } : null,
                  validator: widget.validator,
                  decoration: InputDecoration(
                    labelText: widget.label,
                    prefixIcon: widget.prefixIcon != null
                        ? Icon(
                            widget.prefixIcon,
                            color: Colors.grey.shade600,
                          )
                        : null,
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 16,
                    ),
                    labelStyle: TextStyle(
                      color: Colors.grey.shade600,
                    ),
                  ),
                  dropdownColor: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  onTap: () => _animationController.forward(),
                ),
              ),
              
              // Mensaje de error animado
              AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                height: _errorText != null ? 24 : 0,
                child: _errorText != null
                    ? Padding(
                        padding: const EdgeInsets.only(top: 4, left: 12),
                        child: Row(
                          children: [
                            const Icon(
                              Icons.error_outline,
                              size: 16,
                              color: Colors.red,
                            ),
                            const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                _errorText!,
                                style: const TextStyle(
                                  color: Colors.red,
                                  fontSize: 12,
                                ),
                              ),
                            ),
                          ],
                        ),
                      )
                    : null,
              ),
            ],
          ),
        );
      },
    );
  }
}
