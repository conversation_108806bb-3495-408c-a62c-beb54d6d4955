import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math' as math;

/// Sistema de temas dinámico y adaptativo
class DynamicThemeSystem extends ChangeNotifier {
  static final DynamicThemeSystem _instance = DynamicThemeSystem._internal();
  factory DynamicThemeSystem() => _instance;
  DynamicThemeSystem._internal();

  ThemeMode _themeMode = ThemeMode.system;
  AdaptiveThemeType _adaptiveType = AdaptiveThemeType.timeOfDay;
  BusinessThemeType _businessTheme = BusinessThemeType.neutral;
  bool _isHighContrast = false;
  double _animationSpeed = 1.0;
  Color? _accentColor;

  // Getters
  ThemeMode get themeMode => _themeMode;
  AdaptiveThemeType get adaptiveType => _adaptiveType;
  BusinessThemeType get businessTheme => _businessTheme;
  bool get isHighContrast => _isHighContrast;
  double get animationSpeed => _animationSpeed;
  Color? get accentColor => _accentColor;

  /// Cambiar modo de tema
  void setThemeMode(ThemeMode mode) {
    _themeMode = mode;
    notifyListeners();
  }

  /// Configurar tema adaptativo
  void setAdaptiveType(AdaptiveThemeType type) {
    _adaptiveType = type;
    notifyListeners();
  }

  /// Configurar tema de negocio
  void setBusinessTheme(BusinessThemeType type) {
    _businessTheme = type;
    notifyListeners();
  }

  /// Toggle alto contraste
  void toggleHighContrast() {
    _isHighContrast = !_isHighContrast;
    notifyListeners();
  }

  /// Configurar velocidad de animaciones
  void setAnimationSpeed(double speed) {
    _animationSpeed = speed.clamp(0.1, 3.0);
    notifyListeners();
  }

  /// Configurar color de acento personalizado
  void setAccentColor(Color? color) {
    _accentColor = color;
    notifyListeners();
  }

  /// Obtener tema claro dinámico
  ThemeData getLightTheme() {
    final baseTheme = _getBusinessBaseTheme(false);
    return _applyDynamicModifications(baseTheme, false);
  }

  /// Obtener tema oscuro dinámico
  ThemeData getDarkTheme() {
    final baseTheme = _getBusinessBaseTheme(true);
    return _applyDynamicModifications(baseTheme, true);
  }

  /// Obtener tema base según tipo de negocio
  ThemeData _getBusinessBaseTheme(bool isDark) {
    final colorScheme = _getBusinessColorScheme(isDark);
    
    return ThemeData(
      useMaterial3: true,
      colorScheme: colorScheme,
      appBarTheme: AppBarTheme(
        backgroundColor: colorScheme.primary,
        foregroundColor: colorScheme.onPrimary,
        elevation: 0,
        systemOverlayStyle: isDark ? SystemUiOverlayStyle.light : SystemUiOverlayStyle.dark,
      ),
      cardTheme: CardTheme(
        elevation: 4,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        color: colorScheme.surface,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: colorScheme.primary,
          foregroundColor: colorScheme.onPrimary,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: colorScheme.outline),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: colorScheme.primary, width: 2),
        ),
        filled: true,
        fillColor: colorScheme.surface,
      ),
    );
  }

  /// Obtener esquema de colores según tipo de negocio
  ColorScheme _getBusinessColorScheme(bool isDark) {
    Color primaryColor;
    
    switch (_businessTheme) {
      case BusinessThemeType.success:
        primaryColor = Colors.green;
        break;
      case BusinessThemeType.warning:
        primaryColor = Colors.orange;
        break;
      case BusinessThemeType.error:
        primaryColor = Colors.red;
        break;
      case BusinessThemeType.info:
        primaryColor = Colors.blue;
        break;
      case BusinessThemeType.premium:
        primaryColor = Colors.purple;
        break;
      case BusinessThemeType.neutral:
      default:
        primaryColor = Colors.indigo;
    }

    // Usar color de acento personalizado si está configurado
    if (_accentColor != null) {
      primaryColor = _accentColor!;
    }

    // Aplicar adaptación por hora del día
    if (_adaptiveType == AdaptiveThemeType.timeOfDay) {
      primaryColor = _adaptColorByTimeOfDay(primaryColor);
    }

    return isDark
        ? ColorScheme.fromSeed(seedColor: primaryColor, brightness: Brightness.dark)
        : ColorScheme.fromSeed(seedColor: primaryColor, brightness: Brightness.light);
  }

  /// Adaptar color según hora del día
  Color _adaptColorByTimeOfDay(Color baseColor) {
    final hour = DateTime.now().hour;
    
    if (hour >= 6 && hour < 12) {
      // Mañana - colores más brillantes
      return Color.lerp(baseColor, Colors.yellow, 0.1) ?? baseColor;
    } else if (hour >= 12 && hour < 18) {
      // Tarde - colores normales
      return baseColor;
    } else if (hour >= 18 && hour < 22) {
      // Noche - colores más cálidos
      return Color.lerp(baseColor, Colors.orange, 0.1) ?? baseColor;
    } else {
      // Madrugada - colores más fríos y oscuros
      return Color.lerp(baseColor, Colors.indigo, 0.2) ?? baseColor;
    }
  }

  /// Aplicar modificaciones dinámicas al tema
  ThemeData _applyDynamicModifications(ThemeData baseTheme, bool isDark) {
    var theme = baseTheme;

    // Aplicar alto contraste
    if (_isHighContrast) {
      theme = _applyHighContrast(theme, isDark);
    }

    // Aplicar modificaciones de velocidad de animación
    theme = theme.copyWith(
      pageTransitionsTheme: PageTransitionsTheme(
        builders: {
          TargetPlatform.android: CustomPageTransitionBuilder(_animationSpeed),
          TargetPlatform.iOS: CustomPageTransitionBuilder(_animationSpeed),
          TargetPlatform.windows: CustomPageTransitionBuilder(_animationSpeed),
          TargetPlatform.macOS: CustomPageTransitionBuilder(_animationSpeed),
          TargetPlatform.linux: CustomPageTransitionBuilder(_animationSpeed),
        },
      ),
    );

    return theme;
  }

  /// Aplicar alto contraste
  ThemeData _applyHighContrast(ThemeData theme, bool isDark) {
    final colorScheme = theme.colorScheme;
    
    return theme.copyWith(
      colorScheme: colorScheme.copyWith(
        primary: isDark ? Colors.white : Colors.black,
        onPrimary: isDark ? Colors.black : Colors.white,
        secondary: isDark ? Colors.grey.shade300 : Colors.grey.shade700,
        surface: isDark ? Colors.black : Colors.white,
        onSurface: isDark ? Colors.white : Colors.black,
      ),
    );
  }

  /// Obtener tema actual basado en configuración
  ThemeData getCurrentTheme(BuildContext context) {
    final brightness = MediaQuery.of(context).platformBrightness;
    final shouldUseDark = _themeMode == ThemeMode.dark ||
        (_themeMode == ThemeMode.system && brightness == Brightness.dark);
    
    return shouldUseDark ? getDarkTheme() : getLightTheme();
  }
}

/// Tipos de tema adaptativo
enum AdaptiveThemeType {
  timeOfDay,
  businessStatus,
  seasonal,
  performance,
}

/// Tipos de tema de negocio
enum BusinessThemeType {
  neutral,
  success,
  warning,
  error,
  info,
  premium,
}

/// Constructor de transiciones de página personalizado
class CustomPageTransitionBuilder extends PageTransitionsBuilder {
  final double speedMultiplier;

  const CustomPageTransitionBuilder(this.speedMultiplier);

  @override
  Widget buildTransitions<T extends Object?>(
    PageRoute<T> route,
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    final adjustedAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: animation,
      curve: Interval(
        0.0,
        1.0,
        curve: Curves.easeInOut,
      ),
    ));

    return SlideTransition(
      position: Tween<Offset>(
        begin: const Offset(1.0, 0.0),
        end: Offset.zero,
      ).animate(adjustedAnimation),
      child: child,
    );
  }
}

/// Widget para selector de tema visual
class ThemeSelector extends StatelessWidget {
  const ThemeSelector({super.key});

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: DynamicThemeSystem(),
      builder: (context, child) {
        final themeSystem = DynamicThemeSystem();
        
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Configuración de Tema',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            // Selector de modo de tema
            _buildThemeModeSelector(themeSystem),
            const SizedBox(height: 16),
            
            // Selector de tema de negocio
            _buildBusinessThemeSelector(themeSystem),
            const SizedBox(height: 16),
            
            // Opciones adicionales
            _buildAdditionalOptions(themeSystem),
          ],
        );
      },
    );
  }

  Widget _buildThemeModeSelector(DynamicThemeSystem themeSystem) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Modo de Tema', style: TextStyle(fontWeight: FontWeight.w600)),
        const SizedBox(height: 8),
        Row(
          children: [
            _buildThemeModeChip('Claro', ThemeMode.light, themeSystem),
            const SizedBox(width: 8),
            _buildThemeModeChip('Oscuro', ThemeMode.dark, themeSystem),
            const SizedBox(width: 8),
            _buildThemeModeChip('Sistema', ThemeMode.system, themeSystem),
          ],
        ),
      ],
    );
  }

  Widget _buildThemeModeChip(String label, ThemeMode mode, DynamicThemeSystem themeSystem) {
    final isSelected = themeSystem.themeMode == mode;
    
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        if (selected) {
          themeSystem.setThemeMode(mode);
        }
      },
    );
  }

  Widget _buildBusinessThemeSelector(DynamicThemeSystem themeSystem) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Tema de Negocio', style: TextStyle(fontWeight: FontWeight.w600)),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: BusinessThemeType.values.map((type) {
            return _buildBusinessThemeChip(type, themeSystem);
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildBusinessThemeChip(BusinessThemeType type, DynamicThemeSystem themeSystem) {
    final isSelected = themeSystem.businessTheme == type;
    final label = _getBusinessThemeLabel(type);
    
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        if (selected) {
          themeSystem.setBusinessTheme(type);
        }
      },
    );
  }

  String _getBusinessThemeLabel(BusinessThemeType type) {
    switch (type) {
      case BusinessThemeType.neutral:
        return 'Neutral';
      case BusinessThemeType.success:
        return 'Éxito';
      case BusinessThemeType.warning:
        return 'Advertencia';
      case BusinessThemeType.error:
        return 'Error';
      case BusinessThemeType.info:
        return 'Información';
      case BusinessThemeType.premium:
        return 'Premium';
    }
  }

  Widget _buildAdditionalOptions(DynamicThemeSystem themeSystem) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Opciones Adicionales', style: TextStyle(fontWeight: FontWeight.w600)),
        const SizedBox(height: 8),
        
        SwitchListTile(
          title: const Text('Alto Contraste'),
          subtitle: const Text('Mejora la legibilidad'),
          value: themeSystem.isHighContrast,
          onChanged: (value) => themeSystem.toggleHighContrast(),
        ),
        
        ListTile(
          title: const Text('Velocidad de Animaciones'),
          subtitle: Slider(
            value: themeSystem.animationSpeed,
            min: 0.1,
            max: 3.0,
            divisions: 29,
            label: '${themeSystem.animationSpeed.toStringAsFixed(1)}x',
            onChanged: (value) => themeSystem.setAnimationSpeed(value),
          ),
        ),
      ],
    );
  }
}

/// Widget para preview de tema
class ThemePreview extends StatelessWidget {
  final ThemeData theme;
  final String label;

  const ThemePreview({
    super.key,
    required this.theme,
    required this.label,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 200,
      height: 300,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Theme(
        data: theme,
        child: Column(
          children: [
            Container(
              height: 60,
              decoration: BoxDecoration(
                color: theme.colorScheme.primary,
                borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
              ),
              child: Center(
                child: Text(
                  label,
                  style: TextStyle(
                    color: theme.colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            Expanded(
              child: Container(
                color: theme.colorScheme.surface,
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(12),
                        child: Text(
                          'Tarjeta de ejemplo',
                          style: TextStyle(color: theme.colorScheme.onSurface),
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: () {},
                      child: const Text('Botón'),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
