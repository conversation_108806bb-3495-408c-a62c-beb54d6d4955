import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math' as math;
import '../models/detalle_factura_model.dart';
import '../models/factura_model.dart';
import '../services/cliente_service.dart';
import '../services/modulo_service.dart';
import '../services/factura_service.dart';
import '../models/cliente_model.dart';
import '../widgets/loading_state_builder.dart';
import '../widgets/responsive_container.dart';
import '../widgets/gradient_background.dart';
import '../widgets/advanced_visual_effects.dart';

import '../utils/responsive_utils.dart';

class AgregarFacturaScreen extends StatefulWidget {
  const AgregarFacturaScreen({super.key});

  @override
  State<AgregarFacturaScreen> createState() => _AgregarFacturaScreenState();
}

class _AgregarFacturaScreenState extends State<AgregarFacturaScreen> 
    with TickerProviderStateMixin {
  String? clienteSeleccionado;
  String? moduloSeleccionado;
  final TextEditingController metodoPagoController = TextEditingController();
  List<DetalleFactura> detalles = [];

  // Variables para manejar errores y estados
  String? clienteError;
  String? metodoPagoError;
  String? moduloError;
  bool _isLoading = false;

  // Variables para suscripción
  bool _renovarSuscripcion = false;
  String _tipoSuscripcion = 'mensual';
  final TextEditingController _montoSuscripcionController =
      TextEditingController();

  // Guardamos la lista de módulos con precio base
  List<Map<String, dynamic>> modulos = [];
  
  // Controladores de animación
  late AnimationController _slideController;
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late AnimationController _progressController;
  
  // Animaciones
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _progressAnimation;
  
  // Estado de la UI
  int _currentStep = 0;
  final PageController _pageController = PageController();
  
  // Datos para autocompletado
  final List<String> _metodosPagoSugeridos = [
    'Efectivo',
    'Tarjeta de Crédito',
    'Tarjeta de Débito',
    'Transferencia Bancaria',
    'PayPal',
    'Nequi',
    'Daviplata',
    'Bancolombia',
    'PSE',
  ];
  
  // Clientes cargados
  List<Cliente> _clientes = [];
  Cliente? _clienteSeleccionadoObj;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _cargarModulos();
    _cargarClientes();
    _startEntryAnimations();
  }

  void _initializeAnimations() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeInOut,
    ));
  }

  void _startEntryAnimations() {
    Future.delayed(const Duration(milliseconds: 100), () {
      _fadeController.forward();
    });
    
    Future.delayed(const Duration(milliseconds: 200), () {
      _scaleController.forward();
    });
    
    Future.delayed(const Duration(milliseconds: 300), () {
      _slideController.forward();
    });
  }

  Future<void> _cargarClientes() async {
    try {
      final clientes = await ClienteService.obtenerClientes();
      setState(() {
        _clientes = clientes;
      });
    } catch (e) {
      // Manejar error silenciosamente
    }
  }

  @override
  void dispose() {
    _slideController.dispose();
    _fadeController.dispose();
    _scaleController.dispose();
    _progressController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  // Calcular total de la factura
  double get _totalFactura {
    return detalles.fold(0.0, (sum, item) => sum + item.precioModulo);
  }

  // Calcular subtotal (sin descuentos)
  double get _subtotal {
    double subtotal = 0;
    for (int i = 0; i < detalles.length; i++) {
      final detalle = detalles[i];
      final moduloEncontrado = modulos.firstWhere(
        (m) => m["Nombre"] == detalle.modulo,
        orElse: () => {"Precio": 80000.0},
      );
      final precioBase = moduloEncontrado["Precio"] is double
          ? moduloEncontrado["Precio"] as double
          : double.tryParse(moduloEncontrado["Precio"].toString()) ?? 80000.0;
      
      subtotal += precioBase * detalle.cantidad;
    }
    return subtotal;
  }

  // Calcular descuento total
  double get _descuentoTotal {
    return _subtotal - _totalFactura;
  }

  // Calcular progreso de completado
  double get _progressValue {
    int completedSteps = 0;
    
    // Paso 1: Cliente seleccionado
    if (clienteSeleccionado != null) completedSteps++;
    
    // Paso 2: Método de pago
    if (metodoPagoController.text.isNotEmpty) completedSteps++;
    
    // Paso 3: Al menos un módulo agregado
    if (detalles.isNotEmpty) completedSteps++;
    
    return completedSteps / 3.0;
  }

  // Obtener paso actual
  int get _getCurrentStep {
    if (clienteSeleccionado == null) return 0;
    if (metodoPagoController.text.isEmpty) return 1;
    if (detalles.isEmpty) return 2;
    return 3;
  }

  // Lista de pasos
  List<String> get _steps => [
    'Seleccionar Cliente',
    'Método de Pago',
    'Agregar Módulos',
    'Finalizar',
  ];

  void _updateProgress() {
    final newProgress = _progressValue;
    _progressController.animateTo(newProgress);
    
    setState(() {
      _currentStep = _getCurrentStep;
    });
  }

  // Actualizar monto de suscripción cuando cambia el total
  void _actualizarMontoSuscripcion() {
    if (_renovarSuscripcion) {
      _montoSuscripcionController.text = _totalFactura.toStringAsFixed(2);
    }
  }

  Future<void> _cargarModulos() async {
    final modulosDB = await ModuloService.obtenerModulos();
    setState(() {
      modulos = modulosDB;
    });
  }

  Future<List<Cliente>> _obtenerClientes() async {
    return await ClienteService.obtenerClientes();
  }

  void _agregarDetalle() {
    if (moduloSeleccionado == null) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text("Selecciona un módulo antes de agregar."),
            backgroundColor: Colors.orange,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
      return;
    }

    // Buscar precio base del módulo seleccionado
    final moduloEncontrado = modulos.firstWhere(
      (m) => m["Nombre"] == moduloSeleccionado,
      orElse: () => {"Nombre": moduloSeleccionado, "Precio": 80000.0},
    );
    final precioBase = moduloEncontrado["Precio"] is double
        ? moduloEncontrado["Precio"] as double
        : double.tryParse(moduloEncontrado["Precio"].toString()) ?? 80000.0;

    // Determinar posición del módulo nuevo (índice + 1)
    int posicionModulo = detalles.length + 1;

    // Aplicar descuento según posición
    double precioModuloConDescuento;
    if (posicionModulo == 1) {
      precioModuloConDescuento = precioBase; // 100%
    } else if (posicionModulo == 2) {
      precioModuloConDescuento = precioBase * 0.75; // 25% de descuento
    } else {
      precioModuloConDescuento = precioBase * 0.25; // 75% de descuento
    }

    if (mounted) {
      setState(() {
        detalles.add(DetalleFactura(
          idDetalle: DateTime.now().millisecondsSinceEpoch.toString(),
          idFactura: '',
          cantidad: 1,
          modulo: moduloSeleccionado!,
          suscripcion: true,
          precioModulo: precioModuloConDescuento,
        ));
        // Actualizar monto de suscripción automáticamente
        _actualizarMontoSuscripcion();
        // Actualizar progreso
        _updateProgress();
      });

      // Feedback háptico
      HapticFeedback.lightImpact();

      // Mostrar snackbar de éxito
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text("Módulo '$moduloSeleccionado' agregado"),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  void _eliminarDetalle(int index) {
    if (mounted) {
      setState(() {
        detalles.removeAt(index);
        _actualizarMontoSuscripcion();
        _updateProgress();
      });

      HapticFeedback.lightImpact();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text("Módulo eliminado"),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  void _validateMetodoPago(String value) {
    setState(() {
      if (value.isEmpty) {
        metodoPagoError = 'El método de pago es requerido';
      } else if (value.length < 3) {
        metodoPagoError = 'Método de pago debe tener al menos 3 caracteres';
      } else {
        metodoPagoError = null;
      }
    });
    _updateProgress();
  }

  void _onClienteChanged(String? clienteId) {
    setState(() {
      clienteSeleccionado = clienteId;
      clienteError = null;

      // Encontrar el objeto cliente
      if (clienteId != null) {
        _clienteSeleccionadoObj = _clientes.firstWhere(
          (c) => c.idCliente == clienteId,
          orElse: () => _clientes.first,
        );
      } else {
        _clienteSeleccionadoObj = null;
      }
    });
    _updateProgress();
    HapticFeedback.selectionClick();
  }

  void _onModuloChanged(String? modulo) {
    setState(() {
      moduloSeleccionado = modulo;
    });
    HapticFeedback.selectionClick();
  }

  bool _validarFormulario() {
    bool esValido = true;

    setState(() {
      // Validar cliente
      if (clienteSeleccionado == null) {
        clienteError = 'Selecciona un cliente';
        esValido = false;
      } else {
        clienteError = null;
      }

      // Validar método de pago
      if (metodoPagoController.text.isEmpty) {
        metodoPagoError = 'El método de pago es requerido';
        esValido = false;
      } else {
        metodoPagoError = null;
      }

      // Validar que haya al menos un detalle
      if (detalles.isEmpty) {
        moduloError = 'Agrega al menos un módulo';
        esValido = false;
      } else {
        moduloError = null;
      }
    });

    return esValido;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: GradientBackground(
        child: Stack(
          children: [
            // Partículas de fondo
            FloatingParticles(
              particleCount: 25,
              particleColor: Theme.of(context).primaryColor.withValues(alpha: 0.1),
              child: const SizedBox.expand(),
            ),

            // Contenido principal
            SafeArea(
              child: Column(
                children: [
                  _buildHeader(),
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        children: [
                          _buildProgressSection(),
                          const SizedBox(height: 24),
                          _buildRealtimeSummary(),
                          const SizedBox(height: 24),
                          _buildMainForm(),
                          const SizedBox(height: 100), // Espacio para FAB
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: _buildAnimatedFAB(),
    );
  }

  Widget _buildHeader() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            // Botón de regreso
            ScaleTransition(
              scale: _scaleAnimation,
              child: Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).cardColor,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.arrow_back),
                  tooltip: 'Volver',
                ),
              ),
            ),

            const SizedBox(width: 16),

            // Título
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Nueva Factura',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Crea una factura paso a paso',
                    style: TextStyle(
                      color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressSection() {
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.assignment,
                    color: Theme.of(context).primaryColor,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Progreso de Factura',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Barra de progreso
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text('Completado'),
                  Text(
                    '${(_progressValue * 100).toInt()}%',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              LinearProgressIndicator(
                value: _progressValue,
                backgroundColor: Colors.grey.withValues(alpha: 0.2),
                valueColor: AlwaysStoppedAnimation<Color>(
                  Theme.of(context).primaryColor,
                ),
                minHeight: 6,
              ),
              const SizedBox(height: 16),

              // Lista de pasos
              ..._steps.asMap().entries.map((entry) {
                final index = entry.key;
                final step = entry.value;
                final isCompleted = index < _currentStep;
                final isCurrent = index == _currentStep;

                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4),
                  child: Row(
                    children: [
                      Container(
                        width: 20,
                        height: 20,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: isCompleted
                              ? Colors.green
                              : isCurrent
                                  ? Theme.of(context).primaryColor
                                  : Colors.grey.withValues(alpha: 0.3),
                        ),
                        child: isCompleted
                            ? const Icon(Icons.check, color: Colors.white, size: 14)
                            : isCurrent
                                ? const Icon(Icons.edit, color: Colors.white, size: 14)
                                : null,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          step,
                          style: TextStyle(
                            color: isCompleted || isCurrent
                                ? Theme.of(context).textTheme.bodyMedium?.color
                                : Colors.grey,
                            fontWeight: isCurrent ? FontWeight.w600 : FontWeight.normal,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRealtimeSummary() {
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Theme.of(context).primaryColor,
                Theme.of(context).primaryColor.withValues(alpha: 0.8),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
                blurRadius: 15,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(Icons.receipt_long, color: Colors.white, size: 24),
                  const SizedBox(width: 12),
                  const Text(
                    'Resumen de Factura',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  if (_clienteSeleccionadoObj?.nombre != null)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        _clienteSeleccionadoObj!.nombre,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 20),

              // Estadísticas
              Row(
                children: [
                  Expanded(
                    child: _buildStatItem(
                      'Módulos',
                      detalles.length.toString(),
                      Icons.apps,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildStatItem(
                      'Subtotal',
                      '\$${_subtotal.toStringAsFixed(0)}',
                      Icons.calculate,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              if (_descuentoTotal > 0) ...[
                Row(
                  children: [
                    const Icon(Icons.local_offer, color: Colors.white70, size: 16),
                    const SizedBox(width: 8),
                    Text(
                      'Descuento aplicado: -\$${_descuentoTotal.toStringAsFixed(2)}',
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
              ],

              // Total
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'TOTAL',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        letterSpacing: 1.2,
                      ),
                    ),
                    Text(
                      '\$${_totalFactura.toStringAsFixed(2)}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: Colors.white70, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            label,
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMainForm() {
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            _buildClienteSection(),
            const SizedBox(height: 20),
            _buildMetodoPagoSection(),
            const SizedBox(height: 20),
            _buildModulosSection(),
            const SizedBox(height: 20),
            _buildDetallesSection(),
            const SizedBox(height: 20),
            _buildSuscripcionSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildClienteSection() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.person,
                  color: Theme.of(context).primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'Seleccionar Cliente',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            FutureBuilder<List<Cliente>>(
              future: _obtenerClientes(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (snapshot.hasError || !snapshot.hasData) {
                  return const Text('Error al cargar clientes');
                }

                final clientes = snapshot.data!;
                return DropdownButtonFormField<String>(
                  value: clienteSeleccionado,
                  decoration: InputDecoration(
                    labelText: 'Cliente',
                    hintText: 'Selecciona un cliente',
                    errorText: clienteError,
                    prefixIcon: const Icon(Icons.person_outline),
                    filled: true,
                    fillColor: Theme.of(context).scaffoldBackgroundColor.withValues(alpha: 0.5),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide.none,
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: Theme.of(context).primaryColor,
                        width: 2,
                      ),
                    ),
                  ),
                  items: clientes.map((cliente) {
                    return DropdownMenuItem<String>(
                      value: cliente.idCliente,
                      child: Text(
                        '${cliente.nombre} - ${cliente.email}',
                        overflow: TextOverflow.ellipsis,
                      ),
                    );
                  }).toList(),
                  onChanged: _onClienteChanged,
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetodoPagoSection() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.payment,
                  color: Theme.of(context).primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'Método de Pago',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            TextFormField(
              controller: metodoPagoController,
              decoration: InputDecoration(
                labelText: 'Método de Pago',
                hintText: 'Ej: Efectivo, Tarjeta, Transferencia...',
                errorText: metodoPagoError,
                prefixIcon: const Icon(Icons.payment),
                filled: true,
                fillColor: Theme.of(context).scaffoldBackgroundColor.withValues(alpha: 0.5),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: Theme.of(context).primaryColor,
                    width: 2,
                  ),
                ),
              ),
              onChanged: _validateMetodoPago,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModulosSection() {
    return Column(
      children: [
        Container(
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Icon(
                      Icons.apps,
                      color: Theme.of(context).primaryColor,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Seleccionar Módulo',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),

              // Grid de módulos
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio: 1.2,
                    crossAxisSpacing: 12,
                    mainAxisSpacing: 12,
                  ),
                  itemCount: modulos.length,
                  itemBuilder: (context, index) {
                    final modulo = modulos[index];
                    final nombre = modulo['Nombre'] as String;
                    final precio = modulo['Precio'] as double;
                    final isSelected = moduloSeleccionado == nombre;

                    return GestureDetector(
                      onTap: () => _onModuloChanged(nombre),
                      child: AnimatedContainer(
                        duration: const Duration(milliseconds: 200),
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: isSelected
                              ? Theme.of(context).primaryColor.withValues(alpha: 0.1)
                              : Colors.grey.withValues(alpha: 0.05),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: isSelected
                                ? Theme.of(context).primaryColor
                                : Colors.grey.withValues(alpha: 0.2),
                            width: isSelected ? 2 : 1,
                          ),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.extension,
                              color: isSelected
                                  ? Theme.of(context).primaryColor
                                  : Colors.grey,
                              size: 32,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              nombre,
                              style: TextStyle(
                                fontWeight: FontWeight.w600,
                                fontSize: 14,
                                color: isSelected
                                    ? Theme.of(context).primaryColor
                                    : Theme.of(context).textTheme.bodyMedium?.color,
                              ),
                              textAlign: TextAlign.center,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              '\$${precio.toStringAsFixed(0)}',
                              style: TextStyle(
                                fontSize: 12,
                                color: isSelected
                                    ? Theme.of(context).primaryColor
                                    : Colors.grey,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),

              const SizedBox(height: 16),
            ],
          ),
        ),

        if (moduloError != null)
          Container(
            margin: const EdgeInsets.only(top: 8),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.red.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
            ),
            child: Row(
              children: [
                const Icon(Icons.error, color: Colors.red, size: 16),
                const SizedBox(width: 8),
                Text(
                  moduloError!,
                  style: const TextStyle(color: Colors.red, fontSize: 14),
                ),
              ],
            ),
          ),

        const SizedBox(height: 16),

        // Botón para agregar módulo
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _agregarDetalle,
            icon: const Icon(Icons.add),
            label: const Text('Agregar Módulo'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 4,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDetallesSection() {
    if (detalles.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(40),
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          children: [
            Icon(
              Icons.shopping_cart_outlined,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'No hay módulos agregados',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Selecciona un módulo y haz clic en "Agregar Módulo"',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Icon(
                  Icons.list_alt,
                  color: Theme.of(context).primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'Módulos Agregados',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${detalles.length} módulo${detalles.length != 1 ? 's' : ''}',
                    style: TextStyle(
                      color: Theme.of(context).primaryColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Lista de módulos
          ...detalles.asMap().entries.map((entry) {
            final index = entry.key;
            final detalle = entry.value;
            final isLast = index == detalles.length - 1;

            return Container(
              margin: EdgeInsets.only(
                left: 20,
                right: 20,
                bottom: isLast ? 20 : 12,
              ),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).scaffoldBackgroundColor.withValues(alpha: 0.5),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.grey.withValues(alpha: 0.2),
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.extension,
                      color: Theme.of(context).primaryColor,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          detalle.modulo,
                          style: const TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Text(
                              'Cantidad: ${detalle.cantidad}',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey.shade600,
                              ),
                            ),
                            const SizedBox(width: 16),
                            if (index > 0)
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                decoration: BoxDecoration(
                                  color: Colors.green.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  index == 1 ? '25% OFF' : '75% OFF',
                                  style: const TextStyle(
                                    fontSize: 10,
                                    color: Colors.green,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        '\$${detalle.precioModulo.toStringAsFixed(0)}',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      GestureDetector(
                        onTap: () => _eliminarDetalle(index),
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.red.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: const Icon(
                            Icons.delete,
                            color: Colors.red,
                            size: 16,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildSuscripcionSection() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.autorenew,
                  color: Theme.of(context).primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'Configuración de Suscripción',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Switch para renovar suscripción
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).scaffoldBackgroundColor.withValues(alpha: 0.5),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.refresh,
                    color: _renovarSuscripcion
                        ? Theme.of(context).primaryColor
                        : Colors.grey,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Renovar Suscripción',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: _renovarSuscripcion
                                ? Theme.of(context).textTheme.bodyLarge?.color
                                : Colors.grey,
                          ),
                        ),
                        Text(
                          'Activar renovación automática',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Switch(
                    value: _renovarSuscripcion,
                    onChanged: (value) {
                      setState(() {
                        _renovarSuscripcion = value;
                        _actualizarMontoSuscripcion();
                      });
                      HapticFeedback.selectionClick();
                    },
                    activeColor: Theme.of(context).primaryColor,
                  ),
                ],
              ),
            ),

            if (_renovarSuscripcion) ...[
              const SizedBox(height: 16),

              // Tipo de suscripción
              DropdownButtonFormField<String>(
                value: _tipoSuscripcion,
                decoration: InputDecoration(
                  labelText: 'Tipo de Suscripción',
                  prefixIcon: const Icon(Icons.schedule),
                  filled: true,
                  fillColor: Theme.of(context).scaffoldBackgroundColor.withValues(alpha: 0.5),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                ),
                items: const [
                  DropdownMenuItem(value: 'mensual', child: Text('Mensual')),
                  DropdownMenuItem(value: 'trimestral', child: Text('Trimestral')),
                  DropdownMenuItem(value: 'semestral', child: Text('Semestral')),
                  DropdownMenuItem(value: 'anual', child: Text('Anual')),
                ],
                onChanged: (value) {
                  setState(() {
                    _tipoSuscripcion = value!;
                  });
                  HapticFeedback.selectionClick();
                },
              ),

              const SizedBox(height: 16),

              // Monto de suscripción
              TextFormField(
                controller: _montoSuscripcionController,
                decoration: InputDecoration(
                  labelText: 'Monto de Suscripción',
                  prefixIcon: const Icon(Icons.attach_money),
                  filled: true,
                  fillColor: Theme.of(context).scaffoldBackgroundColor.withValues(alpha: 0.5),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                ),
                keyboardType: TextInputType.number,
                readOnly: true,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAnimatedFAB() {
    return ScaleTransition(
      scale: _scaleAnimation,
      child: FloatingActionButton.extended(
        onPressed: _isLoading ? null : _guardarFactura,
        icon: _isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Icon(Icons.save),
        label: Text(_isLoading ? 'Guardando...' : 'Guardar Factura'),
        backgroundColor: _progressValue >= 1.0
            ? Colors.green
            : Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 8,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
    );
  }

  Future<void> _guardarFactura() async {
    if (!_validarFormulario()) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Por favor completa todos los campos requeridos'),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Crear la factura
      final factura = Factura(
        idFactura: DateTime.now().millisecondsSinceEpoch.toString(),
        cliente: clienteSeleccionado!,
        metodoPago: metodoPagoController.text,
        total: _totalFactura,
        fecha: DateTime.now(),
        estado: 'pendiente',
        modulos: detalles.map((d) => d.modulo).toList(),
      );

      // Guardar factura con detalles
      await FacturaService.insertarFactura(factura, detalles);

      // Mostrar éxito
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Factura creada exitosamente'),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );

        // Regresar a la pantalla anterior
        Navigator.pop(context, true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error al crear factura: $e'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
