import 'package:flutter/material.dart';
import '../models/detalle_factura_model.dart';
import '../models/factura_model.dart';
import '../services/cliente_service.dart';
import '../services/modulo_service.dart';
import '../services/factura_service.dart';
import '../models/cliente_model.dart';
import '../widgets/loading_state_builder.dart';
import '../widgets/responsive_container.dart';
import '../utils/responsive_utils.dart';

class AgregarFacturaScreen extends StatefulWidget {
  const AgregarFacturaScreen({super.key});

  @override
  State<AgregarFacturaScreen> createState() => _AgregarFacturaScreenState();
}

class _AgregarFacturaScreenState extends State<AgregarFacturaScreen> {
  String? clienteSeleccionado;
  String? moduloSeleccionado;
  final TextEditingController metodoPagoController = TextEditingController();
  List<DetalleFactura> detalles = [];

  // Variables para manejar errores y estados
  String? clienteError;
  String? metodoPagoError;
  String? moduloError;
  bool _isLoading = false;

  // Variables para suscripción
  bool _renovarSuscripcion = false;
  String _tipoSuscripcion = 'mensual';
  final TextEditingController _montoSuscripcionController =
      TextEditingController();

  // Guardamos la lista de módulos con precio base
  List<Map<String, dynamic>> modulos = [];

  @override
  void initState() {
    super.initState();
    _cargarModulos();
  }

  // Calcular total de la factura
  double get _totalFactura {
    return detalles.fold(0.0, (sum, item) => sum + item.precioModulo);
  }

  // Actualizar monto de suscripción cuando cambia el total
  void _actualizarMontoSuscripcion() {
    if (_renovarSuscripcion) {
      _montoSuscripcionController.text = _totalFactura.toStringAsFixed(2);
    }
  }

  Future<void> _cargarModulos() async {
    final modulosDB = await ModuloService.obtenerModulos();
    setState(() {
      modulos = modulosDB;
    });
  }

  Future<List<Cliente>> _obtenerClientes() async {
    return await ClienteService.obtenerClientes();
  }

  void _agregarDetalle() {
    if (moduloSeleccionado == null) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text("Selecciona un módulo antes de agregar.")),
        );
      }
      return;
    }

    // Buscar precio base del módulo seleccionado
    final moduloEncontrado = modulos.firstWhere(
      (m) => m["Nombre"] == moduloSeleccionado,
      orElse: () => {"Nombre": moduloSeleccionado, "Precio": 80000.0},
    );
    final precioBase = moduloEncontrado["Precio"] is double
        ? moduloEncontrado["Precio"] as double
        : double.tryParse(moduloEncontrado["Precio"].toString()) ?? 80000.0;

    // Determinar posición del módulo nuevo (índice + 1)
    int posicionModulo = detalles.length + 1;

    // Aplicar descuento según posición
    double precioModuloConDescuento;
    if (posicionModulo == 1) {
      precioModuloConDescuento = precioBase; // 100%
    } else if (posicionModulo == 2) {
      precioModuloConDescuento = precioBase * 0.75; // 25% de descuento
    } else {
      precioModuloConDescuento = precioBase * 0.25; // 75% de descuento
    }

    if (mounted) {
      setState(() {
        detalles.add(DetalleFactura(
          idDetalle: DateTime.now().millisecondsSinceEpoch.toString(),
          idFactura: '',
          cantidad: 1,
          modulo: moduloSeleccionado!,
          suscripcion: true,
          precioModulo: precioModuloConDescuento,
        ));
        // Actualizar monto de suscripción automáticamente
        _actualizarMontoSuscripcion();
      });
    }
  }

  void _actualizarCantidad(int index, int nuevaCantidad) {
    if (nuevaCantidad < 1) return; // No permitimos menos de 1

    final detalle = detalles[index];

    // Buscar precio base del módulo
    final moduloEncontrado = modulos.firstWhere(
      (m) => m["Nombre"] == detalle.modulo,
      orElse: () => {"Precio": 80000.0},
    );
    final precioBase = moduloEncontrado["Precio"] is double
        ? moduloEncontrado["Precio"] as double
        : double.tryParse(moduloEncontrado["Precio"].toString()) ?? 80000.0;

    // Posición del módulo dentro de la lista detalles + 1
    int posicionModulo = index + 1;

    // Factor descuento según posición del módulo
    double descuentoFactor;
    if (posicionModulo == 1) {
      descuentoFactor = 1.0; // sin descuento
    } else if (posicionModulo == 2) {
      descuentoFactor = 0.75; // 25% menos
    } else {
      descuentoFactor = 0.25; // 75% menos
    }

    // Aplicar descuento también por cantidad
    // Suponiendo que para cantidad > 1 se aplica el mismo descuento al total
    double precioCalculado;

    if (nuevaCantidad == 1) {
      precioCalculado = precioBase * descuentoFactor;
    } else if (nuevaCantidad == 2) {
      precioCalculado = precioBase *
          descuentoFactor *
          nuevaCantidad *
          0.75; // 25% menos adicional por cantidad
    } else {
      precioCalculado = precioBase *
          descuentoFactor *
          nuevaCantidad *
          0.25; // 75% menos adicional por cantidad
    }

    setState(() {
      detalles[index].cantidad = nuevaCantidad;
      detalles[index].precioModulo = precioCalculado;
      // Actualizar monto de suscripción automáticamente
      _actualizarMontoSuscripcion();
    });
  }

  void _validateMetodoPago(String value) {
    setState(() {
      if (value.isEmpty) {
        metodoPagoError = 'El método de pago es requerido';
      } else if (value.length < 3) {
        metodoPagoError = 'Método de pago debe tener al menos 3 caracteres';
      } else {
        metodoPagoError = null;
      }
    });
  }

  bool _validateForm() {
    bool isValid = true;

    if (clienteSeleccionado == null) {
      setState(() {
        clienteError = 'Debe seleccionar un cliente';
      });
      isValid = false;
    }

    if (metodoPagoController.text.isEmpty) {
      setState(() {
        metodoPagoError = 'El método de pago es requerido';
      });
      isValid = false;
    }

    if (detalles.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text("Debe agregar al menos un módulo a la factura.")),
      );
      isValid = false;
    }

    return isValid;
  }

  Future<void> _actualizarSuscripcionCliente() async {
    if (!_renovarSuscripcion || clienteSeleccionado == null) return;

    try {
      // Obtener cliente actual
      final clientes = await ClienteService.obtenerClientes();
      final cliente =
          clientes.firstWhere((c) => c.idCliente == clienteSeleccionado);

      // Calcular nueva fecha de vencimiento
      DateTime fechaInicio = DateTime.now();
      DateTime fechaVencimiento;

      switch (_tipoSuscripcion) {
        case 'mensual':
          fechaVencimiento = DateTime(
              fechaInicio.year, fechaInicio.month + 1, fechaInicio.day);
          break;
        case 'trimestral':
          fechaVencimiento = DateTime(
              fechaInicio.year, fechaInicio.month + 3, fechaInicio.day);
          break;
        case 'semestral':
          fechaVencimiento = DateTime(
              fechaInicio.year, fechaInicio.month + 6, fechaInicio.day);
          break;
        case 'anual':
          fechaVencimiento = DateTime(
              fechaInicio.year + 1, fechaInicio.month, fechaInicio.day);
          break;
        default:
          fechaVencimiento = DateTime(
              fechaInicio.year, fechaInicio.month + 1, fechaInicio.day);
      }

      // Crear cliente actualizado con suscripción
      final clienteActualizado = cliente.copyWith(
        fechaInicioSuscripcion: fechaInicio,
        fechaVencimientoSuscripcion: fechaVencimiento,
        tipoSuscripcion: _tipoSuscripcion,
        montoSuscripcion:
            double.tryParse(_montoSuscripcionController.text) ?? 0.0,
        estadoSuscripcion: 'activa',
      );

      // Actualizar cliente en Firebase
      await ClienteService.actualizarCliente(clienteActualizado);

      // print('✅ Suscripción actualizada para cliente: ${cliente.nombre}');
      // print('📅 Nueva fecha de vencimiento: ${fechaVencimiento.toString()}');
    } catch (e) {
      // print('❌ Error actualizando suscripción: $e');
      // No fallar la factura por error en suscripción
    }
  }

  Future<void> _guardarFactura() async {
    if (!_validateForm()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final total = detalles.fold(0.0, (sum, item) => sum + item.precioModulo);

      // Crear ID único para la factura
      final idFactura = 'FAC_${DateTime.now().millisecondsSinceEpoch}';

      // Crear factura
      final factura = Factura(
        idFactura: idFactura,
        cliente: clienteSeleccionado!,
        metodoPago: metodoPagoController.text.trim(),
        total: total,
        comprobante: '',
      );

      // Actualizar IDs de detalles
      for (var detalle in detalles) {
        detalle.idFactura = idFactura;
      }

      // Insertar factura con detalles
      await FacturaService.insertarFactura(factura, detalles);

      // Actualizar suscripción si está marcada
      await _actualizarSuscripcionCliente();

      if (!mounted) return;

      String mensaje = "Factura guardada correctamente.";
      if (_renovarSuscripcion) {
        mensaje += " Suscripción renovada.";
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(mensaje)),
      );

      // Regresar a la pantalla anterior con resultado
      Navigator.of(context).pop(true); // true indica que se creó una factura

      setState(() {
        clienteSeleccionado = null;
        moduloSeleccionado = null;
        metodoPagoController.clear();
        detalles.clear();
        clienteError = null;
        metodoPagoError = null;
        _renovarSuscripcion = false;
        _tipoSuscripcion = 'mensual';
        _montoSuscripcionController.clear();
      });
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text("Error al guardar factura: $e")),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final total = detalles.fold(0.0, (sum, item) => sum + item.precioModulo);

    return Scaffold(
      appBar: AppBar(
        title: ResponsiveText(
          "Agregar Factura",
          mobileFontSize: 18,
          tabletFontSize: 20,
          desktopFontSize: 22,
          fontWeight: FontWeight.w600,
        ),
        centerTitle: ResponsiveUtils.isMobile(context),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.indigo, Colors.teal],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: ResponsiveContainer(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ResponsiveText(
                  "Datos de la Factura",
                  mobileFontSize: 18,
                  tabletFontSize: 20,
                  desktopFontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                ResponsiveSpacing(mobile: 10, tablet: 16, desktop: 20),
                LoadingStateBuilder<List<Cliente>>(
                  future: _obtenerClientes(),
                  emptyMessage: "No hay clientes disponibles",
                  errorMessage: "Error al cargar clientes",
                  customEmptyWidget: const Center(
                    child: Text(
                      "No hay clientes disponibles",
                      style: TextStyle(color: Colors.white),
                    ),
                  ),
                  customLoadingWidget: const Center(
                    child: CircularProgressIndicator(color: Colors.white),
                  ),
                  customErrorWidget: const Center(
                    child: Text(
                      "Error al cargar clientes",
                      style: TextStyle(color: Colors.redAccent),
                    ),
                  ),
                  builder: (context, clientes) {
                    return DropdownButtonFormField<String>(
                      value: clienteSeleccionado,
                      decoration: InputDecoration(
                        labelText: "Seleccionar Cliente",
                        labelStyle: const TextStyle(color: Colors.white),
                        filled: true,
                        fillColor: Colors.white24,
                        errorText: clienteError,
                        errorStyle: const TextStyle(color: Colors.redAccent),
                        prefixIcon:
                            const Icon(Icons.person, color: Colors.white70),
                        border: const OutlineInputBorder(),
                      ),
                      dropdownColor: Colors.white,
                      items: clientes.map((cliente) {
                        return DropdownMenuItem(
                          value: cliente.idCliente,
                          child: Text(cliente.nombre),
                        );
                      }).toList(),
                      validator: (value) {
                        if (value == null) {
                          return 'Debe seleccionar un cliente';
                        }
                        return null;
                      },
                      onChanged: (value) {
                        if (mounted) {
                          setState(() {
                            clienteSeleccionado = value;
                            clienteError = null;
                          });
                        }
                      },
                    );
                  },
                ),
                const SizedBox(height: 10),
                TextFormField(
                  controller: metodoPagoController,
                  style: const TextStyle(color: Colors.white),
                  decoration: InputDecoration(
                    labelText: "Método de Pago",
                    labelStyle: const TextStyle(color: Colors.white),
                    filled: true,
                    fillColor: Colors.white24,
                    errorText: metodoPagoError,
                    errorStyle: const TextStyle(color: Colors.redAccent),
                    prefixIcon:
                        const Icon(Icons.payment, color: Colors.white70),
                    border: const OutlineInputBorder(),
                    hintText: "Ej: Efectivo, Tarjeta, Transferencia",
                    hintStyle: const TextStyle(color: Colors.white54),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'El método de pago es requerido';
                    }
                    if (value.length < 3) {
                      return 'Método de pago debe tener al menos 3 caracteres';
                    }
                    return null;
                  },
                  onChanged: _validateMetodoPago,
                ),
                const SizedBox(height: 10),
                DropdownButtonFormField<String>(
                  value: modulos.any((m) => m["Nombre"] == moduloSeleccionado)
                      ? moduloSeleccionado
                      : null,
                  decoration: const InputDecoration(
                    labelText: "Seleccionar Módulo",
                    labelStyle: TextStyle(color: Colors.white),
                    filled: true,
                    fillColor: Colors.white24,
                  ),
                  dropdownColor: Colors.white,
                  items: modulos
                      .map((modulo) => modulo["Nombre"] as String)
                      .toSet() // Eliminar duplicados
                      .map((nombre) {
                    return DropdownMenuItem<String>(
                      value: nombre,
                      child: Text(nombre),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (mounted) {
                      setState(() {
                        moduloSeleccionado = value;
                      });
                    }
                  },
                ),
                ResponsiveSpacing(mobile: 10, tablet: 16, desktop: 20),
                ResponsiveButton(
                  onPressed: _agregarDetalle,
                  isFullWidth: ResponsiveUtils.isMobile(context),
                  child: const Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.add),
                      SizedBox(width: 8),
                      Text("Agregar Módulo"),
                    ],
                  ),
                ),
                ResponsiveSpacing(mobile: 16, tablet: 20, desktop: 24),
                ResponsiveText(
                  "Detalles:",
                  mobileFontSize: 18,
                  tabletFontSize: 20,
                  desktopFontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                ...detalles.asMap().entries.map((entry) {
                  int index = entry.key;
                  DetalleFactura detalle = entry.value;
                  return Card(
                    margin: EdgeInsets.symmetric(
                      vertical: ResponsiveUtils.getAdaptiveSpacing(context,
                          mobile: 4, tablet: 6, desktop: 8),
                    ),
                    child: ListTile(
                      contentPadding: ResponsiveUtils.getAdaptivePadding(
                        context,
                        mobile: 12,
                        tablet: 16,
                        desktop: 20,
                      ),
                      title: ResponsiveText(
                        detalle.modulo,
                        mobileFontSize: 16,
                        tabletFontSize: 18,
                        desktopFontSize: 20,
                        fontWeight: FontWeight.w600,
                      ),
                      subtitle: ResponsiveText(
                        "Cantidad: ${detalle.cantidad} - Precio: \$${detalle.precioModulo.toStringAsFixed(2)}",
                        mobileFontSize: 14,
                        tabletFontSize: 15,
                        desktopFontSize: 16,
                      ),
                      trailing: ResponsiveUtils.isMobile(context)
                          ? Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    IconButton(
                                      icon: const Icon(Icons.remove),
                                      onPressed: () {
                                        _actualizarCantidad(
                                            index, detalle.cantidad - 1);
                                      },
                                    ),
                                    Text(detalle.cantidad.toString()),
                                    IconButton(
                                      icon: const Icon(Icons.add),
                                      onPressed: () {
                                        _actualizarCantidad(
                                            index, detalle.cantidad + 1);
                                      },
                                    ),
                                  ],
                                ),
                              ],
                            )
                          : Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                IconButton(
                                  icon: const Icon(Icons.remove),
                                  onPressed: () {
                                    _actualizarCantidad(
                                        index, detalle.cantidad - 1);
                                  },
                                ),
                                Text(detalle.cantidad.toString()),
                                IconButton(
                                  icon: const Icon(Icons.add),
                                  onPressed: () {
                                    _actualizarCantidad(
                                        index, detalle.cantidad + 1);
                                  },
                                ),
                              ],
                            ),
                    ),
                  );
                }),
                ResponsiveSpacing(mobile: 10, tablet: 16, desktop: 20),
                ResponsiveText(
                  "Total: \$${total.toStringAsFixed(2)}",
                  mobileFontSize: 18,
                  tabletFontSize: 20,
                  desktopFontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  textAlign: TextAlign.center,
                ),
                ResponsiveSpacing(mobile: 20, tablet: 24, desktop: 32),

                // Sección de Suscripción
                Card(
                  color: Colors.white.withValues(alpha: 0.1),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            const Icon(Icons.subscriptions,
                                color: Colors.white),
                            const SizedBox(width: 8),
                            ResponsiveText(
                              'Gestión de Suscripción',
                              mobileFontSize: 16,
                              tabletFontSize: 18,
                              desktopFontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),

                        // Switch para renovar suscripción
                        SwitchListTile(
                          title: const Text(
                            '¿Esta factura renueva/activa suscripción?',
                            style: TextStyle(color: Colors.white),
                          ),
                          subtitle: const Text(
                            'Al activar, se renovará automáticamente la suscripción del cliente',
                            style: TextStyle(color: Colors.white70),
                          ),
                          value: _renovarSuscripcion,
                          onChanged: (value) {
                            setState(() {
                              _renovarSuscripcion = value;
                              if (value) {
                                // Configurar valores por defecto
                                _tipoSuscripcion = 'mensual';
                                // Usar el total de la factura como monto de suscripción
                                _montoSuscripcionController.text =
                                    _totalFactura.toStringAsFixed(2);
                              } else {
                                _montoSuscripcionController.clear();
                              }
                            });
                          },
                          activeColor: Colors.white,
                          activeTrackColor: Colors.white.withValues(alpha: 0.3),
                        ),

                        if (_renovarSuscripcion) ...[
                          const SizedBox(height: 16),

                          // Tipo de suscripción
                          DropdownButtonFormField<String>(
                            value: _tipoSuscripcion,
                            decoration: const InputDecoration(
                              labelText: 'Tipo de Suscripción',
                              labelStyle: TextStyle(color: Colors.white),
                              prefixIcon:
                                  Icon(Icons.category, color: Colors.white),
                              filled: true,
                              fillColor: Colors.white24,
                              border: OutlineInputBorder(),
                            ),
                            dropdownColor: Colors.white,
                            items: const [
                              DropdownMenuItem(
                                  value: 'mensual',
                                  child: Text('Mensual (+1 mes)')),
                              DropdownMenuItem(
                                  value: 'trimestral',
                                  child: Text('Trimestral (+3 meses)')),
                              DropdownMenuItem(
                                  value: 'semestral',
                                  child: Text('Semestral (+6 meses)')),
                              DropdownMenuItem(
                                  value: 'anual',
                                  child: Text('Anual (+1 año)')),
                            ],
                            onChanged: (value) {
                              setState(() {
                                _tipoSuscripcion = value!;
                              });
                            },
                          ),
                          const SizedBox(height: 16),

                          // Monto de suscripción (automático)
                          TextFormField(
                            controller: _montoSuscripcionController,
                            readOnly:
                                true, // Solo lectura - se actualiza automáticamente
                            decoration: InputDecoration(
                              labelText: 'Monto de Suscripción (Automático)',
                              labelStyle: const TextStyle(color: Colors.white),
                              prefixIcon: const Icon(Icons.attach_money,
                                  color: Colors.white),
                              filled: true,
                              fillColor: Colors.white.withValues(alpha: 0.1),
                              border: const OutlineInputBorder(),
                              hintText: 'Se calcula automáticamente del total',
                              hintStyle: const TextStyle(color: Colors.white54),
                              suffixIcon: const Icon(
                                Icons.lock,
                                color: Colors.white54,
                                size: 20,
                              ),
                            ),
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),

                          const SizedBox(height: 8),
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Colors.green.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(6),
                              border: Border.all(
                                  color: Colors.green.withValues(alpha: 0.3)),
                            ),
                            child: Row(
                              children: [
                                const Icon(Icons.info_outline,
                                    color: Colors.lightGreen, size: 16),
                                const SizedBox(width: 6),
                                Expanded(
                                  child: Text(
                                    'El monto se actualiza automáticamente con el total de la factura',
                                    style: TextStyle(
                                      color: Colors.lightGreen.shade100,
                                      fontSize: 11,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),

                          const SizedBox(height: 12),
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.blue.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                  color: Colors.blue.withValues(alpha: 0.3)),
                            ),
                            child: Row(
                              children: [
                                const Icon(Icons.info,
                                    color: Colors.lightBlue, size: 20),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    'La suscripción se activará automáticamente al guardar la factura',
                                    style: TextStyle(
                                      color: Colors.lightBlue.shade100,
                                      fontSize: 12,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),

                ResponsiveSpacing(mobile: 20, tablet: 24, desktop: 32),
                ResponsiveButton(
                  onPressed: _isLoading ? null : _guardarFactura,
                  isFullWidth: true,
                  mobileHeight: 48,
                  tabletHeight: 56,
                  desktopHeight: 64,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    foregroundColor: Colors.white,
                  ),
                  child: _isLoading
                      ? const Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            SizedBox(
                              height: 16,
                              width: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            ),
                            SizedBox(width: 12),
                            Text("Guardando..."),
                          ],
                        )
                      : const Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.save),
                            SizedBox(width: 8),
                            Text("Guardar Factura"),
                          ],
                        ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
