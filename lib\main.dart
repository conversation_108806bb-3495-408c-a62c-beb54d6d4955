import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'routes/app_routes.dart';
import 'services/cache_service.dart';
import 'theme/app_theme.dart';
import 'utils/constants.dart';
import 'dart:developer';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    log('🚀 Inicializando aplicación ${AppConstants.appName}...');

    // Configurar orientación de pantalla
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    // Inicializar Firebase
    log('🔥 Inicializando Firebase...');
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    log('✅ Firebase inicializado correctamente');

    // Inicializar caché
    log('💾 Inicializando sistema de caché...');
    await CacheService.init();
    log('✅ Sistema de caché inicializado');

    // Limpiar caché expirado
    await CacheService.clearExpired();

    log('🎉 Aplicación inicializada correctamente');
    runApp(const MyApp());

  } catch (e, stackTrace) {
    log('❌ Error inicializando aplicación: $e');
    log('Stack trace: $stackTrace');

    // En caso de error, ejecutar app básica
    runApp(MaterialApp(
      home: Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              const Text('Error de inicialización', style: TextStyle(fontSize: 18)),
              const SizedBox(height: 8),
              Text('$e', textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    ));
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: AppConstants.appName,
      debugShowCheckedModeBanner: false,

      // Usar el nuevo sistema de temas
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system, // Respeta la configuración del sistema

      // Sistema de rutas nombradas
      initialRoute: AppRoutes.root,
      routes: AppRoutes.routes,
      onGenerateRoute: AppRoutes.onGenerateRoute,

      // Configuración adicional
      builder: (context, child) {
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(
            textScaler: TextScaler.linear(1.0), // Evitar escalado excesivo de texto
          ),
          child: child!,
        );
      },
    );
  }
}
