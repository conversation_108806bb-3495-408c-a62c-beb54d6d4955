// Tests unitarios para el modelo Cliente

import 'package:flutter_test/flutter_test.dart';
import 'package:shop_3m/models/cliente_model.dart';

void main() {
  group('Cliente Model Tests', () {
    group('Constructor and Validation Tests', () {
      test('should create a valid Cliente with correct data', () {
        // Arrange & Act
        final cliente = Cliente(
          idCliente: '123',
          nombre: '<PERSON>',
          telefono: '+1234567890',
          email: '<EMAIL>',
        );

        // Assert
        expect(cliente.idCliente, '123');
        expect(cliente.nombre, '<PERSON>');
        expect(cliente.telefono, '+1234567890');
        expect(cliente.email, '<EMAIL>');
      });

      test('should throw ArgumentError for empty nombre', () {
        // Act & Assert
        expect(
          () => Cliente(
            idCliente: '123',
            nombre: '',
            telefono: '+1234567890',
            email: '<EMAIL>',
          ),
          throwsA(isA<ArgumentError>()),
        );
      });

      test('should throw ArgumentError for short nombre', () {
        // Act & Assert
        expect(
          () => Cliente(
            idCliente: '123',
            nombre: 'A',
            telefono: '+1234567890',
            email: '<EMAIL>',
          ),
          throwsA(isA<ArgumentError>()),
        );
      });

      test('should throw ArgumentError for invalid email format', () {
        // Act & Assert
        expect(
          () => Cliente(
            idCliente: '123',
            nombre: 'Juan Pérez',
            telefono: '+1234567890',
            email: 'invalid-email',
          ),
          throwsA(isA<ArgumentError>()),
        );
      });

      test('should throw ArgumentError for invalid telefono format', () {
        // Act & Assert
        expect(
          () => Cliente(
            idCliente: '123',
            nombre: 'Juan Pérez',
            telefono: '123',
            email: '<EMAIL>',
          ),
          throwsA(isA<ArgumentError>()),
        );
      });
    });

    group('Validation Methods Tests', () {
      test('isValidNombre should return true for valid names', () {
        expect(Cliente.isValidNombre('Juan Pérez'), isTrue);
        expect(Cliente.isValidNombre('María José'), isTrue);
        expect(Cliente.isValidNombre('AB'), isTrue);
      });

      test('isValidNombre should return false for invalid names', () {
        expect(Cliente.isValidNombre(''), isFalse);
        expect(Cliente.isValidNombre('A'), isFalse);
        expect(Cliente.isValidNombre('   '), isFalse);
      });

      test('isValidEmail should return true for valid emails', () {
        expect(Cliente.isValidEmail('<EMAIL>'), isTrue);
        expect(Cliente.isValidEmail('<EMAIL>'), isTrue);
        expect(Cliente.isValidEmail('<EMAIL>'), isTrue);
      });

      test('isValidEmail should return false for invalid emails', () {
        expect(Cliente.isValidEmail('invalid-email'), isFalse);
        expect(Cliente.isValidEmail('@example.com'), isFalse);
        expect(Cliente.isValidEmail('test@'), isFalse);
        expect(Cliente.isValidEmail(''), isFalse);
      });

      test('isValidTelefono should return true for valid phones', () {
        expect(Cliente.isValidTelefono('+1234567890'), isTrue);
        expect(Cliente.isValidTelefono('************'), isTrue);
        expect(Cliente.isValidTelefono('(*************'), isTrue);
      });

      test('isValidTelefono should return false for invalid phones', () {
        expect(Cliente.isValidTelefono('123'), isFalse);
        expect(Cliente.isValidTelefono(''), isFalse);
        expect(Cliente.isValidTelefono('abc'), isFalse);
      });
    });

    group('validateAll Tests', () {
      test('should return empty map for valid data', () {
        final errores = Cliente.validateAll(
          nombre: 'Juan Pérez',
          telefono: '+1234567890',
          email: '<EMAIL>',
        );

        expect(errores, isEmpty);
      });

      test('should return errors for invalid data', () {
        final errores = Cliente.validateAll(
          nombre: '',
          telefono: '123',
          email: 'invalid-email',
        );

        expect(errores, isNotEmpty);
        expect(errores.containsKey('nombre'), isTrue);
        expect(errores.containsKey('telefono'), isTrue);
        expect(errores.containsKey('email'), isTrue);
      });
    });

    group('Serialization Tests', () {
      test('toMap should return correct Map', () {
        final cliente = Cliente(
          idCliente: '123',
          nombre: 'Juan Pérez',
          telefono: '+1234567890',
          email: '<EMAIL>',
        );

        final map = cliente.toMap();

        expect(map['IdCliente'], '123');
        expect(map['Nombre'], 'Juan Pérez');
        expect(map['Telefono'], '+1234567890');
        expect(map['Email'], '<EMAIL>'); // Should be lowercase
      });

      test('fromMap should create correct Cliente', () {
        final map = {
          'IdCliente': '123',
          'Nombre': 'Juan Pérez',
          'Telefono': '+1234567890',
          'Email': '<EMAIL>',
        };

        final cliente = Cliente.fromMap(map);

        expect(cliente.idCliente, '123');
        expect(cliente.nombre, 'Juan Pérez');
        expect(cliente.telefono, '+1234567890');
        expect(cliente.email, '<EMAIL>');
      });

      test('fromMap should handle missing values', () {
        final map = <String, dynamic>{};

        expect(
          () => Cliente.fromMap(map),
          throwsA(isA<FormatException>()),
        );
      });
    });

    group('Utility Methods Tests', () {
      test('copyWith should create new instance with updated values', () {
        final original = Cliente(
          idCliente: '123',
          nombre: 'Juan Pérez',
          telefono: '+1234567890',
          email: '<EMAIL>',
        );

        final updated = original.copyWith(nombre: 'Juan Carlos');

        expect(updated.idCliente, original.idCliente);
        expect(updated.nombre, 'Juan Carlos');
        expect(updated.telefono, original.telefono);
        expect(updated.email, original.email);
      });

      test('toString should return formatted string', () {
        final cliente = Cliente(
          idCliente: '123',
          nombre: 'Juan Pérez',
          telefono: '+1234567890',
          email: '<EMAIL>',
        );

        final string = cliente.toString();

        expect(string, contains('Cliente'));
        expect(string, contains('123'));
        expect(string, contains('Juan Pérez'));
      });

      test('nombreFormateado should return trimmed name', () {
        final cliente = Cliente(
          idCliente: '123',
          nombre: '  Juan Pérez  ',
          telefono: '+1234567890',
          email: '<EMAIL>',
        );

        expect(cliente.nombreFormateado, 'Juan Pérez');
      });

      test('emailNormalizado should return lowercase trimmed email', () {
        final cliente = Cliente(
          idCliente: '123',
          nombre: 'Juan Pérez',
          telefono: '+1234567890',
          email: '  <EMAIL>  ',
        );

        expect(cliente.emailNormalizado, '<EMAIL>');
      });
    });
  });
}
