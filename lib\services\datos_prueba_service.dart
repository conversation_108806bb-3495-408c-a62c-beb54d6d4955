import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/cliente_model.dart';
import '../models/factura_model.dart';
import '../models/modulo_model.dart';
import 'dart:developer' as dev;
import 'dart:math';

/// Servicio para generar datos de prueba realistas
class DatosPruebaService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final Random _random = Random();

  /// Generar datos de prueba completos
  static Future<void> generarDatosPrueba() async {
    try {
      dev.log('🎲 Generando datos de prueba...');

      // Generar en orden: módulos -> clientes -> facturas
      await _generarModulos();
      await _generarClientes();
      await _generarFacturas();

      dev.log('✅ Datos de prueba generados exitosamente');
    } catch (e) {
      dev.log('❌ Error generando datos de prueba: $e');
      rethrow;
    }
  }

  /// Generar módulos de prueba
  static Future<void> _generarModulos() async {
    final modulos = [
      Mo<PERSON><PERSON>(
        idModulo: 'mod_001',
        nombre: '<PERSON><PERSON><PERSON><PERSON>',
        precio: 50000,
        descripcion: 'Funcionalidades básicas del sistema',
        activo: true,
        fechaCreacion: DateTime.now().subtract(const Duration(days: 90)),
      ),
      Modulo(
        idModulo: 'mod_002',
        nombre: 'Módulo Premium',
        precio: 100000,
        descripcion: 'Funcionalidades avanzadas y reportes',
        activo: true,
        fechaCreacion: DateTime.now().subtract(const Duration(days: 80)),
      ),
      Modulo(
        idModulo: 'mod_003',
        nombre: 'Módulo Pro',
        precio: 150000,
        descripcion: 'Todas las funcionalidades + soporte prioritario',
        activo: true,
        fechaCreacion: DateTime.now().subtract(const Duration(days: 70)),
      ),
      Modulo(
        idModulo: 'mod_004',
        nombre: 'Módulo Enterprise',
        precio: 250000,
        descripcion: 'Solución empresarial completa',
        activo: true,
        fechaCreacion: DateTime.now().subtract(const Duration(days: 60)),
      ),
      Modulo(
        idModulo: 'mod_005',
        nombre: 'Módulo Legacy',
        precio: 30000,
        descripcion: 'Módulo descontinuado',
        activo: false,
        fechaCreacion: DateTime.now().subtract(const Duration(days: 200)),
      ),
    ];

    for (final modulo in modulos) {
      await _firestore.collection('modulos').doc(modulo.idModulo).set(modulo.toFirestore());
    }
    
    dev.log('📦 ${modulos.length} módulos generados');
  }

  /// Generar clientes de prueba
  static Future<void> _generarClientes() async {
    final nombres = [
      'Juan Pérez', 'María García', 'Carlos López', 'Ana Martínez', 'Luis Rodríguez',
      'Carmen Fernández', 'José González', 'Isabel Sánchez', 'Miguel Torres', 'Laura Ruiz',
      'Antonio Morales', 'Pilar Jiménez', 'Francisco Álvarez', 'Rosa Romero', 'Manuel Navarro',
      'Teresa Muñoz', 'Alejandro Díaz', 'Cristina Herrera', 'Rafael Peña', 'Silvia Castro',
    ];

    final empresas = [
      'TechSolutions SAS', 'Innovación Digital Ltda', 'Sistemas Avanzados SA', 
      'DataCorp Colombia', 'CloudTech Solutions', 'Digital Innovations',
      'SmartSystems Co', 'TechPro Services', 'InnovateTech SA', 'DigitalWorks Ltda',
    ];

    final tiposSuscripcion = ['Básica', 'Premium', 'Pro', 'Enterprise'];
    final estadosSuscripcion = ['activa', 'vencida', 'suspendida', 'cancelada'];

    final clientes = <Cliente>[];

    for (int i = 0; i < 25; i++) {
      final nombre = i < nombres.length ? nombres[i] : '${nombres[_random.nextInt(nombres.length)]} ${i + 1}';
      final empresa = empresas[_random.nextInt(empresas.length)];
      final tipoSuscripcion = tiposSuscripcion[_random.nextInt(tiposSuscripcion.length)];
      final estadoSuscripcion = estadosSuscripcion[_random.nextInt(estadosSuscripcion.length)];
      
      // Fechas realistas
      final fechaInicio = DateTime.now().subtract(Duration(days: _random.nextInt(365) + 30));
      final fechaVencimiento = fechaInicio.add(Duration(days: 30 + _random.nextInt(335)));
      
      // Montos según tipo de suscripción
      double monto;
      switch (tipoSuscripcion) {
        case 'Básica':
          monto = 50000;
          break;
        case 'Premium':
          monto = 100000;
          break;
        case 'Pro':
          monto = 150000;
          break;
        case 'Enterprise':
          monto = 250000;
          break;
        default:
          monto = 50000;
      }

      final cliente = Cliente(
        idCliente: 'cli_${(i + 1).toString().padLeft(3, '0')}',
        nombre: '$nombre - $empresa',
        telefono: '300${_random.nextInt(9000000) + 1000000}',
        email: '${nombre.toLowerCase().replaceAll(' ', '.')}@${empresa.toLowerCase().replaceAll(' ', '').replaceAll('ltda', '').replaceAll('sas', '').replaceAll('sa', '')}.com',
        fechaInicioSuscripcion: fechaInicio,
        fechaVencimientoSuscripcion: fechaVencimiento,
        tipoSuscripcion: tipoSuscripcion,
        montoSuscripcion: monto,
        estadoSuscripcion: estadoSuscripcion,
      );

      clientes.add(cliente);
    }

    // Guardar en Firestore
    for (final cliente in clientes) {
      await _firestore.collection('clientes').doc(cliente.idCliente).set(cliente.toMap());
    }
    
    dev.log('👥 ${clientes.length} clientes generados');
  }

  /// Generar facturas de prueba
  static Future<void> _generarFacturas() async {
    final metodosPago = ['Efectivo', 'Tarjeta', 'Transferencia', 'Cheque'];
    final estados = ['pendiente', 'pagada', 'cancelada'];
    
    // Obtener clientes y módulos existentes
    final clientesSnapshot = await _firestore.collection('clientes').get();
    final modulosSnapshot = await _firestore.collection('modulos').get();
    
    final clientesIds = clientesSnapshot.docs.map((doc) => doc.id).toList();
    final modulosIds = modulosSnapshot.docs.map((doc) => doc.id).toList();
    
    if (clientesIds.isEmpty || modulosIds.isEmpty) {
      dev.log('⚠️ No hay clientes o módulos para generar facturas');
      return;
    }

    final facturas = <Factura>[];

    // Generar facturas de los últimos 6 meses
    for (int i = 0; i < 50; i++) {
      final fechaFactura = DateTime.now().subtract(Duration(days: _random.nextInt(180)));
      final clienteId = clientesIds[_random.nextInt(clientesIds.length)];
      final metodoPago = metodosPago[_random.nextInt(metodosPago.length)];
      final estado = estados[_random.nextInt(estados.length)];
      
      // Seleccionar módulos aleatorios (1-3 módulos por factura)
      final numModulos = _random.nextInt(3) + 1;
      final modulosFactura = <String>[];
      final modulosSeleccionados = List<String>.from(modulosIds)..shuffle();
      
      for (int j = 0; j < numModulos && j < modulosSeleccionados.length; j++) {
        modulosFactura.add(modulosSeleccionados[j]);
      }
      
      // Calcular total basado en módulos
      double total = 0;
      for (final moduloId in modulosFactura) {
        final moduloDoc = await _firestore.collection('modulos').doc(moduloId).get();
        if (moduloDoc.exists) {
          final moduloData = moduloDoc.data() as Map<String, dynamic>;
          total += (moduloData['precio'] ?? 0).toDouble();
        }
      }
      
      // Aplicar descuento aleatorio (0-20%)
      final descuento = _random.nextDouble() * 0.2;
      total = total * (1 - descuento);

      final factura = Factura(
        idFactura: 'fac_${(i + 1).toString().padLeft(3, '0')}',
        cliente: clienteId,
        metodoPago: metodoPago,
        total: total,
        fecha: fechaFactura,
        estado: estado,
        modulos: modulosFactura,
        comprobante: estado == 'pagada' ? 'COMP_${_random.nextInt(999999).toString().padLeft(6, '0')}' : null,
      );

      facturas.add(factura);
    }

    // Guardar en Firestore
    for (final factura in facturas) {
      await _firestore.collection('facturas').doc(factura.idFactura).set(factura.toFirestore());
    }
    
    dev.log('🧾 ${facturas.length} facturas generadas');
  }

  /// Limpiar todos los datos de prueba
  static Future<void> limpiarDatosPrueba() async {
    try {
      dev.log('🧹 Limpiando datos de prueba...');

      // Limpiar colecciones
      await _limpiarColeccion('clientes');
      await _limpiarColeccion('facturas');
      await _limpiarColeccion('modulos');

      dev.log('✅ Datos de prueba limpiados');
    } catch (e) {
      dev.log('❌ Error limpiando datos de prueba: $e');
      rethrow;
    }
  }

  /// Limpiar una colección específica
  static Future<void> _limpiarColeccion(String coleccion) async {
    final snapshot = await _firestore.collection(coleccion).get();
    
    for (final doc in snapshot.docs) {
      await doc.reference.delete();
    }
    
    dev.log('🗑️ Colección $coleccion limpiada (${snapshot.docs.length} documentos)');
  }

  /// Verificar si existen datos de prueba
  static Future<bool> existenDatosPrueba() async {
    try {
      final clientesSnapshot = await _firestore.collection('clientes').limit(1).get();
      final facturasSnapshot = await _firestore.collection('facturas').limit(1).get();
      final modulosSnapshot = await _firestore.collection('modulos').limit(1).get();
      
      return clientesSnapshot.docs.isNotEmpty || 
             facturasSnapshot.docs.isNotEmpty || 
             modulosSnapshot.docs.isNotEmpty;
    } catch (e) {
      dev.log('❌ Error verificando datos de prueba: $e');
      return false;
    }
  }

  /// Obtener estadísticas de datos de prueba
  static Future<Map<String, int>> obtenerEstadisticasDatosPrueba() async {
    try {
      final clientesSnapshot = await _firestore.collection('clientes').get();
      final facturasSnapshot = await _firestore.collection('facturas').get();
      final modulosSnapshot = await _firestore.collection('modulos').get();
      
      return {
        'clientes': clientesSnapshot.docs.length,
        'facturas': facturasSnapshot.docs.length,
        'modulos': modulosSnapshot.docs.length,
      };
    } catch (e) {
      dev.log('❌ Error obteniendo estadísticas: $e');
      return {'clientes': 0, 'facturas': 0, 'modulos': 0};
    }
  }
}
