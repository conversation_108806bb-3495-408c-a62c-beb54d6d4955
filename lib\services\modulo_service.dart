import 'package:cloud_firestore/cloud_firestore.dart';
import '../services/firebase_service.dart';
import '../models/modulo_model.dart';
import 'dart:developer';

class ModuloService {
  static const String _collection = 'modulos';
  static bool _yaVerificado = false; // Para evitar verificar múltiples veces

  /// Verificar y arreglar módulos que no tienen el campo activo
  static Future<void> _verificarYArreglarModulos() async {
    if (_yaVerificado) return; // Solo verificar una vez por sesión

    try {
      log('🔧 Verificando módulos sin campo activo...');

      // Obtener todos los módulos sin filtro
      final snapshot =
          await FirebaseFirestore.instance.collection(_collection).get();

      log('📊 Total de módulos en Firebase: ${snapshot.docs.length}');

      int arreglados = 0;
      for (var doc in snapshot.docs) {
        final data = doc.data();
        log('📄 Módulo ${doc.id}: activo=${data['activo']}, nombre=${data['nombre']}');

        // Si no tiene el campo activo o es null, establecerlo como true
        if (!data.containsKey('activo') || data['activo'] == null) {
          await doc.reference.update({'activo': true});
          log('✅ Módulo arreglado: ${doc.id}');
          arreglados++;
        } else if (data['activo'] == false) {
          log('⚠️ Módulo inactivo: ${doc.id}');
        } else {
          log('✓ Módulo ya activo: ${doc.id}');
        }
      }

      if (arreglados > 0) {
        log('🎉 Se arreglaron $arreglados módulos');
      } else {
        log('ℹ️ Todos los módulos ya tienen el campo activo');
      }

      _yaVerificado = true;
    } catch (e) {
      log('❌ Error verificando módulos: $e');
    }
  }

  /// Resetear verificación (útil para debug)
  static void resetearVerificacion() {
    _yaVerificado = false;
  }

  /// Obtener todos los módulos
  static Future<List<Map<String, dynamic>>> obtenerModulos() async {
    try {
      final snapshot = await FirebaseService.getCollection(
        collection: _collection,
        queryBuilder: (query) => query.where('activo', isEqualTo: true),
      );

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return {
          'IdModulo': doc.id,
          'Nombre': data['nombre'] ?? '',
          'Precio': data['precio'] ?? 0.0,
          'Descripcion': data['descripcion'] ?? '',
        };
      }).toList();
    } catch (e) {
      log('Error obteniendo módulos: $e');
      return [];
    }
  }

  /// Obtener módulos con paginación
  static Future<List<Map<String, dynamic>>> obtenerModulosPaginados({
    int limit = 20,
    int offset = 0,
    String? searchQuery,
    double? precioMinimo,
    double? precioMaximo,
  }) async {
    try {
      log('🔍 Obteniendo módulos paginados - limit: $limit, offset: $offset, searchQuery: $searchQuery');

      // Primero verificamos si hay módulos sin el campo activo y los arreglamos
      await _verificarYArreglarModulos();

      // Estrategia alternativa: obtener todos y filtrar en memoria
      log('📋 Ejecutando consulta sin filtro para obtener todos los módulos');
      Query query = FirebaseFirestore.instance.collection(_collection);

      // Aplicar filtro de búsqueda si existe
      if (searchQuery != null && searchQuery.isNotEmpty) {
        query = query
            .where('nombre', isGreaterThanOrEqualTo: searchQuery)
            .where('nombre', isLessThanOrEqualTo: '$searchQuery\uf8ff');
      }

      // Aplicar filtros de precio
      if (precioMinimo != null) {
        query = query.where('precio', isGreaterThanOrEqualTo: precioMinimo);
      }
      if (precioMaximo != null) {
        query = query.where('precio', isLessThanOrEqualTo: precioMaximo);
      }

      // Aplicar paginación
      query = query.orderBy('nombre');

      // Aplicar offset si es necesario
      if (offset > 0) {
        // Para Firebase, necesitamos usar startAfter con un documento específico
        // Por simplicidad, vamos a usar limit y skip en memoria por ahora
        query = query.limit(limit + offset);
      } else {
        query = query.limit(limit);
      }

      final snapshot = await query.get();
      log('📊 Módulos encontrados: ${snapshot.docs.length}');

      // Aplicar offset en memoria si es necesario
      List<QueryDocumentSnapshot> docs = snapshot.docs;
      if (offset > 0 && docs.length > offset) {
        docs = docs.skip(offset).take(limit).toList();
      }

      final modulos = docs
          .map((doc) {
            final data = doc.data() as Map<String, dynamic>;
            log('📄 Procesando módulo: ${doc.id} - datos: $data');

            // Filtrar solo módulos activos en memoria
            bool esActivo = data['activo'] == true;
            if (!esActivo) {
              log('⚠️ Módulo filtrado (inactivo): ${doc.id}');
              return null;
            }

            return {
              'IdModulo': doc.id,
              'Nombre': data['nombre'] ?? '',
              'Precio': data['precio'] ?? 0.0,
              'Descripcion': data['descripcion'] ?? '',
            };
          })
          .where((modulo) => modulo != null)
          .cast<Map<String, dynamic>>()
          .toList();

      log('✅ Módulos procesados: ${modulos.length}');
      return modulos;
    } catch (e) {
      log('Error obteniendo módulos paginados: $e');
      return [];
    }
  }

  /// Contar módulos
  static Future<int> contarModulos({String? searchQuery}) async {
    try {
      log('🔢 Contando módulos...');
      Query query = FirebaseFirestore.instance.collection(_collection);

      if (searchQuery != null && searchQuery.isNotEmpty) {
        query = query
            .where('nombre', isGreaterThanOrEqualTo: searchQuery)
            .where('nombre', isLessThanOrEqualTo: '$searchQuery\uf8ff');
      }

      final snapshot = await query.get();

      // Contar solo módulos activos en memoria
      int count = 0;
      for (var doc in snapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        if (data['activo'] == true) {
          count++;
        }
      }

      log('📊 Total de módulos activos encontrados: $count de ${snapshot.docs.length}');
      return count;
    } catch (e) {
      log('Error contando módulos: $e');
      return 0;
    }
  }

  /// Insertar módulo
  static Future<void> insertarModulo(String nombre, double precio,
      {String? descripcion}) async {
    try {
      final docRef = FirebaseFirestore.instance.collection(_collection).doc();

      await FirebaseService.createDocument(
        collection: _collection,
        docId: docRef.id,
        data: {
          'nombre': nombre,
          'precio': precio,
          'descripcion': descripcion ?? '',
          'activo': true,
        },
      );
      log('Módulo insertado exitosamente: $nombre');
    } catch (e) {
      log('Error insertando módulo: $e');
      rethrow;
    }
  }

  /// Insertar módulo completo (con imagen)
  static Future<void> insertarModuloCompleto(Modulo modulo) async {
    try {
      await FirebaseService.createDocument(
        collection: _collection,
        docId: modulo.idModulo,
        data: modulo.toMap()..addAll({'activo': true}),
      );
      log('Módulo completo insertado exitosamente: ${modulo.nombre}');
    } catch (e) {
      log('Error insertando módulo completo: $e');
      rethrow;
    }
  }

  /// Actualizar módulo
  static Future<void> actualizarModulo(
      String idModulo, String nombre, double precio,
      {String? descripcion}) async {
    try {
      await FirebaseService.updateDocument(
        collection: _collection,
        docId: idModulo,
        data: {
          'nombre': nombre,
          'precio': precio,
          'descripcion': descripcion ?? '',
        },
      );
      log('Módulo actualizado exitosamente: $nombre');
    } catch (e) {
      log('Error actualizando módulo: $e');
      rethrow;
    }
  }

  /// Eliminar módulo (soft delete)
  static Future<void> eliminarModulo(String idModulo) async {
    try {
      log('🗑️ Eliminando módulo: $idModulo');
      await FirebaseService.updateDocument(
        collection: _collection,
        docId: idModulo,
        data: {
          'activo': false,
          'fechaEliminacion': FieldValue.serverTimestamp(),
        },
      );
      log('✅ Módulo eliminado exitosamente: $idModulo');
    } catch (e) {
      log('❌ Error eliminando módulo: $e');
      rethrow;
    }
  }

  /// Obtener módulo por ID
  static Future<Map<String, dynamic>?> obtenerModuloPorId(
      String idModulo) async {
    try {
      final doc = await FirebaseService.getDocument(
        collection: _collection,
        docId: idModulo,
      );

      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;
        return {
          'IdModulo': doc.id,
          'Nombre': data['nombre'] ?? '',
          'Precio': data['precio'] ?? 0.0,
          'Descripcion': data['descripcion'] ?? '',
          'Activo': data['activo'] ?? true,
        };
      }
      return null;
    } catch (e) {
      log('Error obteniendo módulo por ID: $e');
      return null;
    }
  }

  /// Buscar módulos por nombre
  static Future<List<Map<String, dynamic>>> buscarModulosPorNombre(
      String nombre) async {
    try {
      final snapshot = await FirebaseService.getCollection(
        collection: _collection,
        queryBuilder: (query) => query
            .where('activo', isEqualTo: true)
            .where('nombre', isGreaterThanOrEqualTo: nombre)
            .where('nombre', isLessThanOrEqualTo: '$nombre\uf8ff')
            .limit(10),
      );

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return {
          'IdModulo': doc.id,
          'Nombre': data['nombre'] ?? '',
          'Precio': data['precio'] ?? 0.0,
          'Descripcion': data['descripcion'] ?? '',
        };
      }).toList();
    } catch (e) {
      log('Error buscando módulos por nombre: $e');
      return [];
    }
  }

  /// Verificar si existe un módulo con el mismo nombre
  static Future<bool> existeModuloConNombre(String nombre,
      {String? excludeId}) async {
    try {
      Query query = FirebaseFirestore.instance
          .collection(_collection)
          .where('nombre', isEqualTo: nombre)
          .where('activo', isEqualTo: true);

      final snapshot = await query.get();

      if (excludeId != null) {
        return snapshot.docs.any((doc) => doc.id != excludeId);
      }

      return snapshot.docs.isNotEmpty;
    } catch (e) {
      log('Error verificando nombre duplicado: $e');
      return false;
    }
  }

  /// Obtener módulos más vendidos
  static Future<List<Map<String, dynamic>>> obtenerModulosMasVendidos(
      {int limit = 5}) async {
    try {
      // Esta funcionalidad requeriría un campo de contador en cada módulo
      // Por ahora, devolvemos los módulos ordenados por nombre
      final snapshot = await FirebaseService.getCollection(
        collection: _collection,
        queryBuilder: (query) => query
            .where('activo', isEqualTo: true)
            .orderBy('nombre')
            .limit(limit),
      );

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return {
          'IdModulo': doc.id,
          'Nombre': data['nombre'] ?? '',
          'Precio': data['precio'] ?? 0.0,
          'Descripcion': data['descripcion'] ?? '',
          'Ventas': data['totalVentas'] ?? 0, // Campo que se podría agregar
        };
      }).toList();
    } catch (e) {
      log('Error obteniendo módulos más vendidos: $e');
      return [];
    }
  }
}
