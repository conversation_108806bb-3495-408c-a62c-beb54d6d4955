import 'package:flutter/material.dart';
import 'search_filter_bar.dart';
import '../utils/responsive_utils.dart';

class PaginatedListBuilder<T> extends StatefulWidget {
  final Future<List<T>> Function({
    int limit,
    int offset,
    String? searchQuery,
  }) dataLoader;
  final Future<int> Function({String? searchQuery}) countLoader;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final String searchHint;
  final int initialItemsPerPage;
  final bool showSearch;
  final bool showPagination;
  final Widget? emptyWidget;
  final String? emptyMessage;
  final Widget? customFilterWidget;
  final VoidCallback? onFilterPressed;
  final PaginatedListController? controller;

  const PaginatedListBuilder({
    super.key,
    required this.dataLoader,
    required this.countLoader,
    required this.itemBuilder,
    this.searchHint = "Buscar...",
    this.initialItemsPerPage = 20,
    this.showSearch = true,
    this.showPagination = true,
    this.emptyWidget,
    this.emptyMessage,
    this.customFilterWidget,
    this.onFilterPressed,
    this.controller,
  });

  @override
  State<PaginatedListBuilder<T>> createState() =>
      _PaginatedListBuilderState<T>();
}

// Clase para controlar el PaginatedListBuilder desde fuera
class PaginatedListController {
  _PaginatedListBuilderState? _state;

  void _attach(_PaginatedListBuilderState state) {
    _state = state;
  }

  void _detach() {
    _state = null;
  }

  Future<void> refresh() async {
    await _state?._loadData();
  }
}

class _PaginatedListBuilderState<T> extends State<PaginatedListBuilder<T>> {
  int _currentPage = 1;
  int _itemsPerPage = 20;
  int _totalItems = 0;
  String _searchQuery = '';
  bool _isLoading = false;
  List<T> _items = [];
  String? _error;

  @override
  void initState() {
    super.initState();
    _itemsPerPage = widget.initialItemsPerPage;
    widget.controller?._attach(this);
    _loadData();
  }

  @override
  void dispose() {
    widget.controller?._detach();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Ajustar items por página basado en el tamaño de pantalla
    final adaptiveItemsPerPage = ResponsiveUtils.getItemsPerPage(context);
    if (_itemsPerPage != adaptiveItemsPerPage &&
        widget.initialItemsPerPage == 10) {
      // Solo cambiar si se está usando el valor por defecto
      _itemsPerPage = adaptiveItemsPerPage;
      _loadData();
    }
  }

  Future<void> _loadData() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Cargar datos y conteo en paralelo
      final futures = await Future.wait([
        widget.dataLoader(
          limit: _itemsPerPage,
          offset: (_currentPage - 1) * _itemsPerPage,
          searchQuery: _searchQuery.isEmpty ? null : _searchQuery,
        ),
        widget.countLoader(
          searchQuery: _searchQuery.isEmpty ? null : _searchQuery,
        ),
      ]);

      if (mounted) {
        setState(() {
          _items = futures[0] as List<T>;
          _totalItems = futures[1] as int;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
      _currentPage = 1; // Reset to first page when searching
    });
    _loadData();
  }

  void _onPageChanged(int page) {
    setState(() {
      _currentPage = page;
    });
    _loadData();
  }

  void _onItemsPerPageChanged(int itemsPerPage) {
    setState(() {
      _itemsPerPage = itemsPerPage;
      _currentPage = 1; // Reset to first page when changing items per page
    });
    _loadData();
  }

  int get _totalPages => (_totalItems / _itemsPerPage).ceil();

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Barra de búsqueda
        if (widget.showSearch)
          SearchFilterBar(
            searchHint: widget.searchHint,
            onSearchChanged: _onSearchChanged,
            onFilterPressed: widget.onFilterPressed,
            showFilterButton: widget.onFilterPressed != null,
            customFilterWidget: widget.customFilterWidget,
          ),

        // Lista de elementos
        Expanded(
          child: _buildContent(),
        ),

        // Controles de paginación
        if (widget.showPagination && _totalItems > 0)
          PaginationControls(
            currentPage: _currentPage,
            totalPages: _totalPages,
            totalItems: _totalItems,
            itemsPerPage: _itemsPerPage,
            onPageChanged: _onPageChanged,
            onItemsPerPageChanged: _onItemsPerPageChanged,
          ),
      ],
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Error al cargar los datos',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadData,
              child: const Text('Reintentar'),
            ),
          ],
        ),
      );
    }

    if (_items.isEmpty) {
      return widget.emptyWidget ??
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.inbox_outlined,
                  size: 64,
                  color: Colors.grey,
                ),
                const SizedBox(height: 16),
                Text(
                  widget.emptyMessage ?? 'No hay elementos para mostrar',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                if (_searchQuery.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Text(
                    'Intenta con una búsqueda diferente',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ],
            ),
          );
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _items.length,
        itemBuilder: (context, index) {
          return widget.itemBuilder(context, _items[index], index);
        },
      ),
    );
  }
}
