# 🔥 Configuración de Firebase para Shop 3M

## 📋 Información del Proyecto

- **Package Name:** `com.example.shop_3m`
- **Proyecto sugerido:** `shop3m-project`

## 🔐 Certificados SHA-1

### Debug (Desarrollo)
```
SHA-1: 35:40:2E:AF:8D:70:8A:B9:9C:A4:05:BF:A3:1F:09:D7:25:B0:34:15
```

### Release (Producción)
```
SHA-1: C9:95:D9:21:01:02:F3:43:57:26:8A:12:CE:69:68:E4:C7:EB:99:A8
```

## 🚀 Pasos para Configurar Firebase

### 1. Crear Proyecto en Firebase Console

1. Ve a [Firebase Console](https://console.firebase.google.com/)
2. Haz clic en "Crear un proyecto"
3. Nombre del proyecto: `shop3m-project` (o el que prefieras)
4. Habilita Google Analytics (opcional)

### 2. Configurar Android App

1. En el proyecto Firebase, haz clic en "Agregar app" → Android
2. **Package name:** `com.example.shop_3m`
3. **App nickname:** `Shop 3M Android`
4. **SHA-1 Debug:** `35:40:2E:AF:8D:70:8A:B9:9C:A4:05:BF:A3:1F:09:D7:25:B0:34:15`
5. Descarga `google-services.json`
6. Coloca el archivo en `android/app/google-services.json`

### 3. Configurar Web App

1. En el proyecto Firebase, haz clic en "Agregar app" → Web
2. **App nickname:** `Shop 3M Web`
3. Habilita Firebase Hosting (opcional)
4. Copia la configuración de Firebase

### 4. Actualizar firebase_options.dart

Reemplaza los valores en `lib/firebase_options.dart` con los datos de tu proyecto:

```dart
static const FirebaseOptions web = FirebaseOptions(
  apiKey: 'TU_WEB_API_KEY',
  appId: 'TU_WEB_APP_ID',
  messagingSenderId: 'TU_SENDER_ID',
  projectId: 'tu-project-id',
  authDomain: 'tu-project-id.firebaseapp.com',
  storageBucket: 'tu-project-id.appspot.com',
  measurementId: 'TU_MEASUREMENT_ID',
);

static const FirebaseOptions android = FirebaseOptions(
  apiKey: 'TU_ANDROID_API_KEY',
  appId: 'TU_ANDROID_APP_ID',
  messagingSenderId: 'TU_SENDER_ID',
  projectId: 'tu-project-id',
  storageBucket: 'tu-project-id.appspot.com',
);
```

### 5. Habilitar Servicios en Firebase

#### Authentication
1. Ve a Authentication → Sign-in method
2. Habilita "Email/Password"
3. Configura dominios autorizados si es necesario

#### Firestore Database
1. Ve a Firestore Database
2. Haz clic en "Crear base de datos"
3. Selecciona modo de prueba (para desarrollo)
4. Elige una ubicación cercana

#### Storage
1. Ve a Storage
2. Haz clic en "Comenzar"
3. Configura reglas de seguridad según necesites

### 6. Configurar Reglas de Seguridad

#### Firestore Rules (Desarrollo)
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Permitir lectura/escritura a usuarios autenticados
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

#### Storage Rules (Desarrollo)
```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /{allPaths=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

## 🛠️ Comandos Útiles

### Instalar FlutterFire CLI
```bash
dart pub global activate flutterfire_cli
```

### Configurar automáticamente (alternativa)
```bash
flutterfire configure
```

### Ejecutar en Android
```bash
flutter run -d android
```

### Ejecutar en Web
```bash
flutter run -d chrome
```

## 📱 Funcionalidades Implementadas

- ✅ **Autenticación con Email/Password**
- ✅ **Registro de usuarios**
- ✅ **Inicio de sesión**
- ✅ **Restablecimiento de contraseña**
- ✅ **Estado de autenticación persistente**
- ✅ **Navegación automática según estado de auth**
- ✅ **Servicio Firebase centralizado**
- ✅ **Manejo de errores de autenticación**

## 🔧 Próximos Pasos

1. **Configurar el proyecto en Firebase Console**
2. **Actualizar firebase_options.dart con tus credenciales**
3. **Colocar google-services.json en android/app/**
4. **Probar la autenticación**
5. **Migrar datos de SQLite a Firestore (opcional)**

## 🚨 Notas Importantes

- **Los certificados SHA-1 son específicos para este keystore**
- **Guarda el keystore de release en lugar seguro**
- **Las reglas de Firestore mostradas son para desarrollo**
- **Para producción, implementa reglas de seguridad más estrictas**
- **El archivo firebase_options.dart actual tiene valores placeholder**

## 📞 Soporte

Si necesitas ayuda con la configuración, revisa:
- [Documentación oficial de FlutterFire](https://firebase.flutter.dev/)
- [Firebase Console](https://console.firebase.google.com/)
- [Guía de configuración de Android](https://firebase.flutter.dev/docs/installation/android)
