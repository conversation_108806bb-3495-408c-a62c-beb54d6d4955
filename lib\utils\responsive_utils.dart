import 'package:flutter/material.dart';

/// Utilidades para diseño responsive
/// Proporciona breakpoints, dimensiones adaptativas y helpers para diferentes tamaños de pantalla
class ResponsiveUtils {
  // Breakpoints estándar
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 900;
  static const double desktopBreakpoint = 1200;
  static const double largeDesktopBreakpoint = 1600;

  /// Obtiene el tipo de dispositivo basado en el ancho de pantalla
  static DeviceType getDeviceType(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    
    if (width < mobileBreakpoint) {
      return DeviceType.mobile;
    } else if (width < tabletBreakpoint) {
      return DeviceType.tablet;
    } else if (width < desktopBreakpoint) {
      return DeviceType.desktop;
    } else {
      return DeviceType.largeDesktop;
    }
  }

  /// Verifica si es dispositivo móvil
  static bool isMobile(BuildContext context) {
    return getDeviceType(context) == DeviceType.mobile;
  }

  /// Verifica si es tablet
  static bool isTablet(BuildContext context) {
    return getDeviceType(context) == DeviceType.tablet;
  }

  /// Verifica si es desktop
  static bool isDesktop(BuildContext context) {
    final type = getDeviceType(context);
    return type == DeviceType.desktop || type == DeviceType.largeDesktop;
  }

  /// Verifica si la orientación es landscape
  static bool isLandscape(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.landscape;
  }

  /// Verifica si la orientación es portrait
  static bool isPortrait(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.portrait;
  }

  /// Obtiene el número de columnas para grid basado en el tamaño de pantalla
  static int getGridColumns(BuildContext context, {int? mobileColumns, int? tabletColumns, int? desktopColumns}) {
    final deviceType = getDeviceType(context);
    final isLandscapeMode = isLandscape(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        if (isLandscapeMode) {
          return (mobileColumns ?? 2) + 1; // Una columna extra en landscape
        }
        return mobileColumns ?? 2;
      case DeviceType.tablet:
        if (isLandscapeMode) {
          return (tabletColumns ?? 3) + 1;
        }
        return tabletColumns ?? 3;
      case DeviceType.desktop:
      case DeviceType.largeDesktop:
        return desktopColumns ?? 4;
    }
  }

  /// Obtiene padding adaptativo
  static EdgeInsets getAdaptivePadding(BuildContext context, {
    double? mobile,
    double? tablet,
    double? desktop,
  }) {
    final deviceType = getDeviceType(context);
    double padding;
    
    switch (deviceType) {
      case DeviceType.mobile:
        padding = mobile ?? 16.0;
        break;
      case DeviceType.tablet:
        padding = tablet ?? 24.0;
        break;
      case DeviceType.desktop:
      case DeviceType.largeDesktop:
        padding = desktop ?? 32.0;
        break;
    }
    
    return EdgeInsets.all(padding);
  }

  /// Obtiene ancho máximo para contenido centrado
  static double getMaxContentWidth(BuildContext context) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return double.infinity; // Usa todo el ancho disponible
      case DeviceType.tablet:
        return 700;
      case DeviceType.desktop:
        return 900;
      case DeviceType.largeDesktop:
        return 1200;
    }
  }

  /// Obtiene tamaño de fuente adaptativo
  static double getAdaptiveFontSize(BuildContext context, {
    double? mobile,
    double? tablet,
    double? desktop,
  }) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return mobile ?? 14.0;
      case DeviceType.tablet:
        return tablet ?? 16.0;
      case DeviceType.desktop:
      case DeviceType.largeDesktop:
        return desktop ?? 18.0;
    }
  }

  /// Obtiene espaciado adaptativo
  static double getAdaptiveSpacing(BuildContext context, {
    double? mobile,
    double? tablet,
    double? desktop,
  }) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return mobile ?? 8.0;
      case DeviceType.tablet:
        return tablet ?? 12.0;
      case DeviceType.desktop:
      case DeviceType.largeDesktop:
        return desktop ?? 16.0;
    }
  }

  /// Obtiene altura de elementos UI adaptativa
  static double getAdaptiveHeight(BuildContext context, {
    double? mobile,
    double? tablet,
    double? desktop,
  }) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return mobile ?? 48.0;
      case DeviceType.tablet:
        return tablet ?? 56.0;
      case DeviceType.desktop:
      case DeviceType.largeDesktop:
        return desktop ?? 64.0;
    }
  }

  /// Obtiene el aspect ratio para cards/elementos
  static double getCardAspectRatio(BuildContext context) {
    final deviceType = getDeviceType(context);
    final isLandscapeMode = isLandscape(context);
    
    if (isLandscapeMode) {
      return 1.2; // Más ancho en landscape
    }
    
    switch (deviceType) {
      case DeviceType.mobile:
        return 1.0; // Cuadrado en móvil
      case DeviceType.tablet:
        return 1.1;
      case DeviceType.desktop:
      case DeviceType.largeDesktop:
        return 1.2;
    }
  }

  /// Verifica si debe mostrar sidebar/drawer
  static bool shouldShowSidebar(BuildContext context) {
    return isDesktop(context) && MediaQuery.of(context).size.width > 1000;
  }

  /// Obtiene el tipo de navegación recomendado
  static NavigationType getNavigationType(BuildContext context) {
    if (isMobile(context)) {
      return NavigationType.drawer;
    } else if (isTablet(context)) {
      return isLandscape(context) ? NavigationType.rail : NavigationType.drawer;
    } else {
      return NavigationType.sidebar;
    }
  }

  /// Calcula el número de elementos por página para listas paginadas
  static int getItemsPerPage(BuildContext context) {
    final deviceType = getDeviceType(context);
    final screenHeight = MediaQuery.of(context).size.height;
    
    // Estimar elementos visibles basado en altura de pantalla
    int baseItems;
    switch (deviceType) {
      case DeviceType.mobile:
        baseItems = 10;
        break;
      case DeviceType.tablet:
        baseItems = 15;
        break;
      case DeviceType.desktop:
      case DeviceType.largeDesktop:
        baseItems = 20;
        break;
    }
    
    // Ajustar basado en altura real
    if (screenHeight > 800) {
      baseItems += 5;
    } else if (screenHeight < 600) {
      baseItems -= 5;
    }
    
    return baseItems.clamp(5, 50); // Mínimo 5, máximo 50
  }
}

/// Enum para tipos de dispositivo
enum DeviceType {
  mobile,
  tablet,
  desktop,
  largeDesktop,
}

/// Enum para tipos de navegación
enum NavigationType {
  drawer,
  rail,
  sidebar,
}

/// Widget helper para layouts responsive
class ResponsiveBuilder extends StatelessWidget {
  final Widget Function(BuildContext context, DeviceType deviceType) builder;
  final Widget? mobile;
  final Widget? tablet;
  final Widget? desktop;

  const ResponsiveBuilder({
    super.key,
    required this.builder,
    this.mobile,
    this.tablet,
    this.desktop,
  });

  @override
  Widget build(BuildContext context) {
    final deviceType = ResponsiveUtils.getDeviceType(context);
    
    // Si se proporcionan widgets específicos, usarlos
    switch (deviceType) {
      case DeviceType.mobile:
        if (mobile != null) return mobile!;
        break;
      case DeviceType.tablet:
        if (tablet != null) return tablet!;
        break;
      case DeviceType.desktop:
      case DeviceType.largeDesktop:
        if (desktop != null) return desktop!;
        break;
    }
    
    // Usar el builder como fallback
    return builder(context, deviceType);
  }
}
