import 'package:cloud_firestore/cloud_firestore.dart';
import '../services/firebase_service.dart';
import '../models/factura_model.dart';
import '../models/detalle_factura_model.dart';
import 'dart:developer';

class FacturaService {
  static const String _facturaCollection = 'facturas';
  static const String _detalleCollection = 'detalles_factura';

  /// Obtener todas las facturas
  static Future<List<Factura>> obtenerFacturas() async {
    try {
      final snapshot = await FirebaseService.getCollection(
        collection: _facturaCollection,
        queryBuilder: (query) =>
            query.orderBy('fechaCreacion', descending: true),
      );

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return Factura(
          idFactura: data['idFactura'] ?? doc.id,
          cliente: data['clienteId'] ?? '',
          metodoPago: data['metodoPago'] ?? '',
          total: (data['total'] ?? 0.0).toDouble(),
          comprobante: data['comprobante'] ?? '',
        );
      }).toList();
    } catch (e) {
      log('Error obteniendo facturas: $e');
      return [];
    }
  }

  /// Obtener facturas con paginación
  static Future<List<Map<String, dynamic>>> obtenerFacturasPaginadas({
    int limit = 20,
    int offset = 0,
    String? searchQuery,
    String? clienteId,
    String? metodoPago,
    DateTime? fechaInicio,
    DateTime? fechaFin,
  }) async {
    try {
      log('🔍 Obteniendo facturas paginadas - limit: $limit, offset: $offset, searchQuery: $searchQuery');
      Query query = FirebaseFirestore.instance.collection(_facturaCollection);

      // Aplicar filtros
      if (clienteId != null && clienteId.isNotEmpty) {
        query = query.where('clienteId', isEqualTo: clienteId);
      }

      if (metodoPago != null && metodoPago.isNotEmpty) {
        query = query.where('metodoPago', isEqualTo: metodoPago);
      }

      if (fechaInicio != null) {
        query = query.where('fechaCreacion',
            isGreaterThanOrEqualTo: Timestamp.fromDate(fechaInicio));
      }

      if (fechaFin != null) {
        query = query.where('fechaCreacion',
            isLessThanOrEqualTo: Timestamp.fromDate(fechaFin));
      }

      // Ordenar y paginar
      query = query.orderBy('fechaCreacion', descending: true);

      // Aplicar offset si es necesario
      if (offset > 0) {
        // Para Firebase, necesitamos usar startAfter con un documento específico
        // Por simplicidad, vamos a usar limit y skip en memoria por ahora
        query = query.limit(limit + offset);
      } else {
        query = query.limit(limit);
      }

      final snapshot = await query.get();
      log('📊 Facturas encontradas: ${snapshot.docs.length}');

      // Aplicar offset en memoria si es necesario
      List<QueryDocumentSnapshot> docs = snapshot.docs;
      if (offset > 0 && docs.length > offset) {
        docs = docs.skip(offset).take(limit).toList();
      }

      final facturas = docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        final timestamp = data['fechaCreacion'] as Timestamp?;

        return {
          'IdFactura': data['idFactura'] ?? doc.id,
          'Cliente': data['clienteNombre'] ?? data['clienteId'] ?? '',
          'ClienteNombre': data['clienteNombre'] ?? data['clienteId'] ?? '',
          'MetodoPago': data['metodoPago'] ?? '',
          'Total': (data['total'] ?? 0.0).toDouble(),
          'FechaCreacion': timestamp?.toDate() ?? DateTime.now(),
          'Estado': data['estado'] ?? 'completada',
          'Comprobante': data['comprobante'] ?? '',
        };
      }).toList();

      log('✅ Facturas procesadas: ${facturas.length}');
      return facturas;
    } catch (e) {
      log('Error obteniendo facturas paginadas: $e');
      return [];
    }
  }

  /// Contar facturas
  static Future<int> contarFacturas({
    String? clienteId,
    String? metodoPago,
    DateTime? fechaInicio,
    DateTime? fechaFin,
  }) async {
    try {
      log('🔢 Contando facturas...');
      Query query = FirebaseFirestore.instance.collection(_facturaCollection);

      if (clienteId != null && clienteId.isNotEmpty) {
        query = query.where('clienteId', isEqualTo: clienteId);
      }

      if (metodoPago != null && metodoPago.isNotEmpty) {
        query = query.where('metodoPago', isEqualTo: metodoPago);
      }

      if (fechaInicio != null) {
        query = query.where('fechaCreacion',
            isGreaterThanOrEqualTo: Timestamp.fromDate(fechaInicio));
      }

      if (fechaFin != null) {
        query = query.where('fechaCreacion',
            isLessThanOrEqualTo: Timestamp.fromDate(fechaFin));
      }

      final snapshot = await query.get();
      final count = snapshot.docs.length;
      log('📊 Total de facturas encontradas: $count');
      return count;
    } catch (e) {
      log('Error contando facturas: $e');
      return 0;
    }
  }

  /// Insertar factura con detalles
  static Future<void> insertarFactura(
      Factura factura, List<DetalleFactura> detalles) async {
    try {
      // Obtener el nombre real del cliente
      String clienteNombre = factura.cliente; // Por defecto usar el ID
      try {
        final clienteDoc = await FirebaseFirestore.instance
            .collection('clientes')
            .doc(factura.cliente)
            .get();
        if (clienteDoc.exists) {
          final clienteData = clienteDoc.data() as Map<String, dynamic>;
          clienteNombre = clienteData['nombre'] ?? factura.cliente;
        }
      } catch (e) {
        log('Error obteniendo nombre del cliente: $e');
        // Continuar con el ID como fallback
      }

      // Usar transacción para asegurar consistencia
      await FirebaseFirestore.instance.runTransaction((transaction) async {
        // Insertar factura
        final facturaRef = FirebaseFirestore.instance
            .collection(_facturaCollection)
            .doc(factura.idFactura);
        transaction.set(facturaRef, {
          'idFactura': factura.idFactura,
          'clienteId': factura.cliente,
          'clienteNombre': clienteNombre, // Ahora usa el nombre real
          'metodoPago': factura.metodoPago,
          'total': factura.total,
          'estado': 'completada',
          'fechaCreacion': FieldValue.serverTimestamp(),
          'comprobante': factura.comprobante,
        });

        // Insertar detalles
        for (final detalle in detalles) {
          final detalleRef = FirebaseFirestore.instance
              .collection(_detalleCollection)
              .doc(detalle.idDetalle);
          transaction.set(detalleRef, {
            'idDetalle': detalle.idDetalle,
            'facturaId': detalle.idFactura,
            'moduloNombre': detalle.modulo,
            'cantidad': detalle.cantidad,
            'precioUnitario': detalle.precioModulo,
            'subtotal': detalle.cantidad * detalle.precioModulo,
            'suscripcion': detalle.suscripcion,
            'fechaCreacion': FieldValue.serverTimestamp(),
          });
        }
      });

      log('Factura insertada exitosamente: ${factura.idFactura}');
    } catch (e) {
      log('Error insertando factura: $e');
      rethrow;
    }
  }

  /// Obtener detalles de una factura
  static Future<List<DetalleFactura>> obtenerDetalleFactura(
      String idFactura) async {
    try {
      final snapshot = await FirebaseService.getCollection(
        collection: _detalleCollection,
        queryBuilder: (query) => query.where('facturaId', isEqualTo: idFactura),
      );

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return DetalleFactura(
          idDetalle: data['idDetalle'] ?? doc.id,
          idFactura: data['facturaId'] ?? '',
          modulo: data['moduloNombre'] ?? '',
          cantidad: data['cantidad'] ?? 1,
          precioModulo: (data['precioUnitario'] ?? 0.0).toDouble(),
          suscripcion: data['suscripcion'] ?? false,
        );
      }).toList();
    } catch (e) {
      log('Error obteniendo detalles de factura: $e');
      return [];
    }
  }

  /// Eliminar factura y sus detalles
  static Future<void> eliminarFactura(String idFactura) async {
    try {
      await FirebaseFirestore.instance.runTransaction((transaction) async {
        // Eliminar detalles primero
        final detallesSnapshot = await FirebaseFirestore.instance
            .collection(_detalleCollection)
            .where('facturaId', isEqualTo: idFactura)
            .get();

        for (final doc in detallesSnapshot.docs) {
          transaction.delete(doc.reference);
        }

        // Eliminar factura
        final facturaRef = FirebaseFirestore.instance
            .collection(_facturaCollection)
            .doc(idFactura);
        transaction.delete(facturaRef);
      });

      log('Factura eliminada exitosamente: $idFactura');
    } catch (e) {
      log('Error eliminando factura: $e');
      rethrow;
    }
  }

  /// Obtener factura por ID
  static Future<Factura?> obtenerFacturaPorId(String idFactura) async {
    try {
      final doc = await FirebaseService.getDocument(
        collection: _facturaCollection,
        docId: idFactura,
      );

      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;
        return Factura(
          idFactura: data['idFactura'] ?? doc.id,
          cliente: data['clienteId'] ?? '',
          metodoPago: data['metodoPago'] ?? '',
          total: (data['total'] ?? 0.0).toDouble(),
          comprobante: data['comprobante'] ?? '',
        );
      }
      return null;
    } catch (e) {
      log('Error obteniendo factura por ID: $e');
      return null;
    }
  }

  /// Obtener estadísticas de ventas
  static Future<Map<String, dynamic>> obtenerEstadisticasVentas({
    DateTime? fechaInicio,
    DateTime? fechaFin,
  }) async {
    try {
      Query query = FirebaseFirestore.instance.collection(_facturaCollection);

      if (fechaInicio != null) {
        query = query.where('fechaCreacion',
            isGreaterThanOrEqualTo: Timestamp.fromDate(fechaInicio));
      }

      if (fechaFin != null) {
        query = query.where('fechaCreacion',
            isLessThanOrEqualTo: Timestamp.fromDate(fechaFin));
      }

      final snapshot = await query.get();

      double totalVentas = 0;
      int totalFacturas = snapshot.docs.length;
      Map<String, int> ventasPorMetodo = {};

      for (final doc in snapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final total = (data['total'] ?? 0.0).toDouble();
        final metodoPago = data['metodoPago'] ?? 'Sin especificar';

        totalVentas += total;
        ventasPorMetodo[metodoPago] = (ventasPorMetodo[metodoPago] ?? 0) + 1;
      }

      return {
        'totalVentas': totalVentas,
        'totalFacturas': totalFacturas,
        'promedioVenta': totalFacturas > 0 ? totalVentas / totalFacturas : 0,
        'ventasPorMetodo': ventasPorMetodo,
      };
    } catch (e) {
      log('Error obteniendo estadísticas de ventas: $e');
      return {
        'totalVentas': 0.0,
        'totalFacturas': 0,
        'promedioVenta': 0.0,
        'ventasPorMetodo': <String, int>{},
      };
    }
  }
}
