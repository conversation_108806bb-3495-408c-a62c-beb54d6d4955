import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Widget de deslizamiento para acciones (swipe actions)
class SwipeActionCard extends StatefulWidget {
  final Widget child;
  final List<SwipeAction>? leftActions;
  final List<SwipeAction>? rightActions;
  final double threshold;
  final Duration animationDuration;

  const SwipeActionCard({
    super.key,
    required this.child,
    this.leftActions,
    this.rightActions,
    this.threshold = 0.3,
    this.animationDuration = const Duration(milliseconds: 200),
  });

  @override
  State<SwipeActionCard> createState() => _SwipeActionCardState();
}

class _SwipeActionCardState extends State<SwipeActionCard>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Offset> _offsetAnimation;
  
  double _dragExtent = 0;
  bool _dragUnderway = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    _offsetAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: Offset.zero,
    ).animate(_controller);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _handleDragStart(DragStartDetails details) {
    _dragUnderway = true;
    if (_controller.isAnimating) {
      _controller.stop();
    }
  }

  void _handleDragUpdate(DragUpdateDetails details) {
    if (!_dragUnderway) return;

    final delta = details.primaryDelta! / context.size!.width;
    _dragExtent += delta;

    setState(() {
      _offsetAnimation = Tween<Offset>(
        begin: Offset.zero,
        end: Offset(_dragExtent, 0),
      ).animate(_controller);
    });
  }

  void _handleDragEnd(DragEndDetails details) {
    if (!_dragUnderway) return;
    _dragUnderway = false;

    final velocity = details.primaryVelocity! / context.size!.width;
    
    if (_dragExtent.abs() > widget.threshold || velocity.abs() > 0.5) {
      if (_dragExtent > 0 && widget.leftActions != null) {
        _executeAction(widget.leftActions!.first);
      } else if (_dragExtent < 0 && widget.rightActions != null) {
        _executeAction(widget.rightActions!.first);
      }
    }
    
    _resetPosition();
  }

  void _executeAction(SwipeAction action) {
    HapticFeedback.mediumImpact();
    action.onPressed();
  }

  void _resetPosition() {
    _controller.animateTo(0).then((_) {
      setState(() {
        _dragExtent = 0;
        _offsetAnimation = Tween<Offset>(
          begin: Offset.zero,
          end: Offset.zero,
        ).animate(_controller);
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onHorizontalDragStart: _handleDragStart,
      onHorizontalDragUpdate: _handleDragUpdate,
      onHorizontalDragEnd: _handleDragEnd,
      child: Stack(
        children: [
          // Acciones de fondo
          if (_dragExtent > 0 && widget.leftActions != null)
            _buildActionBackground(widget.leftActions!, true),
          if (_dragExtent < 0 && widget.rightActions != null)
            _buildActionBackground(widget.rightActions!, false),
          
          // Contenido principal
          AnimatedBuilder(
            animation: _offsetAnimation,
            builder: (context, child) {
              return Transform.translate(
                offset: Offset(_dragExtent * context.size!.width, 0),
                child: widget.child,
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildActionBackground(List<SwipeAction> actions, bool isLeft) {
    return Positioned.fill(
      child: Container(
        color: actions.first.backgroundColor,
        child: Row(
          mainAxisAlignment: isLeft ? MainAxisAlignment.start : MainAxisAlignment.end,
          children: [
            if (isLeft) const SizedBox(width: 20),
            Icon(
              actions.first.icon,
              color: actions.first.iconColor ?? Colors.white,
              size: 24,
            ),
            const SizedBox(width: 8),
            Text(
              actions.first.label,
              style: TextStyle(
                color: actions.first.iconColor ?? Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            if (!isLeft) const SizedBox(width: 20),
          ],
        ),
      ),
    );
  }
}

/// Acción de deslizamiento
class SwipeAction {
  final String label;
  final IconData icon;
  final Color backgroundColor;
  final Color? iconColor;
  final VoidCallback onPressed;

  const SwipeAction({
    required this.label,
    required this.icon,
    required this.backgroundColor,
    required this.onPressed,
    this.iconColor,
  });
}

/// Widget de pull-to-refresh personalizado
class CustomRefreshIndicator extends StatefulWidget {
  final Widget child;
  final Future<void> Function() onRefresh;
  final Color? color;
  final double displacement;

  const CustomRefreshIndicator({
    super.key,
    required this.child,
    required this.onRefresh,
    this.color,
    this.displacement = 40.0,
  });

  @override
  State<CustomRefreshIndicator> createState() => _CustomRefreshIndicatorState();
}

class _CustomRefreshIndicatorState extends State<CustomRefreshIndicator>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0, end: 1).animate(_controller);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final color = widget.color ?? Theme.of(context).primaryColor;
    
    return RefreshIndicator(
      onRefresh: () async {
        _controller.forward();
        HapticFeedback.mediumImpact();
        await widget.onRefresh();
        _controller.reverse();
      },
      displacement: widget.displacement,
      color: color,
      backgroundColor: Colors.white,
      strokeWidth: 3,
      child: widget.child,
    );
  }
}

/// Widget de lista con gestos táctiles optimizados
class TouchOptimizedList extends StatefulWidget {
  final List<Widget> children;
  final ScrollController? controller;
  final EdgeInsets? padding;
  final bool shrinkWrap;
  final ScrollPhysics? physics;

  const TouchOptimizedList({
    super.key,
    required this.children,
    this.controller,
    this.padding,
    this.shrinkWrap = false,
    this.physics,
  });

  @override
  State<TouchOptimizedList> createState() => _TouchOptimizedListState();
}

class _TouchOptimizedListState extends State<TouchOptimizedList> {
  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      controller: widget.controller,
      padding: widget.padding ?? const EdgeInsets.all(16),
      shrinkWrap: widget.shrinkWrap,
      physics: widget.physics ?? const BouncingScrollPhysics(),
      itemCount: widget.children.length,
      separatorBuilder: (context, index) => const SizedBox(height: 8),
      itemBuilder: (context, index) {
        return TapFeedbackWrapper(
          child: widget.children[index],
        );
      },
    );
  }
}

/// Wrapper que agrega feedback táctil
class TapFeedbackWrapper extends StatefulWidget {
  final Widget child;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;

  const TapFeedbackWrapper({
    super.key,
    required this.child,
    this.onTap,
    this.onLongPress,
  });

  @override
  State<TapFeedbackWrapper> createState() => _TapFeedbackWrapperState();
}

class _TapFeedbackWrapperState extends State<TapFeedbackWrapper>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.98).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: (_) {
              _controller.forward();
              HapticFeedback.lightImpact();
            },
            onTapUp: (_) => _controller.reverse(),
            onTapCancel: () => _controller.reverse(),
            onTap: widget.onTap,
            onLongPress: () {
              HapticFeedback.mediumImpact();
              widget.onLongPress?.call();
            },
            child: widget.child,
          ),
        );
      },
    );
  }
}

/// Bottom sheet moderno con gestos
class ModernBottomSheet extends StatefulWidget {
  final Widget child;
  final double initialChildSize;
  final double minChildSize;
  final double maxChildSize;
  final bool expand;
  final bool snap;

  const ModernBottomSheet({
    super.key,
    required this.child,
    this.initialChildSize = 0.5,
    this.minChildSize = 0.25,
    this.maxChildSize = 1.0,
    this.expand = true,
    this.snap = true,
  });

  @override
  State<ModernBottomSheet> createState() => _ModernBottomSheetState();
}

class _ModernBottomSheetState extends State<ModernBottomSheet> {
  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(
      initialChildSize: widget.initialChildSize,
      minChildSize: widget.minChildSize,
      maxChildSize: widget.maxChildSize,
      expand: widget.expand,
      snap: widget.snap,
      builder: (context, scrollController) {
        return Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black26,
                blurRadius: 10,
                offset: Offset(0, -2),
              ),
            ],
          ),
          child: Column(
            children: [
              // Handle para arrastrar
              Container(
                margin: const EdgeInsets.only(top: 8),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(height: 16),
              
              // Contenido
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  child: widget.child,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

/// Mostrar bottom sheet moderno
void showModernBottomSheet({
  required BuildContext context,
  required Widget child,
  double initialChildSize = 0.5,
  double minChildSize = 0.25,
  double maxChildSize = 1.0,
  bool isDismissible = true,
  bool enableDrag = true,
}) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    isDismissible: isDismissible,
    enableDrag: enableDrag,
    backgroundColor: Colors.transparent,
    builder: (context) => ModernBottomSheet(
      initialChildSize: initialChildSize,
      minChildSize: minChildSize,
      maxChildSize: maxChildSize,
      child: child,
    ),
  );
}
