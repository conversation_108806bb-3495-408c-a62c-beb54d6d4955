/// Excepciones personalizadas para operaciones de base de datos
///
/// Estas clases proporcionan un manejo de errores más específico y útil
/// para las operaciones de la base de datos.
library;

/// Excepción base para errores de base de datos
abstract class DatabaseException implements Exception {
  final String message;
  final String? details;
  final Exception? originalException;

  const DatabaseException(this.message, {this.details, this.originalException});

  @override
  String toString() {
    if (details != null) {
      return 'DatabaseException: $message\nDetalles: $details';
    }
    return 'DatabaseException: $message';
  }
}

/// Excepción para errores de inicialización de base de datos
class DatabaseInitializationException extends DatabaseException {
  const DatabaseInitializationException(
    super.message, {
    super.details,
    super.originalException,
  });
}

/// Excepción para errores de inserción
class DatabaseInsertException extends DatabaseException {
  final String tableName;

  const DatabaseInsertException(
    super.message,
    this.tableName, {
    super.details,
    super.originalException,
  });

  @override
  String toString() {
    return 'Error al insertar en tabla $tableName: $message';
  }
}

/// Excepción para errores de actualización
class DatabaseUpdateException extends DatabaseException {
  final String tableName;

  const DatabaseUpdateException(
    super.message,
    this.tableName, {
    super.details,
    super.originalException,
  });

  @override
  String toString() {
    return 'Error al actualizar tabla $tableName: $message';
  }
}

/// Excepción para errores de eliminación
class DatabaseDeleteException extends DatabaseException {
  final String tableName;

  const DatabaseDeleteException(
    super.message,
    this.tableName, {
    super.details,
    super.originalException,
  });

  @override
  String toString() {
    return 'Error al eliminar de tabla $tableName: $message';
  }
}

/// Excepción para errores de consulta
class DatabaseQueryException extends DatabaseException {
  final String tableName;

  const DatabaseQueryException(
    super.message,
    this.tableName, {
    super.details,
    super.originalException,
  });

  @override
  String toString() {
    return 'Error al consultar tabla $tableName: $message';
  }
}

/// Excepción para violaciones de restricciones
class DatabaseConstraintException extends DatabaseException {
  final String constraintType;

  const DatabaseConstraintException(
    super.message,
    this.constraintType, {
    super.details,
    super.originalException,
  });

  @override
  String toString() {
    return 'Violación de restricción $constraintType: $message';
  }
}

/// Excepción para registros no encontrados
class RecordNotFoundException extends DatabaseException {
  final String tableName;
  final String searchCriteria;
  
  const RecordNotFoundException(
    this.tableName,
    this.searchCriteria, {
    String? details,
  }) : super(
    'Registro no encontrado en $tableName con criterio: $searchCriteria',
    details: details,
  );
}

/// Excepción para datos duplicados
class DuplicateRecordException extends DatabaseException {
  final String tableName;
  final String field;
  
  const DuplicateRecordException(
    this.tableName,
    this.field, {
    String? details,
  }) : super(
    'Ya existe un registro en $tableName con el mismo $field',
    details: details,
  );
}
