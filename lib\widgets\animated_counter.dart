import 'package:flutter/material.dart';
import 'dart:math' as math;

/// Widget para números animados con efectos visuales avanzados
class AnimatedCounter extends StatefulWidget {
  final double value;
  final String prefix;
  final String suffix;
  final TextStyle? textStyle;
  final Duration duration;
  final Curve curve;
  final bool showSparkline;
  final List<double>? sparklineData;
  final Color? sparklineColor;
  final bool showTrendArrow;
  final double? previousValue;

  const AnimatedCounter({
    super.key,
    required this.value,
    this.prefix = '',
    this.suffix = '',
    this.textStyle,
    this.duration = const Duration(milliseconds: 1500),
    this.curve = Curves.easeOutCubic,
    this.showSparkline = false,
    this.sparklineData,
    this.sparklineColor,
    this.showTrendArrow = false,
    this.previousValue,
  });

  @override
  State<AnimatedCounter> createState() => _AnimatedCounterState();
}

class _AnimatedCounterState extends State<AnimatedCounter>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;
  
  double _currentValue = 0;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.elasticOut,
    ));
    
    _updateAnimation();
  }

  void _updateAnimation() {
    _animation = Tween<double>(
      begin: _currentValue,
      end: widget.value,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: widget.curve,
    ));
    
    _controller.forward(from: 0);
    _pulseController.forward(from: 0);
  }

  @override
  void didUpdateWidget(AnimatedCounter oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      _currentValue = oldWidget.value;
      _updateAnimation();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  String _formatNumber(double value) {
    if (value >= 1000000) {
      return '${(value / 1000000).toStringAsFixed(1)}M';
    } else if (value >= 1000) {
      return '${(value / 1000).toStringAsFixed(1)}K';
    } else {
      return value.toStringAsFixed(0);
    }
  }

  Widget _buildTrendArrow() {
    if (!widget.showTrendArrow || widget.previousValue == null) {
      return const SizedBox();
    }

    final isUp = widget.value > widget.previousValue!;
    final isFlat = widget.value == widget.previousValue!;
    
    if (isFlat) return const SizedBox();

    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 600),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.scale(
          scale: value,
          child: Icon(
            isUp ? Icons.trending_up : Icons.trending_down,
            color: isUp ? Colors.green : Colors.red,
            size: 16,
          ),
        );
      },
    );
  }

  Widget _buildSparkline() {
    if (!widget.showSparkline || widget.sparklineData == null || widget.sparklineData!.isEmpty) {
      return const SizedBox();
    }

    return SizedBox(
      width: 60,
      height: 20,
      child: CustomPaint(
        painter: SparklinePainter(
          data: widget.sparklineData!,
          color: widget.sparklineColor ?? Colors.blue,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_animation, _pulseAnimation]),
      builder: (context, child) {
        return Transform.scale(
          scale: _pulseAnimation.value,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    '${widget.prefix}${_formatNumber(_animation.value)}${widget.suffix}',
                    style: widget.textStyle ?? const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(width: 4),
                  _buildTrendArrow(),
                ],
              ),
              if (widget.showSparkline) ...[
                const SizedBox(height: 4),
                _buildSparkline(),
              ],
            ],
          ),
        );
      },
    );
  }
}

/// Painter para mini gráficos sparkline
class SparklinePainter extends CustomPainter {
  final List<double> data;
  final Color color;

  SparklinePainter({required this.data, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    if (data.length < 2) return;

    final paint = Paint()
      ..color = color
      ..strokeWidth = 1.5
      ..style = PaintingStyle.stroke;

    final path = Path();
    final maxValue = data.reduce(math.max);
    final minValue = data.reduce(math.min);
    final range = maxValue - minValue;

    if (range == 0) return;

    for (int i = 0; i < data.length; i++) {
      final x = (i / (data.length - 1)) * size.width;
      final y = size.height - ((data[i] - minValue) / range) * size.height;

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }

    canvas.drawPath(path, paint);

    // Dibujar puntos en los extremos
    final pointPaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    // Primer punto
    canvas.drawCircle(
      Offset(0, size.height - ((data.first - minValue) / range) * size.height),
      1.5,
      pointPaint,
    );

    // Último punto
    canvas.drawCircle(
      Offset(size.width, size.height - ((data.last - minValue) / range) * size.height),
      1.5,
      pointPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// Widget para contador estilo odómetro
class OdometerCounter extends StatefulWidget {
  final int value;
  final int digits;
  final TextStyle? textStyle;
  final Duration duration;

  const OdometerCounter({
    super.key,
    required this.value,
    this.digits = 6,
    this.textStyle,
    this.duration = const Duration(milliseconds: 1000),
  });

  @override
  State<OdometerCounter> createState() => _OdometerCounterState();
}

class _OdometerCounterState extends State<OdometerCounter>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;
  int _previousValue = 0;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _controllers = List.generate(
      widget.digits,
      (index) => AnimationController(
        duration: widget.duration,
        vsync: this,
      ),
    );

    _animations = _controllers.map((controller) =>
        Tween<double>(begin: 0, end: 1).animate(
          CurvedAnimation(parent: controller, curve: Curves.easeOutCubic),
        )).toList();

    _updateValue();
  }

  void _updateValue() {
    final valueStr = widget.value.toString().padLeft(widget.digits, '0');
    final previousStr = _previousValue.toString().padLeft(widget.digits, '0');

    for (int i = 0; i < widget.digits; i++) {
      final currentDigit = int.parse(valueStr[i]);
      final previousDigit = int.parse(previousStr[i]);

      if (currentDigit != previousDigit) {
        _controllers[i].forward(from: 0);
      }
    }

    _previousValue = widget.value;
  }

  @override
  void didUpdateWidget(OdometerCounter oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      _updateValue();
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final valueStr = widget.value.toString().padLeft(widget.digits, '0');

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(widget.digits, (index) {
        return AnimatedBuilder(
          animation: _animations[index],
          builder: (context, child) {
            final digit = int.parse(valueStr[index]);
            return SizedBox(
              width: 20,
              height: 30,
              child: ClipRect(
                child: Stack(
                  children: [
                    Transform.translate(
                      offset: Offset(0, -30 * _animations[index].value),
                      child: Column(
                        children: List.generate(10, (digitIndex) {
                          return SizedBox(
                            height: 30,
                            child: Center(
                              child: Text(
                                digitIndex.toString(),
                                style: widget.textStyle ?? const TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          );
                        }),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      }),
    );
  }
}

/// Widget para progreso circular animado
class AnimatedCircularProgress extends StatefulWidget {
  final double value;
  final double size;
  final Color? color;
  final Color? backgroundColor;
  final double strokeWidth;
  final Widget? child;
  final Duration duration;

  const AnimatedCircularProgress({
    super.key,
    required this.value,
    this.size = 100,
    this.color,
    this.backgroundColor,
    this.strokeWidth = 8,
    this.child,
    this.duration = const Duration(milliseconds: 1500),
  });

  @override
  State<AnimatedCircularProgress> createState() => _AnimatedCircularProgressState();
}

class _AnimatedCircularProgressState extends State<AnimatedCircularProgress>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    _animation = Tween<double>(begin: 0, end: widget.value).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeOutCubic),
    );
    _controller.forward();
  }

  @override
  void didUpdateWidget(AnimatedCircularProgress oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      _animation = Tween<double>(
        begin: _animation.value,
        end: widget.value,
      ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeOutCubic));
      _controller.forward(from: 0);
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return SizedBox(
          width: widget.size,
          height: widget.size,
          child: Stack(
            alignment: Alignment.center,
            children: [
              SizedBox(
                width: widget.size,
                height: widget.size,
                child: CircularProgressIndicator(
                  value: _animation.value,
                  strokeWidth: widget.strokeWidth,
                  color: widget.color ?? Theme.of(context).primaryColor,
                  backgroundColor: widget.backgroundColor ?? Colors.grey.shade300,
                ),
              ),
              if (widget.child != null) widget.child!,
            ],
          ),
        );
      },
    );
  }
}
