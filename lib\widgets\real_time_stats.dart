import 'package:flutter/material.dart';
import '../services/estadisticas_service.dart';
import '../widgets/modern_loading.dart';
import '../theme/app_gradients.dart';
import 'dart:async';

/// Widget que muestra estadísticas en tiempo real
class RealTimeStats extends StatefulWidget {
  final Duration refreshInterval;
  final bool autoRefresh;

  const RealTimeStats({
    super.key,
    this.refreshInterval = const Duration(minutes: 5),
    this.autoRefresh = true,
  });

  @override
  State<RealTimeStats> createState() => _RealTimeStatsState();
}

class _RealTimeStatsState extends State<RealTimeStats> {
  DashboardStats? _stats;
  bool _isLoading = true;
  Timer? _refreshTimer;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadStats();
    if (widget.autoRefresh) {
      _startAutoRefresh();
    }
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }

  void _startAutoRefresh() {
    _refreshTimer = Timer.periodic(widget.refreshInterval, (_) {
      if (mounted) {
        _loadStats();
      }
    });
  }

  Future<void> _loadStats() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final stats = await EstadisticasService.obtenerEstadisticasDashboard();
      
      if (mounted) {
        setState(() {
          _stats = stats;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading && _stats == null) {
      return const Center(
        child: ModernLoading(
          message: 'Cargando estadísticas...',
        ),
      );
    }

    if (_error != null && _stats == null) {
      return _buildErrorWidget();
    }

    return RefreshIndicator(
      onRefresh: _loadStats,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Column(
          children: [
            _buildHeader(),
            const SizedBox(height: 16),
            _buildStatsGrid(),
            const SizedBox(height: 16),
            _buildDetailedStats(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: AppGradients.primary,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.analytics,
            color: Colors.white,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Estadísticas en Tiempo Real',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (_stats != null)
                  Text(
                    'Actualizado: ${_formatTime(_stats!.ultimaActualizacion)}',
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 12,
                    ),
                  ),
              ],
            ),
          ),
          if (_isLoading)
            const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          else
            IconButton(
              icon: const Icon(Icons.refresh, color: Colors.white),
              onPressed: _loadStats,
              tooltip: 'Actualizar',
            ),
        ],
      ),
    );
  }

  Widget _buildStatsGrid() {
    if (_stats == null) return const SizedBox();

    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisSpacing: 12,
      mainAxisSpacing: 12,
      childAspectRatio: 1.5,
      children: [
        _buildStatCard(
          'Clientes',
          _stats!.clientes.totalFormatted,
          Icons.people,
          const Color(0xFF4CAF50),
          subtitle: '${_stats!.clientes.activos} activos',
        ),
        _buildStatCard(
          'Facturas',
          _stats!.facturas.totalFormatted,
          Icons.receipt,
          const Color(0xFF2196F3),
          subtitle: '${_stats!.facturas.esteMes} este mes',
        ),
        _buildStatCard(
          'Módulos',
          _stats!.modulos.total.toString(),
          Icons.grid_view,
          const Color(0xFF9C27B0),
          subtitle: '${_stats!.modulos.activos} activos',
        ),
        _buildStatCard(
          'Ventas',
          _stats!.ventas.totalFormatted,
          Icons.trending_up,
          const Color(0xFFFF9800),
          subtitle: _stats!.ventas.esteMesFormatted + ' este mes',
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color, {
    String? subtitle,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.2)),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                color: Colors.grey.shade500,
                fontSize: 10,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDetailedStats() {
    if (_stats == null) return const SizedBox();

    return Column(
      children: [
        _buildDetailCard(
          'Clientes Detallado',
          [
            _buildDetailRow('Total', _stats!.clientes.total.toString()),
            _buildDetailRow('Activos', _stats!.clientes.activos.toString()),
            _buildDetailRow('Inactivos', _stats!.clientes.inactivos.toString()),
            _buildDetailRow('Nuevos este mes', _stats!.clientes.nuevosEsteMes.toString()),
          ],
          Icons.people,
          const Color(0xFF4CAF50),
        ),
        const SizedBox(height: 12),
        _buildDetailCard(
          'Facturas Detallado',
          [
            _buildDetailRow('Total', _stats!.facturas.total.toString()),
            _buildDetailRow('Este mes', _stats!.facturas.esteMes.toString()),
            _buildDetailRow('Pendientes', _stats!.facturas.pendientes.toString()),
            _buildDetailRow('Pagadas', _stats!.facturas.pagadas.toString()),
          ],
          Icons.receipt,
          const Color(0xFF2196F3),
        ),
      ],
    );
  }

  Widget _buildDetailCard(
    String title,
    List<Widget> details,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Text(
                title,
                style: TextStyle(
                  color: color,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...details,
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              color: Colors.grey.shade600,
              fontSize: 14,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              color: Colors.black87,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          const Text(
            'Error cargando estadísticas',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _error ?? 'Error desconocido',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadStats,
            child: const Text('Reintentar'),
          ),
        ],
      ),
    );
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inMinutes < 1) {
      return 'Ahora mismo';
    } else if (difference.inMinutes < 60) {
      return 'Hace ${difference.inMinutes} min';
    } else if (difference.inHours < 24) {
      return 'Hace ${difference.inHours} h';
    } else {
      return '${time.day}/${time.month} ${time.hour}:${time.minute.toString().padLeft(2, '0')}';
    }
  }
}
