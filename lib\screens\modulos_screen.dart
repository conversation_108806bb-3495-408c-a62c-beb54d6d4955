import 'package:flutter/material.dart';
import '../services/modulo_service.dart';
import '../widgets/paginated_list_builder.dart';
import '../widgets/gradient_background.dart';
import '../widgets/responsive_container.dart';
import '../widgets/selector_imagen.dart';
import '../widgets/imagen_avatar.dart';
import '../models/modulo_model.dart';
import '../utils/database_exceptions.dart';

class ModulosScreen extends StatefulWidget {
  const ModulosScreen({super.key});

  @override
  State<ModulosScreen> createState() => _ModulosScreenState();
}

class _ModulosScreenState extends State<ModulosScreen> {
  final TextEditingController nombreModuloController = TextEditingController();
  final TextEditingController precioModuloController = TextEditingController();
  final PaginatedListController _listController = PaginatedListController();

  // Variable para imagen del módulo
  String? _imagenUrl;

  Future<List<Map<String, dynamic>>> _cargarModulosPaginados({
    int limit = 20,
    int offset = 0,
    String? searchQuery,
  }) {
    return ModuloService.obtenerModulosPaginados(
      limit: limit,
      offset: offset,
      searchQuery: searchQuery,
    );
  }

  Future<int> _contarModulos({String? searchQuery}) {
    return ModuloService.contarModulos(searchQuery: searchQuery);
  }

  Future<void> _agregarModulo() async {
    final String nombre = nombreModuloController.text.trim();
    final String precioStr = precioModuloController.text.trim();

    // Validar usando el modelo Modulo
    final errores = Modulo.validateAll(nombre: nombre, precioStr: precioStr);

    if (errores.isNotEmpty) {
      if (mounted) {
        final mensaje = errores.values.first;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(mensaje),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    try {
      final precio = double.parse(precioStr);

      // Crear módulo con imagen
      final nuevoModulo = Modulo(
        idModulo: DateTime.now().millisecondsSinceEpoch.toString(),
        nombre: nombre,
        precio: precio,
        imagenUrl: _imagenUrl,
      );

      await ModuloService.insertarModuloCompleto(nuevoModulo);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text("Módulo agregado correctamente"),
            backgroundColor: Colors.green,
          ),
        );

        nombreModuloController.clear();
        precioModuloController.clear();
        setState(() {
          _imagenUrl = null;
        });
        Navigator.pop(context);
        // Refrescar la lista usando el controlador
        await _listController.refresh();
      }
    } catch (e) {
      if (mounted) {
        String mensaje = "Error al agregar módulo";
        if (e is DuplicateRecordException) {
          mensaje = "Ya existe un módulo con ese nombre";
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(mensaje),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _eliminarModulo(String idModulo, String nombreModulo) async {
    // Mostrar diálogo de confirmación responsive
    final confirmar = await showDialog<bool>(
      context: context,
      builder: (context) => ResponsiveDialog(
        title: "Confirmar eliminación",
        actions: [
          ResponsiveButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text("Cancelar"),
          ),
          ResponsiveSpacing.horizontal(mobile: 8, tablet: 12, desktop: 16),
          ResponsiveButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child:
                const Text("Eliminar", style: TextStyle(color: Colors.white)),
          ),
        ],
        child: ResponsiveText(
          "¿Estás seguro de que deseas eliminar el módulo '$nombreModulo'?",
          mobileFontSize: 16,
          tabletFontSize: 17,
          desktopFontSize: 18,
        ),
      ),
    );

    if (confirmar != true) return;

    try {
      await ModuloService.eliminarModulo(idModulo);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text("Módulo eliminado correctamente"),
            backgroundColor: Colors.green,
          ),
        );
        // Refrescar la lista usando el controlador
        await _listController.refresh();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text("Error al eliminar módulo: ${e.toString()}"),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _editarModulo(String idModulo) async {
    final String nombre = nombreModuloController.text.trim();
    final String precioStr = precioModuloController.text.trim();

    // Validar usando el modelo Modulo
    final errores = Modulo.validateAll(nombre: nombre, precioStr: precioStr);

    if (errores.isNotEmpty) {
      if (mounted) {
        final mensaje = errores.values.first;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(mensaje),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    try {
      final precio = double.parse(precioStr);
      await ModuloService.actualizarModulo(idModulo, nombre, precio);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text("Módulo actualizado correctamente"),
            backgroundColor: Colors.green,
          ),
        );

        nombreModuloController.clear();
        precioModuloController.clear();
        Navigator.pop(context);
        // Refrescar la lista usando el controlador
        await _listController.refresh();
      }
    } catch (e) {
      if (mounted) {
        String mensaje = "Error al actualizar módulo";
        if (e is DuplicateRecordException) {
          mensaje = "Ya existe un módulo con ese nombre";
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(mensaje),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _mostrarDialogoAgregarModulo() {
    nombreModuloController.clear();
    precioModuloController.clear();
    setState(() {
      _imagenUrl = null;
    });

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text("Agregar Módulo"),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Selector de imagen
                SelectorImagen(
                  imagenUrl: _imagenUrl,
                  nombre: nombreModuloController.text.isNotEmpty
                      ? nombreModuloController.text
                      : 'Nuevo Módulo',
                  carpeta: 'modulos',
                  id: DateTime.now().millisecondsSinceEpoch.toString(),
                  onImagenCambiada: (nuevaUrl) {
                    setDialogState(() {
                      _imagenUrl = nuevaUrl;
                    });
                    setState(() {
                      _imagenUrl = nuevaUrl;
                    });
                  },
                  size: 80,
                  esCircular: false,
                  iconoPorDefecto: Icons.extension,
                ),
                const SizedBox(height: 16),

                TextField(
                  controller: nombreModuloController,
                  decoration: const InputDecoration(
                    labelText: "Nombre",
                    hintText: "Ej: Módulo de Ventas",
                  ),
                  textCapitalization: TextCapitalization.words,
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: precioModuloController,
                  decoration: const InputDecoration(
                    labelText: "Precio",
                    hintText: "Ej: 50000",
                    prefixText: "\$ ",
                  ),
                  keyboardType: TextInputType.number,
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text("Cancelar"),
            ),
            ElevatedButton(
              onPressed: _agregarModulo,
              child: const Text("Guardar"),
            ),
          ],
        ),
      ),
    );
  }

  void _mostrarDialogoEditarModulo(Map<String, dynamic> modulo) {
    nombreModuloController.text = modulo["Nombre"];
    precioModuloController.text = modulo["Precio"].toString();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text("Editar Módulo"),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nombreModuloController,
              decoration: const InputDecoration(
                labelText: "Nombre",
                hintText: "Ej: Módulo de Ventas",
              ),
              textCapitalization: TextCapitalization.words,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: precioModuloController,
              decoration: const InputDecoration(
                labelText: "Precio",
                hintText: "Ej: 50000",
                prefixText: "\$ ",
              ),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text("Cancelar"),
          ),
          ElevatedButton(
            onPressed: () => _editarModulo(modulo["IdModulo"]),
            child: const Text("Actualizar"),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GradientBackground(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: AppBar(
          title: const Text("Gestión de Módulos"),
          actions: [
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () async {
                await _listController.refresh();
              },
              tooltip: 'Actualizar lista',
            ),
          ],
        ),
        body: PaginatedListBuilder<Map<String, dynamic>>(
          controller: _listController,
          dataLoader: _cargarModulosPaginados,
          countLoader: _contarModulos,
          searchHint: "Buscar módulos por nombre...",
          emptyMessage: "No hay módulos disponibles",
          itemBuilder: (context, modulo, index) {
            return Card(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              color: Colors.white.withAlpha(242),
              elevation: 6,
              margin: const EdgeInsets.symmetric(vertical: 8),
              child: ListTile(
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                title: Text(
                  modulo["Nombre"],
                  style: Theme.of(context)
                      .textTheme
                      .titleMedium
                      ?.copyWith(fontWeight: FontWeight.bold),
                ),
                subtitle: Text(
                  "Precio: \$${modulo["Precio"]}",
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.green.shade700,
                      ),
                ),
                leading: AvatarModulo(
                  imagenUrl: modulo['imagenUrl'],
                  nombre: modulo['Nombre'],
                  size: 50,
                ),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: const Icon(Icons.edit, color: Colors.blue),
                      onPressed: () => _mostrarDialogoEditarModulo(modulo),
                      tooltip: 'Editar módulo',
                    ),
                    IconButton(
                      icon: const Icon(Icons.delete, color: Colors.red),
                      onPressed: () =>
                          _eliminarModulo(modulo["IdModulo"], modulo["Nombre"]),
                      tooltip: 'Eliminar módulo',
                    ),
                  ],
                ),
              ),
            );
          },
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: _mostrarDialogoAgregarModulo,
          backgroundColor: Colors.teal.shade700,
          child: const Icon(Icons.add, color: Colors.white),
        ),
      ),
    );
  }
}
