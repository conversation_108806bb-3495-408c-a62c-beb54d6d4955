import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math' as math;
import '../services/modulo_service.dart';
import '../widgets/paginated_list_builder.dart';
import '../widgets/gradient_background.dart';
import '../widgets/responsive_container.dart';
import '../widgets/selector_imagen.dart';
import '../widgets/imagen_avatar.dart';
import '../widgets/advanced_visual_effects.dart';
import '../models/modulo_model.dart';
import '../utils/database_exceptions.dart';

class ModulosScreen extends StatefulWidget {
  const ModulosScreen({super.key});

  @override
  State<ModulosScreen> createState() => _ModulosScreenState();
}

class _ModulosScreenState extends State<ModulosScreen>
    with TickerProviderStateMixin {
  final TextEditingController nombreModuloController = TextEditingController();
  final TextEditingController precioModuloController = TextEditingController();
  final TextEditingController _searchController = TextEditingController();
  final PaginatedListController _listController = PaginatedListController();

  // Variable para imagen del módulo
  String? _imagenUrl;

  // Controladores de animación
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _scaleController;
  late AnimationController _statsController;

  // Animaciones
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _statsAnimation;

  // Estado de la UI
  bool _showFilters = false;
  String _sortBy = 'nombre';
  String _filterByPrice = 'todos';

  // Estadísticas
  int _totalModulos = 0;
  double _precioPromedio = 0.0;
  double _precioMinimo = 0.0;
  double _precioMaximo = 0.0;

  // Validación en tiempo real
  String? _nombreError;
  String? _precioError;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadStatistics();
    _startEntryAnimations();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _scaleController.dispose();
    _statsController.dispose();
    _searchController.dispose();
    nombreModuloController.dispose();
    precioModuloController.dispose();
    super.dispose();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _statsController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    _statsAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _statsController,
      curve: Curves.easeInOut,
    ));
  }

  void _startEntryAnimations() {
    Future.delayed(const Duration(milliseconds: 100), () {
      _fadeController.forward();
    });

    Future.delayed(const Duration(milliseconds: 200), () {
      _scaleController.forward();
    });

    Future.delayed(const Duration(milliseconds: 300), () {
      _slideController.forward();
    });

    Future.delayed(const Duration(milliseconds: 500), () {
      _statsController.forward();
    });
  }

  Future<void> _loadStatistics() async {
    try {
      final modulos = await ModuloService.obtenerModulos();
      if (modulos.isNotEmpty) {
        setState(() {
          _totalModulos = modulos.length;
          final precios = modulos.map((m) => m['Precio'] as double).toList();
          _precioPromedio = precios.reduce((a, b) => a + b) / precios.length;
          _precioMinimo = precios.reduce(math.min);
          _precioMaximo = precios.reduce(math.max);
        });
      }
    } catch (e) {
      // Manejar error silenciosamente
    }
  }

  Future<List<Map<String, dynamic>>> _cargarModulosPaginados({
    int limit = 20,
    int offset = 0,
    String? searchQuery,
  }) {
    return ModuloService.obtenerModulosPaginados(
      limit: limit,
      offset: offset,
      searchQuery: searchQuery,
    );
  }

  Future<int> _contarModulos({String? searchQuery}) {
    return ModuloService.contarModulos(searchQuery: searchQuery);
  }

  void _validateNombre(String value) {
    setState(() {
      if (value.isEmpty) {
        _nombreError = 'El nombre es requerido';
      } else if (value.length < 3) {
        _nombreError = 'El nombre debe tener al menos 3 caracteres';
      } else if (value.length > 50) {
        _nombreError = 'El nombre no puede exceder 50 caracteres';
      } else {
        _nombreError = null;
      }
    });
  }

  void _validatePrecio(String value) {
    setState(() {
      if (value.isEmpty) {
        _precioError = 'El precio es requerido';
      } else {
        final precio = double.tryParse(value);
        if (precio == null) {
          _precioError = 'Ingrese un precio válido';
        } else if (precio <= 0) {
          _precioError = 'El precio debe ser mayor a 0';
        } else if (precio > 10000000) {
          _precioError = 'El precio no puede exceder \$10,000,000';
        } else {
          _precioError = null;
        }
      }
    });
  }

  void _refreshData() async {
    await _listController.refresh();
    await _loadStatistics();
    HapticFeedback.selectionClick();
  }

  void _toggleFilters() {
    setState(() {
      _showFilters = !_showFilters;
    });
    HapticFeedback.selectionClick();
  }

  Future<void> _agregarModulo() async {
    final String nombre = nombreModuloController.text.trim();
    final String precioStr = precioModuloController.text.trim();

    // Validar usando el modelo Modulo
    final errores = Modulo.validateAll(nombre: nombre, precioStr: precioStr);

    if (errores.isNotEmpty) {
      if (mounted) {
        final mensaje = errores.values.first;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(mensaje),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    try {
      final precio = double.parse(precioStr);

      // Crear módulo con imagen
      final nuevoModulo = Modulo(
        idModulo: DateTime.now().millisecondsSinceEpoch.toString(),
        nombre: nombre,
        precio: precio,
        imagenUrl: _imagenUrl,
      );

      await ModuloService.insertarModuloCompleto(nuevoModulo);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text("Módulo agregado correctamente"),
            backgroundColor: Colors.green,
          ),
        );

        nombreModuloController.clear();
        precioModuloController.clear();
        setState(() {
          _imagenUrl = null;
        });
        Navigator.pop(context);
        // Refrescar la lista usando el controlador
        await _listController.refresh();
      }
    } catch (e) {
      if (mounted) {
        String mensaje = "Error al agregar módulo";
        if (e is DuplicateRecordException) {
          mensaje = "Ya existe un módulo con ese nombre";
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(mensaje),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _eliminarModulo(String idModulo, String nombreModulo) async {
    // Mostrar diálogo de confirmación responsive
    final confirmar = await showDialog<bool>(
      context: context,
      builder: (context) => ResponsiveDialog(
        title: "Confirmar eliminación",
        actions: [
          ResponsiveButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text("Cancelar"),
          ),
          ResponsiveSpacing.horizontal(mobile: 8, tablet: 12, desktop: 16),
          ResponsiveButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child:
                const Text("Eliminar", style: TextStyle(color: Colors.white)),
          ),
        ],
        child: ResponsiveText(
          "¿Estás seguro de que deseas eliminar el módulo '$nombreModulo'?",
          mobileFontSize: 16,
          tabletFontSize: 17,
          desktopFontSize: 18,
        ),
      ),
    );

    if (confirmar != true) return;

    try {
      await ModuloService.eliminarModulo(idModulo);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text("Módulo eliminado correctamente"),
            backgroundColor: Colors.green,
          ),
        );
        // Refrescar la lista usando el controlador
        await _listController.refresh();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text("Error al eliminar módulo: ${e.toString()}"),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _editarModulo(String idModulo) async {
    final String nombre = nombreModuloController.text.trim();
    final String precioStr = precioModuloController.text.trim();

    // Validar usando el modelo Modulo
    final errores = Modulo.validateAll(nombre: nombre, precioStr: precioStr);

    if (errores.isNotEmpty) {
      if (mounted) {
        final mensaje = errores.values.first;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(mensaje),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    try {
      final precio = double.parse(precioStr);
      await ModuloService.actualizarModulo(idModulo, nombre, precio);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text("Módulo actualizado correctamente"),
            backgroundColor: Colors.green,
          ),
        );

        nombreModuloController.clear();
        precioModuloController.clear();
        Navigator.pop(context);
        // Refrescar la lista usando el controlador
        await _listController.refresh();
      }
    } catch (e) {
      if (mounted) {
        String mensaje = "Error al actualizar módulo";
        if (e is DuplicateRecordException) {
          mensaje = "Ya existe un módulo con ese nombre";
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(mensaje),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _mostrarDialogoAgregarModulo() {
    nombreModuloController.clear();
    precioModuloController.clear();
    setState(() {
      _imagenUrl = null;
    });

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text("Agregar Módulo"),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Selector de imagen
                SelectorImagen(
                  imagenUrl: _imagenUrl,
                  nombre: nombreModuloController.text.isNotEmpty
                      ? nombreModuloController.text
                      : 'Nuevo Módulo',
                  carpeta: 'modulos',
                  id: DateTime.now().millisecondsSinceEpoch.toString(),
                  onImagenCambiada: (nuevaUrl) {
                    setDialogState(() {
                      _imagenUrl = nuevaUrl;
                    });
                    setState(() {
                      _imagenUrl = nuevaUrl;
                    });
                  },
                  size: 80,
                  esCircular: false,
                  iconoPorDefecto: Icons.extension,
                ),
                const SizedBox(height: 16),

                TextFormField(
                  controller: nombreModuloController,
                  decoration: InputDecoration(
                    labelText: "Nombre del Módulo",
                    hintText: "Ej: Módulo de Ventas",
                    errorText: _nombreError,
                    prefixIcon: const Icon(Icons.extension),
                    filled: true,
                    fillColor: Theme.of(context).scaffoldBackgroundColor.withValues(alpha: 0.5),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide.none,
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: Theme.of(context).primaryColor,
                        width: 2,
                      ),
                    ),
                    errorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(
                        color: Colors.red,
                        width: 2,
                      ),
                    ),
                  ),
                  textCapitalization: TextCapitalization.words,
                  onChanged: (value) {
                    setState(() {
                      _validateNombre(value);
                    });
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: precioModuloController,
                  decoration: InputDecoration(
                    labelText: "Precio",
                    hintText: "Ej: 50000",
                    errorText: _precioError,
                    prefixIcon: const Icon(Icons.attach_money),
                    filled: true,
                    fillColor: Theme.of(context).scaffoldBackgroundColor.withValues(alpha: 0.5),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide.none,
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: Theme.of(context).primaryColor,
                        width: 2,
                      ),
                    ),
                    errorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(
                        color: Colors.red,
                        width: 2,
                      ),
                    ),
                  ),
                  keyboardType: TextInputType.number,
                  onChanged: (value) {
                    setState(() {
                      _validatePrecio(value);
                    });
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text("Cancelar"),
            ),
            ElevatedButton(
              onPressed: (_nombreError == null && _precioError == null &&
                         nombreModuloController.text.isNotEmpty &&
                         precioModuloController.text.isNotEmpty)
                  ? _agregarModulo
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).primaryColor,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text("Guardar Módulo"),
            ),
          ],
        ),
      ),
    );
  }

  void _mostrarDialogoEditarModulo(Map<String, dynamic> modulo) {
    nombreModuloController.text = modulo["Nombre"];
    precioModuloController.text = modulo["Precio"].toString();
    _nombreError = null;
    _precioError = null;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.edit,
                  color: Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              const Text("Editar Módulo"),
            ],
          ),
          content: SizedBox(
            width: 400,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Información del módulo actual
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      AvatarModulo(
                        imagenUrl: modulo['imagenUrl'],
                        nombre: modulo['Nombre'],
                        size: 40,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Editando: ${modulo["Nombre"]}',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.blue,
                              ),
                            ),
                            Text(
                              'Precio actual: \$${modulo["Precio"]}',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.blue.shade600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 20),

                TextFormField(
                  controller: nombreModuloController,
                  decoration: InputDecoration(
                    labelText: "Nombre del Módulo",
                    hintText: "Ej: Módulo de Ventas",
                    errorText: _nombreError,
                    prefixIcon: const Icon(Icons.extension),
                    filled: true,
                    fillColor: Theme.of(context).scaffoldBackgroundColor.withValues(alpha: 0.5),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide.none,
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: Theme.of(context).primaryColor,
                        width: 2,
                      ),
                    ),
                    errorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(
                        color: Colors.red,
                        width: 2,
                      ),
                    ),
                  ),
                  textCapitalization: TextCapitalization.words,
                  onChanged: (value) {
                    setState(() {
                      _validateNombre(value);
                    });
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: precioModuloController,
                  decoration: InputDecoration(
                    labelText: "Precio",
                    hintText: "Ej: 50000",
                    errorText: _precioError,
                    prefixIcon: const Icon(Icons.attach_money),
                    filled: true,
                    fillColor: Theme.of(context).scaffoldBackgroundColor.withValues(alpha: 0.5),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide.none,
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: Theme.of(context).primaryColor,
                        width: 2,
                      ),
                    ),
                    errorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(
                        color: Colors.red,
                        width: 2,
                      ),
                    ),
                  ),
                  keyboardType: TextInputType.number,
                  onChanged: (value) {
                    setState(() {
                      _validatePrecio(value);
                    });
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text("Cancelar"),
            ),
            ElevatedButton(
              onPressed: (_nombreError == null && _precioError == null &&
                         nombreModuloController.text.isNotEmpty &&
                         precioModuloController.text.isNotEmpty)
                  ? () => _editarModulo(modulo["IdModulo"])
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text("Actualizar Módulo"),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GradientBackground(
      child: Stack(
        children: [
          // Partículas de fondo
          FloatingParticles(
            particleCount: 20,
            particleColor: Theme.of(context).primaryColor.withValues(alpha: 0.1),
            child: const SizedBox.expand(),
          ),

          Scaffold(
            backgroundColor: Colors.transparent,
            body: SafeArea(
              child: Column(
                children: [
                  _buildHeader(),
                  _buildStatsSection(),
                  if (_showFilters) _buildFiltersSection(),
                  Expanded(
                    child: _buildModulosList(),
                  ),
                ],
              ),
            ),
            floatingActionButton: _buildAnimatedFAB(),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            // Botón de regreso
            ScaleTransition(
              scale: _scaleAnimation,
              child: Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).cardColor,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.arrow_back),
                  tooltip: 'Volver',
                ),
              ),
            ),

            const SizedBox(width: 16),

            // Título y subtítulo
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Gestión de Módulos',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Administra los módulos del sistema',
                    style: TextStyle(
                      color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ),
            ),

            // Botones de acción
            Row(
              children: [
                ScaleTransition(
                  scale: _scaleAnimation,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Theme.of(context).cardColor,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: IconButton(
                      onPressed: _toggleFilters,
                      icon: Icon(
                        _showFilters ? Icons.filter_list_off : Icons.filter_list,
                        color: _showFilters ? Theme.of(context).primaryColor : null,
                      ),
                      tooltip: 'Filtros',
                    ),
                  ),
                ),

                const SizedBox(width: 8),

                ScaleTransition(
                  scale: _scaleAnimation,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Theme.of(context).cardColor,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: IconButton(
                      onPressed: _refreshData,
                      icon: const Icon(Icons.refresh),
                      tooltip: 'Actualizar datos',
                    ),
                  ),
                ),

              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsSection() {
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 20),
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Theme.of(context).primaryColor,
                Theme.of(context).primaryColor.withValues(alpha: 0.8),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
                blurRadius: 15,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(Icons.analytics, color: Colors.white, size: 24),
                  const SizedBox(width: 12),
                  const Text(
                    'Estadísticas de Módulos',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),

              // Grid de estadísticas
              Row(
                children: [
                  Expanded(
                    child: _buildStatCard(
                      'Total',
                      _totalModulos.toString(),
                      Icons.apps,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildStatCard(
                      'Promedio',
                      '\$${_precioPromedio.toStringAsFixed(0)}',
                      Icons.trending_up,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              Row(
                children: [
                  Expanded(
                    child: _buildStatCard(
                      'Mínimo',
                      '\$${_precioMinimo.toStringAsFixed(0)}',
                      Icons.arrow_downward,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildStatCard(
                      'Máximo',
                      '\$${_precioMaximo.toStringAsFixed(0)}',
                      Icons.arrow_upward,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatCard(String label, String value, IconData icon) {
    return AnimatedBuilder(
      animation: _statsAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: 0.8 + (0.2 * _statsAnimation.value),
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                Icon(icon, color: Colors.white70, size: 20),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  label,
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildFiltersSection() {
    return SlideTransition(
      position: _slideAnimation,
      child: Container(
        margin: const EdgeInsets.all(20),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.tune,
                  color: Theme.of(context).primaryColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Filtros y Ordenamiento',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _sortBy,
                    decoration: InputDecoration(
                      labelText: 'Ordenar por',
                      prefixIcon: const Icon(Icons.sort),
                      filled: true,
                      fillColor: Theme.of(context).scaffoldBackgroundColor.withValues(alpha: 0.5),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide.none,
                      ),
                    ),
                    items: const [
                      DropdownMenuItem(value: 'nombre', child: Text('Nombre')),
                      DropdownMenuItem(value: 'precio_asc', child: Text('Precio (menor a mayor)')),
                      DropdownMenuItem(value: 'precio_desc', child: Text('Precio (mayor a menor)')),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _sortBy = value!;
                      });
                      HapticFeedback.selectionClick();
                    },
                  ),
                ),

                const SizedBox(width: 16),

                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _filterByPrice,
                    decoration: InputDecoration(
                      labelText: 'Filtrar por precio',
                      prefixIcon: const Icon(Icons.attach_money),
                      filled: true,
                      fillColor: Theme.of(context).scaffoldBackgroundColor.withValues(alpha: 0.5),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide.none,
                      ),
                    ),
                    items: const [
                      DropdownMenuItem(value: 'todos', child: Text('Todos')),
                      DropdownMenuItem(value: 'bajo', child: Text('Bajo (\$0 - \$50k)')),
                      DropdownMenuItem(value: 'medio', child: Text('Medio (\$50k - \$100k)')),
                      DropdownMenuItem(value: 'alto', child: Text('Alto (\$100k+)')),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _filterByPrice = value!;
                      });
                      HapticFeedback.selectionClick();
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModulosList() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: PaginatedListBuilder<Map<String, dynamic>>(
        controller: _listController,
        dataLoader: _cargarModulosPaginados,
        countLoader: _contarModulos,
        searchHint: "Buscar módulos por nombre...",
        emptyMessage: "No hay módulos disponibles",
        itemBuilder: (context, modulo, index) {
          return _buildModuloListCard(modulo, index);
        },
      ),
    );
  }



  Widget _buildModuloListCard(Map<String, dynamic> modulo, int index) {
    return SlideTransition(
      position: _slideAnimation,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 8),
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: ListTile(
          contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
          leading: AvatarModulo(
            imagenUrl: modulo['imagenUrl'],
            nombre: modulo['Nombre'],
            size: 50,
          ),
          title: Text(
            modulo["Nombre"],
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          subtitle: Container(
            margin: const EdgeInsets.only(top: 8),
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.green.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              "Precio: \$${modulo["Precio"]}",
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.green.shade700,
              ),
            ),
          ),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: IconButton(
                  icon: const Icon(Icons.edit, color: Colors.blue),
                  onPressed: () => _mostrarDialogoEditarModulo(modulo),
                  tooltip: 'Editar módulo',
                ),
              ),
              const SizedBox(width: 8),
              Container(
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: IconButton(
                  icon: const Icon(Icons.delete, color: Colors.red),
                  onPressed: () => _eliminarModulo(modulo["IdModulo"], modulo["Nombre"]),
                  tooltip: 'Eliminar módulo',
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAnimatedFAB() {
    return ScaleTransition(
      scale: _scaleAnimation,
      child: FloatingActionButton.extended(
        onPressed: _mostrarDialogoAgregarModulo,
        icon: const Icon(Icons.add),
        label: const Text('Nuevo Módulo'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 8,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
    );
  }
}

