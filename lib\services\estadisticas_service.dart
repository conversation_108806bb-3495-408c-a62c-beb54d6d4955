import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/cliente_model.dart';
import '../models/factura_model.dart';
import '../models/modulo_model.dart';
import '../services/cache_service.dart';
import 'dart:developer';

/// Servicio para obtener estadísticas en tiempo real
class EstadisticasService with CacheableMixin {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Obtener estadísticas del dashboard
  static Future<DashboardStats> obtenerEstadisticasDashboard() async {
    try {
      log('📊 Obteniendo estadísticas del dashboard...');

      // Usar caché para mejorar rendimiento
      return await _getInstance().getWithCache(
        CacheKeys.estadisticas,
        () => _fetchEstadisticasDashboard(),
        expiration: const Duration(minutes: 5),
      );
    } catch (e) {
      log('❌ Error obteniendo estadísticas: $e');
      return DashboardStats.empty();
    }
  }

  static EstadisticasService _getInstance() => EstadisticasService();

  static Future<DashboardStats> _fetchEstadisticasDashboard() async {
    try {
      // Obtener datos en paralelo para mejor rendimiento
      final futures = await Future.wait([
        _obtenerEstadisticasClientes(),
        _obtenerEstadisticasFacturas(),
        _obtenerEstadisticasModulos(),
        _obtenerEstadisticasVentas(),
      ]);

      final clientesStats = futures[0] as ClientesStats;
      final facturasStats = futures[1] as FacturasStats;
      final modulosStats = futures[2] as ModulosStats;
      final ventasStats = futures[3] as VentasStats;

      return DashboardStats(
        clientes: clientesStats,
        facturas: facturasStats,
        modulos: modulosStats,
        ventas: ventasStats,
        ultimaActualizacion: DateTime.now(),
      );
    } catch (e) {
      log('❌ Error en _fetchEstadisticasDashboard: $e');
      rethrow;
    }
  }

  /// Obtener estadísticas de clientes
  static Future<ClientesStats> _obtenerEstadisticasClientes() async {
    try {
      final snapshot = await _firestore.collection('clientes').get();
      final clientes = snapshot.docs.map((doc) => Cliente.fromFirestore(doc)).toList();

      final total = clientes.length;
      final activos = clientes.where((c) => c.tieneSuscripcion && c.suscripcionActiva).length;
      final nuevosEsteMes = clientes.where((c) =>
        c.fechaInicioSuscripcion?.isAfter(DateTime.now().subtract(const Duration(days: 30))) ?? false
      ).length;

      return ClientesStats(
        total: total,
        activos: activos,
        nuevosEsteMes: nuevosEsteMes,
        inactivos: total - activos,
      );
    } catch (e) {
      log('❌ Error obteniendo estadísticas de clientes: $e');
      return ClientesStats.empty();
    }
  }

  /// Obtener estadísticas de facturas
  static Future<FacturasStats> _obtenerEstadisticasFacturas() async {
    try {
      final snapshot = await _firestore.collection('facturas').get();
      final facturas = snapshot.docs.map((doc) => Factura.fromFirestore(doc)).toList();

      final total = facturas.length;
      final esteMes = facturas.where((f) => 
        f.fecha.isAfter(DateTime.now().subtract(const Duration(days: 30)))
      ).length;
      final pendientes = facturas.where((f) => f.estado == 'pendiente').length;
      final pagadas = facturas.where((f) => f.estado == 'pagada').length;

      return FacturasStats(
        total: total,
        esteMes: esteMes,
        pendientes: pendientes,
        pagadas: pagadas,
      );
    } catch (e) {
      log('❌ Error obteniendo estadísticas de facturas: $e');
      return FacturasStats.empty();
    }
  }

  /// Obtener estadísticas de módulos
  static Future<ModulosStats> _obtenerEstadisticasModulos() async {
    try {
      final snapshot = await _firestore.collection('modulos').get();
      final modulos = snapshot.docs.map((doc) => Modulo.fromFirestore(doc)).toList();

      final total = modulos.length;
      final activos = modulos.where((m) => m.activo).length;
      final masVendido = _obtenerModuloMasVendido(modulos);

      return ModulosStats(
        total: total,
        activos: activos,
        inactivos: total - activos,
        masVendido: masVendido,
      );
    } catch (e) {
      log('❌ Error obteniendo estadísticas de módulos: $e');
      return ModulosStats.empty();
    }
  }

  /// Obtener estadísticas de ventas
  static Future<VentasStats> _obtenerEstadisticasVentas() async {
    try {
      final snapshot = await _firestore.collection('facturas').get();
      final facturas = snapshot.docs.map((doc) => Factura.fromFirestore(doc)).toList();

      final totalVentas = facturas
          .where((f) => f.estado == 'pagada')
          .fold(0.0, (acc, f) => acc + f.total);

      final ventasEsteMes = facturas
          .where((f) =>
            f.estado == 'pagada' &&
            f.fecha.isAfter(DateTime.now().subtract(const Duration(days: 30)))
          )
          .fold(0.0, (acc, f) => acc + f.total);

      final ventasEsteAno = facturas
          .where((f) =>
            f.estado == 'pagada' &&
            f.fecha.year == DateTime.now().year
          )
          .fold(0.0, (acc, f) => acc + f.total);

      final promedioMensual = ventasEsteAno / DateTime.now().month;

      return VentasStats(
        total: totalVentas,
        esteMes: ventasEsteMes,
        esteAno: ventasEsteAno,
        promedioMensual: promedioMensual,
      );
    } catch (e) {
      log('❌ Error obteniendo estadísticas de ventas: $e');
      return VentasStats.empty();
    }
  }

  /// Obtener módulo más vendido
  static String _obtenerModuloMasVendido(List<Modulo> modulos) {
    if (modulos.isEmpty) return 'N/A';
    
    // Por ahora retornamos el primer módulo activo
    // En el futuro se puede implementar lógica más compleja
    final moduloActivo = modulos.firstWhere(
      (m) => m.activo,
      orElse: () => modulos.first,
    );
    
    return moduloActivo.nombre;
  }

  /// Obtener tendencia de ventas por mes
  static Future<List<VentasMensual>> obtenerTendenciaVentas() async {
    try {
      final snapshot = await _firestore.collection('facturas').get();
      final facturas = snapshot.docs.map((doc) => Factura.fromFirestore(doc)).toList();

      final Map<int, double> ventasPorMes = {};
      final ahora = DateTime.now();

      // Inicializar últimos 6 meses
      for (int i = 5; i >= 0; i--) {
        final mes = DateTime(ahora.year, ahora.month - i, 1);
        ventasPorMes[mes.month] = 0.0;
      }

      // Calcular ventas por mes
      for (final factura in facturas) {
        if (factura.estado == 'pagada' && 
            factura.fecha.isAfter(DateTime(ahora.year, ahora.month - 5, 1))) {
          ventasPorMes[factura.fecha.month] = 
              (ventasPorMes[factura.fecha.month] ?? 0) + factura.total;
        }
      }

      return ventasPorMes.entries.map((entry) => 
        VentasMensual(mes: entry.key, ventas: entry.value)
      ).toList();
    } catch (e) {
      log('❌ Error obteniendo tendencia de ventas: $e');
      return [];
    }
  }

  /// Invalidar caché de estadísticas
  static Future<void> invalidarCache() async {
    await CacheService.remove(CacheKeys.estadisticas);
    log('🗑️ Caché de estadísticas invalidado');
  }
}

/// Modelo de estadísticas del dashboard
class DashboardStats {
  final ClientesStats clientes;
  final FacturasStats facturas;
  final ModulosStats modulos;
  final VentasStats ventas;
  final DateTime ultimaActualizacion;

  DashboardStats({
    required this.clientes,
    required this.facturas,
    required this.modulos,
    required this.ventas,
    required this.ultimaActualizacion,
  });

  factory DashboardStats.empty() {
    return DashboardStats(
      clientes: ClientesStats.empty(),
      facturas: FacturasStats.empty(),
      modulos: ModulosStats.empty(),
      ventas: VentasStats.empty(),
      ultimaActualizacion: DateTime.now(),
    );
  }
}

/// Estadísticas de clientes
class ClientesStats {
  final int total;
  final int activos;
  final int inactivos;
  final int nuevosEsteMes;

  ClientesStats({
    required this.total,
    required this.activos,
    required this.inactivos,
    required this.nuevosEsteMes,
  });

  factory ClientesStats.empty() {
    return ClientesStats(total: 0, activos: 0, inactivos: 0, nuevosEsteMes: 0);
  }

  String get totalFormatted => total.toString();
  String get activosFormatted => activos.toString();
}

/// Estadísticas de facturas
class FacturasStats {
  final int total;
  final int esteMes;
  final int pendientes;
  final int pagadas;

  FacturasStats({
    required this.total,
    required this.esteMes,
    required this.pendientes,
    required this.pagadas,
  });

  factory FacturasStats.empty() {
    return FacturasStats(total: 0, esteMes: 0, pendientes: 0, pagadas: 0);
  }

  String get totalFormatted {
    if (total >= 1000) return '${(total / 1000).toStringAsFixed(1)}K';
    return total.toString();
  }
}

/// Estadísticas de módulos
class ModulosStats {
  final int total;
  final int activos;
  final int inactivos;
  final String masVendido;

  ModulosStats({
    required this.total,
    required this.activos,
    required this.inactivos,
    required this.masVendido,
  });

  factory ModulosStats.empty() {
    return ModulosStats(total: 0, activos: 0, inactivos: 0, masVendido: 'N/A');
  }
}

/// Estadísticas de ventas
class VentasStats {
  final double total;
  final double esteMes;
  final double esteAno;
  final double promedioMensual;

  VentasStats({
    required this.total,
    required this.esteMes,
    required this.esteAno,
    required this.promedioMensual,
  });

  factory VentasStats.empty() {
    return VentasStats(total: 0, esteMes: 0, esteAno: 0, promedioMensual: 0);
  }

  String get totalFormatted {
    if (total >= 1000000) return '\$${(total / 1000000).toStringAsFixed(1)}M';
    if (total >= 1000) return '\$${(total / 1000).toStringAsFixed(1)}K';
    return '\$${total.toStringAsFixed(0)}';
  }

  String get esteMesFormatted {
    if (esteMes >= 1000000) return '\$${(esteMes / 1000000).toStringAsFixed(1)}M';
    if (esteMes >= 1000) return '\$${(esteMes / 1000).toStringAsFixed(1)}K';
    return '\$${esteMes.toStringAsFixed(0)}';
  }
}

/// Ventas mensuales para gráficos
class VentasMensual {
  final int mes;
  final double ventas;

  VentasMensual({required this.mes, required this.ventas});
}
