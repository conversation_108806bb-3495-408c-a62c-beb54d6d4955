import 'package:cloud_firestore/cloud_firestore.dart';
import '../services/firebase_service.dart';
import '../models/cliente_model.dart';
import 'dart:developer';

class ClienteService {
  static const String _collection = 'clientes';

  /// Obtener todos los clientes
  static Future<List<Cliente>> obtenerClientes() async {
    try {
      final snapshot =
          await FirebaseService.getCollection(collection: _collection);

      // Debug: mostrar datos raw de Firebase (comentado para producción)
      // print('🔍 Datos raw de Firebase:');
      // for (var doc in snapshot.docs) {
      //   final data = doc.data() as Map<String, dynamic>;
      //   print('📄 Documento: ${data['nombre'] ?? data['Nombre']}');
      //   print('   - fechaVencimientoSuscripcion (raw): ${data['fechaVencimientoSuscripcion']} (${data['fechaVencimientoSuscripcion'].runtimeType})');
      //   print('   - estadoSuscripcion (raw): ${data['estadoSuscripcion']}');
      //   print('   - montoSuscripcion (raw): ${data['montoSuscripcion']}');
      // }

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;

        // Normalizar los nombres de campos (Firebase puede tener diferentes formatos)
        final normalizedData = {
          'IdCliente': data['idCliente'] ?? data['IdCliente'] ?? doc.id,
          'Nombre': data['nombre'] ?? data['Nombre'] ?? '',
          'Telefono': data['telefono'] ?? data['Telefono'] ?? '',
          'Email': data['email'] ?? data['Email'] ?? '',
          'imagenUrl': data['imagenUrl'],
          'fechaInicioSuscripcion': data['fechaInicioSuscripcion'],
          'fechaVencimientoSuscripcion': data['fechaVencimientoSuscripcion'],
          'tipoSuscripcion': data['tipoSuscripcion'],
          'montoSuscripcion': data['montoSuscripcion'],
          'estadoSuscripcion': data['estadoSuscripcion'],
        };

        return Cliente.fromMap(normalizedData);
      }).toList();
    } catch (e) {
      log('Error obteniendo clientes: $e');
      return [];
    }
  }

  /// Obtener clientes con paginación
  static Future<List<Map<String, dynamic>>> obtenerClientesPaginados({
    int limit = 20,
    int offset = 0,
    String? searchQuery,
  }) async {
    try {
      Query query = FirebaseFirestore.instance.collection(_collection);

      // Aplicar filtro de búsqueda si existe
      if (searchQuery != null && searchQuery.isNotEmpty) {
        query = query
            .where('nombre', isGreaterThanOrEqualTo: searchQuery)
            .where('nombre', isLessThanOrEqualTo: '$searchQuery\uf8ff');
      }

      // Aplicar paginación
      query = query.limit(limit);
      if (offset > 0) {
        // Para offset, necesitaríamos implementar cursor-based pagination
        // Por simplicidad, usamos orderBy y limit
        query = query.orderBy('nombre');
      }

      final snapshot = await query.get();
      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return {
          'IdCliente': data['idCliente'] ?? doc.id,
          'Nombre': data['nombre'] ?? '',
          'Telefono': data['telefono'] ?? '',
          'Email': data['email'] ?? '',
        };
      }).toList();
    } catch (e) {
      log('Error obteniendo clientes paginados: $e');
      return [];
    }
  }

  /// Contar clientes
  static Future<int> contarClientes({String? searchQuery}) async {
    try {
      Query query = FirebaseFirestore.instance.collection(_collection);

      if (searchQuery != null && searchQuery.isNotEmpty) {
        query = query
            .where('nombre', isGreaterThanOrEqualTo: searchQuery)
            .where('nombre', isLessThanOrEqualTo: '$searchQuery\uf8ff');
      }

      final snapshot = await query.get();
      return snapshot.docs.length;
    } catch (e) {
      log('Error contando clientes: $e');
      return 0;
    }
  }

  /// Insertar cliente
  static Future<void> insertarCliente(Cliente cliente) async {
    try {
      await FirebaseService.createDocument(
        collection: _collection,
        docId: cliente.idCliente,
        data: {
          'idCliente': cliente.idCliente,
          'nombre': cliente.nombre,
          'telefono': cliente.telefono,
          'email': cliente.email,
          'activo': true,
        },
      );
      log('Cliente insertado exitosamente: ${cliente.nombre}');
    } catch (e) {
      log('Error insertando cliente: $e');
      rethrow;
    }
  }

  /// Actualizar cliente
  static Future<void> actualizarCliente(Cliente cliente) async {
    try {
      await FirebaseService.updateDocument(
        collection: _collection,
        docId: cliente.idCliente,
        data: {
          'nombre': cliente.nombre,
          'telefono': cliente.telefono,
          'email': cliente.email,
          'imagenUrl': cliente.imagenUrl,
          'fechaInicioSuscripcion':
              cliente.fechaInicioSuscripcion?.millisecondsSinceEpoch,
          'fechaVencimientoSuscripcion':
              cliente.fechaVencimientoSuscripcion?.millisecondsSinceEpoch,
          'tipoSuscripcion': cliente.tipoSuscripcion,
          'montoSuscripcion': cliente.montoSuscripcion,
          'estadoSuscripcion': cliente.estadoSuscripcion,
        },
      );
      log('Cliente actualizado exitosamente: ${cliente.nombre}');
      if (cliente.fechaVencimientoSuscripcion != null) {
        log('Suscripción actualizada - Vence: ${cliente.fechaVencimientoSuscripcion}');
      }
    } catch (e) {
      log('Error actualizando cliente: $e');
      rethrow;
    }
  }

  /// Eliminar cliente
  static Future<void> eliminarCliente(String idCliente) async {
    try {
      await FirebaseService.deleteDocument(
        collection: _collection,
        docId: idCliente,
      );
      log('Cliente eliminado exitosamente: $idCliente');
    } catch (e) {
      log('Error eliminando cliente: $e');
      rethrow;
    }
  }

  /// Obtener cliente por ID
  static Future<Cliente?> obtenerClientePorId(String idCliente) async {
    try {
      final doc = await FirebaseService.getDocument(
        collection: _collection,
        docId: idCliente,
      );

      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;
        return Cliente(
          idCliente: data['idCliente'] ?? doc.id,
          nombre: data['nombre'] ?? '',
          telefono: data['telefono'] ?? '',
          email: data['email'] ?? '',
        );
      }
      return null;
    } catch (e) {
      log('Error obteniendo cliente por ID: $e');
      return null;
    }
  }

  /// Buscar clientes por nombre
  static Future<List<Cliente>> buscarClientesPorNombre(String nombre) async {
    try {
      final snapshot = await FirebaseService.getCollection(
        collection: _collection,
        queryBuilder: (query) => query
            .where('nombre', isGreaterThanOrEqualTo: nombre)
            .where('nombre', isLessThanOrEqualTo: '$nombre\uf8ff')
            .limit(10),
      );

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return Cliente(
          idCliente: data['idCliente'] ?? doc.id,
          nombre: data['nombre'] ?? '',
          telefono: data['telefono'] ?? '',
          email: data['email'] ?? '',
        );
      }).toList();
    } catch (e) {
      log('Error buscando clientes por nombre: $e');
      return [];
    }
  }

  /// Verificar si existe un cliente con el mismo email
  static Future<bool> existeClienteConEmail(String email,
      {String? excludeId}) async {
    try {
      Query query = FirebaseFirestore.instance
          .collection(_collection)
          .where('email', isEqualTo: email);

      final snapshot = await query.get();

      if (excludeId != null) {
        return snapshot.docs.any((doc) => doc.id != excludeId);
      }

      return snapshot.docs.isNotEmpty;
    } catch (e) {
      log('Error verificando email duplicado: $e');
      return false;
    }
  }
}
