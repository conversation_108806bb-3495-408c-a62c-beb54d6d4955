import 'dart:developer';
import 'package:flutter/material.dart';
import '../services/factura_service.dart';
import '../models/factura_model.dart';
import '../widgets/paginated_list_builder.dart';
import '../widgets/gradient_background.dart';
import '../routes/app_routes.dart';
import 'package:file_picker/file_picker.dart';

class FacturasScreen extends StatefulWidget {
  const FacturasScreen({super.key});

  @override
  State<FacturasScreen> createState() => _FacturasScreenState();
}

class _FacturasScreenState extends State<FacturasScreen> {
  final PaginatedListController _listController = PaginatedListController();

  Future<List<Map<String, dynamic>>> _cargarFacturasPaginadas({
    int limit = 20,
    int offset = 0,
    String? searchQuery,
  }) {
    return FacturaService.obtenerFacturasPaginadas(
      limit: limit,
      offset: offset,
      searchQuery: searchQuery,
    );
  }

  Future<int> _contarFacturas({String? searchQuery}) {
    return FacturaService.contarFacturas();
  }

  Future<void> _adjuntarComprobante(String idFactura) async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf'],
      );

      if (result == null ||
          result.files.isEmpty ||
          result.files.single.path == null) {
        log("No se seleccionó ningún archivo 🚨");
        return;
      }

      // NOTA: Funcionalidad de subida de archivos pendiente de implementación
      // Para implementar completamente:
      // 1. Subir archivo a Firebase Storage
      // 2. Obtener URL de descarga
      // 3. Guardar URL en la factura correspondiente
      String fileName = result.files.single.name;

      // Por ahora, solo mostramos el nombre del archivo seleccionado

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("Archivo seleccionado: $fileName")),
        );
        setState(() {}); // Refrescar la lista
      }
    } catch (e) {
      log("Error al adjuntar comprobante: $e 🚨");
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text("Error al adjuntar comprobante")),
        );
      }
    }
  }

  Future<void> _eliminarFactura(String idFactura) async {
    await FacturaService.eliminarFactura(idFactura);
    if (mounted) setState(() {}); // Refrescar la lista
  }

  @override
  Widget build(BuildContext context) {
    return GradientBackground(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: AppBar(
          title: const Text("Facturas"),
          actions: [
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () async {
                await _listController.refresh();
              },
              tooltip: 'Actualizar lista',
            ),
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: () async {
                final result = await Navigator.pushNamed(
                    context, AppRoutes.agregarFactura);
                if (result == true) {
                  // Se creó una nueva factura, actualizar la lista
                  await _listController.refresh();
                }
              },
              tooltip: 'Agregar factura',
            ),
          ],
        ),
        body: PaginatedListBuilder<Map<String, dynamic>>(
          controller: _listController,
          dataLoader: _cargarFacturasPaginadas,
          countLoader: _contarFacturas,
          searchHint: "Buscar facturas por ID, cliente o método de pago...",
          emptyMessage: "No hay facturas registradas",
          itemBuilder: (context, facturaMap, index) {
            // Crear objeto Factura desde el Map
            final factura = Factura.fromMap(facturaMap);
            final clienteNombre =
                facturaMap['ClienteNombre'] ?? 'Cliente desconocido';

            return Card(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              color: Colors.white.withAlpha(242),
              elevation: 6,
              margin: const EdgeInsets.symmetric(vertical: 8),
              child: ListTile(
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                title: Text(
                  "Factura #${factura.idFactura}",
                  style: Theme.of(context)
                      .textTheme
                      .titleMedium
                      ?.copyWith(fontWeight: FontWeight.bold),
                ),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 4),
                    Text(
                      "Cliente: $clienteNombre",
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                    Text(
                      "Método: ${factura.metodoPago}",
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                    Text(
                      "Total: \$${factura.total.toStringAsFixed(2)}",
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Colors.green.shade700,
                          ),
                    ),
                  ],
                ),
                leading: CircleAvatar(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  child: const Icon(Icons.receipt_long, color: Colors.white),
                ),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon:
                          const Icon(Icons.picture_as_pdf, color: Colors.blue),
                      onPressed: () => _adjuntarComprobante(factura.idFactura),
                      tooltip: 'Adjuntar comprobante',
                    ),
                    IconButton(
                      icon: const Icon(Icons.visibility, color: Colors.green),
                      onPressed: () => context.pushNamed(
                        AppRoutes.detalleFactura,
                        arguments: factura.idFactura,
                      ),
                      tooltip: 'Ver detalles',
                    ),
                    IconButton(
                      icon: const Icon(Icons.delete, color: Colors.red),
                      onPressed: () => _eliminarFactura(factura.idFactura),
                      tooltip: 'Eliminar factura',
                    ),
                  ],
                ),
              ),
            );
          },
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: () async {
            log("Navegando a pantalla de agregar factura");
            await context.pushNamed(AppRoutes.agregarFactura);
            setState(() {}); // Refrescar la lista
          },
          backgroundColor: Colors.teal.shade700,
          child: const Icon(Icons.add),
        ),
      ),
    );
  }
}
