import 'dart:developer';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/factura_service.dart';
import '../models/factura_model.dart';
import '../widgets/paginated_list_builder.dart';
import '../widgets/gradient_background.dart';
import '../widgets/advanced_visual_effects.dart';
import '../widgets/animated_counter.dart';
import '../widgets/advanced_charts.dart';
import '../widgets/theme_toggle_button.dart';
import '../widgets/simple_chart.dart';
import '../routes/app_routes.dart';
import 'package:file_picker/file_picker.dart';

class FacturasScreen extends StatefulWidget {
  const FacturasScreen({super.key});

  @override
  State<FacturasScreen> createState() => _FacturasScreenState();
}

enum FacturasViewMode { dashboard, list, grid, analytics }
enum FacturasFilter { todas, pendientes, pagadas, vencidas }
enum FacturasSortBy { fecha, total, cliente, estado }

class _FacturasScreenState extends State<FacturasScreen>
    with TickerProviderStateMixin {
  final PaginatedListController _listController = PaginatedListController();

  // Controladores de animación
  late AnimationController _slideController;
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late AnimationController _dashboardController;
  late AnimationController _chartController;

  // Animaciones
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _dashboardAnimation;
  late Animation<double> _chartAnimation;

  // Estado de la vista
  FacturasViewMode _viewMode = FacturasViewMode.dashboard;
  FacturasFilter _currentFilter = FacturasFilter.todas;
  FacturasSortBy _sortBy = FacturasSortBy.fecha;
  bool _sortAscending = false;

  // Datos de estadísticas
  Map<String, dynamic> _estadisticas = {};
  bool _loadingStats = true;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadEstadisticas();
    _startEntryAnimations();
  }

  void _initializeAnimations() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _dashboardController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _chartController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    _dashboardAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _dashboardController,
      curve: Curves.easeOutCubic,
    ));

    _chartAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _chartController,
      curve: Curves.elasticOut,
    ));
  }

  void _startEntryAnimations() {
    Future.delayed(const Duration(milliseconds: 100), () {
      _fadeController.forward();
    });

    Future.delayed(const Duration(milliseconds: 200), () {
      _scaleController.forward();
    });

    Future.delayed(const Duration(milliseconds: 300), () {
      _slideController.forward();
    });

    Future.delayed(const Duration(milliseconds: 500), () {
      _dashboardController.forward();
    });

    Future.delayed(const Duration(milliseconds: 700), () {
      _chartController.forward();
    });
  }

  Future<void> _loadEstadisticas() async {
    setState(() {
      _loadingStats = true;
    });

    try {
      // Cargar todas las facturas para calcular estadísticas reales
      final facturas = await FacturaService.obtenerFacturasPaginadas(limit: 1000);

      double totalVentas = 0;
      int totalFacturas = facturas.length;
      int facturasPendientes = 0;
      int facturasPagadas = 0;
      int facturasVencidas = 0;
      Map<String, double> ventasPorMes = {};
      Map<String, int> facturasPorMetodo = {};
      double facturaMaxima = 0;
      double facturaMinima = double.infinity;

      // Obtener fecha actual para comparaciones
      final ahora = DateTime.now();

      for (var facturaMap in facturas) {
        final factura = Factura.fromMap(facturaMap);
        totalVentas += factura.total;

        // Encontrar factura máxima y mínima
        if (factura.total > facturaMaxima) {
          facturaMaxima = factura.total;
        }
        if (factura.total < facturaMinima) {
          facturaMinima = factura.total;
        }

        // Simular estados de factura basados en lógica real
        // (En una app real, esto vendría de la base de datos)
        final diasDesdeCreacion = ahora.difference(DateTime.now()).inDays.abs();

        if (factura.total < 500) {
          facturasPendientes++;
        } else if (diasDesdeCreacion > 30) {
          facturasVencidas++;
        } else {
          facturasPagadas++;
        }

        // Agrupar por método de pago real
        facturasPorMetodo[factura.metodoPago] =
            (facturasPorMetodo[factura.metodoPago] ?? 0) + 1;

        // Agrupar ventas por mes (usando fecha actual como ejemplo)
        String mesKey = '${ahora.year}-${ahora.month.toString().padLeft(2, '0')}';
        ventasPorMes[mesKey] = (ventasPorMes[mesKey] ?? 0) + factura.total;
      }

      // Calcular métricas adicionales
      double promedioVenta = totalFacturas > 0 ? totalVentas / totalFacturas : 0;
      String metodoMasUsado = _calcularMetodoMasUsado(facturasPorMetodo);
      double crecimientoMensual = _calcularCrecimientoMensual(ventasPorMes);

      setState(() {
        _estadisticas = {
          'totalVentas': totalVentas,
          'totalFacturas': totalFacturas,
          'facturasPendientes': facturasPendientes,
          'facturasPagadas': facturasPagadas,
          'facturasVencidas': facturasVencidas,
          'ventasPorMes': ventasPorMes,
          'facturasPorMetodo': facturasPorMetodo,
          'promedioVenta': promedioVenta,
          'facturaMaxima': facturaMaxima == 0 ? 0 : facturaMaxima,
          'facturaMinima': facturaMinima == double.infinity ? 0 : facturaMinima,
          'metodoMasUsado': metodoMasUsado,
          'crecimientoMensual': crecimientoMensual,
        };
        _loadingStats = false;
      });
    } catch (e) {
      log('Error cargando estadísticas: $e');
      setState(() {
        _estadisticas = {
          'totalVentas': 0.0,
          'totalFacturas': 0,
          'facturasPendientes': 0,
          'facturasPagadas': 0,
          'facturasVencidas': 0,
          'ventasPorMes': <String, double>{},
          'facturasPorMetodo': <String, int>{},
          'promedioVenta': 0.0,
          'facturaMaxima': 0.0,
          'facturaMinima': 0.0,
          'metodoMasUsado': 'N/A',
          'crecimientoMensual': 0.0,
        };
        _loadingStats = false;
      });
    }
  }

  String _calcularMetodoMasUsado(Map<String, int> facturasPorMetodo) {
    if (facturasPorMetodo.isEmpty) return 'N/A';

    String metodoMasUsado = facturasPorMetodo.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;

    return metodoMasUsado;
  }

  double _calcularCrecimientoMensual(Map<String, double> ventasPorMes) {
    if (ventasPorMes.length < 2) return 0.0;

    // Simular cálculo de crecimiento (en una app real sería más complejo)
    final valores = ventasPorMes.values.toList();
    if (valores.length >= 2) {
      final actual = valores.last;
      final anterior = valores[valores.length - 2];
      if (anterior > 0) {
        return ((actual - anterior) / anterior) * 100;
      }
    }
    return 0.0;
  }

  @override
  void dispose() {
    _slideController.dispose();
    _fadeController.dispose();
    _scaleController.dispose();
    _dashboardController.dispose();
    _chartController.dispose();
    super.dispose();
  }

  Future<List<Map<String, dynamic>>> _cargarFacturasPaginadas({
    int limit = 20,
    int offset = 0,
    String? searchQuery,
  }) async {
    // Obtener todas las facturas
    final todasLasFacturas = await FacturaService.obtenerFacturasPaginadas(
      limit: 1000, // Obtener todas para filtrar
      offset: 0,
      searchQuery: searchQuery,
    );

    // Aplicar filtro según el estado seleccionado
    List<Map<String, dynamic>> facturasFiltradas = [];

    for (var facturaMap in todasLasFacturas) {
      final factura = Factura.fromMap(facturaMap);
      bool incluir = false;

      switch (_currentFilter) {
        case FacturasFilter.todas:
          incluir = true;
          break;
        case FacturasFilter.pendientes:
          // Lógica para determinar si está pendiente
          incluir = factura.total < 500; // Ejemplo de lógica
          break;
        case FacturasFilter.pagadas:
          // Lógica para determinar si está pagada
          incluir = factura.total >= 500 && factura.total < 2000;
          break;
        case FacturasFilter.vencidas:
          // Lógica para determinar si está vencida
          incluir = factura.total >= 2000;
          break;
      }

      if (incluir) {
        facturasFiltradas.add(facturaMap);
      }
    }

    // Aplicar paginación
    final inicio = offset;
    final fin = math.min(inicio + limit, facturasFiltradas.length);

    if (inicio >= facturasFiltradas.length) {
      return [];
    }

    return facturasFiltradas.sublist(inicio, fin);
  }

  Future<int> _contarFacturas({String? searchQuery}) {
    return FacturaService.contarFacturas();
  }

  void _changeViewMode(FacturasViewMode newMode) {
    if (_viewMode != newMode) {
      setState(() {
        _viewMode = newMode;
      });
      _animateViewTransition();
      HapticFeedback.selectionClick();
    }
  }

  void _animateViewTransition() {
    _slideController.reset();
    _fadeController.reset();
    _scaleController.reset();

    Future.delayed(const Duration(milliseconds: 50), () {
      _fadeController.forward();
    });

    Future.delayed(const Duration(milliseconds: 100), () {
      _scaleController.forward();
    });

    Future.delayed(const Duration(milliseconds: 150), () {
      _slideController.forward();
    });
  }

  void _changeFilter(FacturasFilter newFilter) {
    setState(() {
      _currentFilter = newFilter;
    });
    // Refrescar la lista con el nuevo filtro
    _listController.refresh();
    HapticFeedback.selectionClick();
  }

  void _changeSorting(FacturasSortBy newSort) {
    setState(() {
      if (_sortBy == newSort) {
        _sortAscending = !_sortAscending;
      } else {
        _sortBy = newSort;
        _sortAscending = true;
      }
    });
    HapticFeedback.selectionClick();
  }

  Future<void> _adjuntarComprobante(String idFactura) async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf'],
      );

      if (result == null ||
          result.files.isEmpty ||
          result.files.single.path == null) {
        log("No se seleccionó ningún archivo 🚨");
        return;
      }

      // NOTA: Funcionalidad de subida de archivos pendiente de implementación
      // Para implementar completamente:
      // 1. Subir archivo a Firebase Storage
      // 2. Obtener URL de descarga
      // 3. Guardar URL en la factura correspondiente
      String fileName = result.files.single.name;

      // Por ahora, solo mostramos el nombre del archivo seleccionado

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("Archivo seleccionado: $fileName")),
        );
        setState(() {}); // Refrescar la lista
      }
    } catch (e) {
      log("Error al adjuntar comprobante: $e 🚨");
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text("Error al adjuntar comprobante")),
        );
      }
    }
  }

  Future<void> _eliminarFactura(String idFactura) async {
    await FacturaService.eliminarFactura(idFactura);
    if (mounted) setState(() {}); // Refrescar la lista
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      body: GradientBackground(
        child: Stack(
          children: [
            // Partículas de fondo
            FloatingParticles(
              particleCount: 30,
              particleColor: theme.primaryColor.withValues(alpha: 0.1),
              child: const SizedBox.expand(),
            ),

            // Contenido principal
            SafeArea(
              child: Column(
                children: [
                  _buildHeader(),
                  _buildViewModeSelector(),
                  Expanded(
                    child: _buildCurrentView(),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: _buildAnimatedFAB(),
    );
  }

  Widget _buildHeader() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            // Botón de regreso
            ScaleTransition(
              scale: _scaleAnimation,
              child: Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).cardColor,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [context.adaptiveShadow],
                ),
                child: IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.arrow_back),
                  tooltip: 'Volver',
                ),
              ),
            ),

            const SizedBox(width: 16),

            // Título y estadísticas rápidas
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Facturas',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  if (!_loadingStats && _estadisticas.isNotEmpty)
                    Row(
                      children: [
                        Text(
                          '${_estadisticas['totalFacturas'] ?? 0}',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Theme.of(context).primaryColor,
                          ),
                        ),
                        Text(
                          ' facturas • ',
                          style: TextStyle(
                            color: context.adaptiveTextColor.withValues(alpha: 0.7),
                          ),
                        ),
                        Text(
                          '\$${(_estadisticas['totalVentas'] ?? 0).toStringAsFixed(0)}',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Colors.green,
                          ),
                        ),
                      ],
                    ),
                ],
              ),
            ),

            // Botones de acción
            Row(
              children: [
                ScaleTransition(
                  scale: _scaleAnimation,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Theme.of(context).cardColor,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [context.adaptiveShadow],
                    ),
                    child: IconButton(
                      onPressed: () {
                        _loadEstadisticas();
                        _listController.refresh();
                      },
                      icon: const Icon(Icons.refresh),
                      tooltip: 'Actualizar',
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                ThemeToggleButton(
                  onThemeChanged: (theme) {
                    // Implementar cambio de tema si es necesario
                  },
                  currentTheme: ThemeMode.system,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildViewModeSelector() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
        child: Row(
          children: [
            Expanded(
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    _buildViewModeButton(
                      FacturasViewMode.dashboard,
                      Icons.dashboard,
                      'Dashboard',
                    ),
                    const SizedBox(width: 8),
                    _buildViewModeButton(
                      FacturasViewMode.list,
                      Icons.list,
                      'Lista',
                    ),
                    const SizedBox(width: 8),
                    _buildViewModeButton(
                      FacturasViewMode.grid,
                      Icons.grid_view,
                      'Cuadrícula',
                    ),
                    const SizedBox(width: 8),
                    _buildViewModeButton(
                      FacturasViewMode.analytics,
                      Icons.analytics,
                      'Análisis',
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(width: 12),
            _buildFilterButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildViewModeButton(FacturasViewMode mode, IconData icon, String label) {
    final isSelected = _viewMode == mode;

    return ScaleTransition(
      scale: _scaleAnimation,
      child: GestureDetector(
        onTap: () => _changeViewMode(mode),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: isSelected
                ? Theme.of(context).primaryColor
                : Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(20),
            boxShadow: isSelected ? [
              BoxShadow(
                color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ] : [context.adaptiveShadow],
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 18,
                color: isSelected
                    ? Colors.white
                    : Theme.of(context).iconTheme.color,
              ),
              const SizedBox(width: 6),
              Text(
                label,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: isSelected
                      ? Colors.white
                      : Theme.of(context).textTheme.bodyMedium?.color,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFilterButton() {
    return ScaleTransition(
      scale: _scaleAnimation,
      child: PopupMenuButton<FacturasFilter>(
        icon: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [context.adaptiveShadow],
          ),
          child: const Icon(Icons.filter_list),
        ),
        onSelected: _changeFilter,
        itemBuilder: (context) => [
          PopupMenuItem(
            value: FacturasFilter.todas,
            child: Row(
              children: [
                Icon(
                  Icons.all_inclusive,
                  color: _currentFilter == FacturasFilter.todas ? Colors.blue : null,
                ),
                const SizedBox(width: 8),
                const Text('Todas'),
              ],
            ),
          ),
          PopupMenuItem(
            value: FacturasFilter.pendientes,
            child: Row(
              children: [
                Icon(
                  Icons.pending,
                  color: _currentFilter == FacturasFilter.pendientes ? Colors.orange : null,
                ),
                const SizedBox(width: 8),
                const Text('Pendientes'),
              ],
            ),
          ),
          PopupMenuItem(
            value: FacturasFilter.pagadas,
            child: Row(
              children: [
                Icon(
                  Icons.check_circle,
                  color: _currentFilter == FacturasFilter.pagadas ? Colors.green : null,
                ),
                const SizedBox(width: 8),
                const Text('Pagadas'),
              ],
            ),
          ),
          PopupMenuItem(
            value: FacturasFilter.vencidas,
            child: Row(
              children: [
                Icon(
                  Icons.warning,
                  color: _currentFilter == FacturasFilter.vencidas ? Colors.red : null,
                ),
                const SizedBox(width: 8),
                const Text('Vencidas'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCurrentView() {
    switch (_viewMode) {
      case FacturasViewMode.dashboard:
        return _buildDashboardView();
      case FacturasViewMode.list:
        return _buildListView();
      case FacturasViewMode.grid:
        return _buildGridView();
      case FacturasViewMode.analytics:
        return _buildAnalyticsView();
    }
  }

  Widget _buildDashboardView() {
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              _buildStatsCards(),
              const SizedBox(height: 20),
              _buildQuickActions(),
              const SizedBox(height: 20),
              _buildRecentFacturas(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatsCards() {
    if (_loadingStats) {
      return _buildStatsLoading();
    }

    return AnimatedBuilder(
      animation: _dashboardAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _dashboardAnimation.value,
          child: Opacity(
            opacity: _dashboardAnimation.value,
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: _buildStatCard(
                        'Total Ventas',
                        '\$${(_estadisticas['totalVentas'] ?? 0).toStringAsFixed(0)}',
                        Icons.attach_money,
                        Colors.green,
                        trend: _formatearCrecimiento(_estadisticas['crecimientoMensual'] ?? 0),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildStatCard(
                        'Facturas',
                        '${_estadisticas['totalFacturas'] ?? 0}',
                        Icons.receipt_long,
                        Colors.blue,
                        trend: null, // Sin tendencia para total de facturas
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: _buildStatCard(
                        'Pendientes',
                        '${_estadisticas['facturasPendientes'] ?? 0}',
                        Icons.pending,
                        Colors.orange,
                        trend: null, // Sin tendencia para pendientes
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildStatCard(
                        'Promedio',
                        '\$${(_estadisticas['promedioVenta'] ?? 0).toStringAsFixed(0)}',
                        Icons.trending_up,
                        Colors.purple,
                        trend: null, // Sin tendencia para promedio
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color, {String? trend}) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [context.adaptiveShadow],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(icon, color: color, size: 18),
              ),
              const Spacer(),
              if (trend != null)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                  decoration: BoxDecoration(
                    color: trend.startsWith('+') ? Colors.green.withValues(alpha: 0.1) : Colors.red.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    trend,
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.w600,
                      color: trend.startsWith('+') ? Colors.green : Colors.red,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 2),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: context.adaptiveTextColor.withValues(alpha: 0.7),
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildStatsLoading() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(child: _buildLoadingCard()),
            const SizedBox(width: 12),
            Expanded(child: _buildLoadingCard()),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(child: _buildLoadingCard()),
            const SizedBox(width: 12),
            Expanded(child: _buildLoadingCard()),
          ],
        ),
      ],
    );
  }

  Widget _buildLoadingCard() {
    return Container(
      height: 120,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [context.adaptiveShadow],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          BreathingWidget(
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
          const Spacer(),
          BreathingWidget(
            child: Container(
              width: double.infinity,
              height: 20,
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
          const SizedBox(height: 8),
          BreathingWidget(
            child: Container(
              width: 80,
              height: 14,
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [context.adaptiveShadow],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Acciones Rápidas',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildQuickActionButton(
                  'Nueva Factura',
                  Icons.add,
                  Colors.green,
                  () async {
                    await Navigator.pushNamed(context, AppRoutes.agregarFactura);
                    _loadEstadisticas();
                    _listController.refresh();
                  },
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildQuickActionButton(
                  'Exportar',
                  Icons.download,
                  Colors.blue,
                  () async {
                    await _exportarFacturas();
                  },
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildQuickActionButton(
                  'Reportes',
                  Icons.analytics,
                  Colors.purple,
                  () {
                    _changeViewMode(FacturasViewMode.analytics);
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionButton(String label, IconData icon, Color color, VoidCallback onPressed) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 8),
            Text(
              label,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentFacturas() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [context.adaptiveShadow],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                'Facturas Recientes',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              TextButton(
                onPressed: () => _changeViewMode(FacturasViewMode.list),
                child: const Text('Ver todas'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          FutureBuilder<List<Map<String, dynamic>>>(
            future: _cargarFacturasPaginadas(limit: 3),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return Column(
                  children: List.generate(3, (index) => _buildRecentFacturaLoading()),
                );
              }

              if (snapshot.hasError || !snapshot.hasData || snapshot.data!.isEmpty) {
                return Center(
                  child: Column(
                    children: [
                      Icon(
                        Icons.receipt_long,
                        size: 48,
                        color: Colors.grey.shade400,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'No hay facturas recientes',
                        style: TextStyle(color: Colors.grey.shade600),
                      ),
                    ],
                  ),
                );
              }

              return Column(
                children: snapshot.data!.map((facturaMap) {
                  final factura = Factura.fromMap(facturaMap);
                  final clienteNombre = facturaMap['ClienteNombre'] ?? 'Cliente desconocido';

                  return _buildRecentFacturaItem(factura, clienteNombre);
                }).toList(),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildRecentFacturaItem(Factura factura, String clienteNombre) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.receipt_long,
              color: Theme.of(context).primaryColor,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Factura #${factura.idFactura}',
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  clienteNombre,
                  style: TextStyle(
                    color: context.adaptiveTextColor.withValues(alpha: 0.7),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '\$${factura.total.toStringAsFixed(2)}',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                factura.metodoPago,
                style: TextStyle(
                  color: context.adaptiveTextColor.withValues(alpha: 0.5),
                  fontSize: 11,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRecentFacturaLoading() {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          BreathingWidget(
            child: Container(
              width: 36,
              height: 36,
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                BreathingWidget(
                  child: Container(
                    width: double.infinity,
                    height: 14,
                    decoration: BoxDecoration(
                      color: Colors.grey.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
                const SizedBox(height: 6),
                BreathingWidget(
                  child: Container(
                    width: 100,
                    height: 12,
                    decoration: BoxDecoration(
                      color: Colors.grey.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
              ],
            ),
          ),
          BreathingWidget(
            child: Container(
              width: 60,
              height: 14,
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildListView() {
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: PaginatedListBuilder<Map<String, dynamic>>(
          controller: _listController,
          dataLoader: _cargarFacturasPaginadas,
          countLoader: _contarFacturas,
          searchHint: "Buscar facturas por ID, cliente o método de pago...",
          emptyMessage: "No hay facturas registradas",
          itemBuilder: (context, facturaMap, index) {
            final factura = Factura.fromMap(facturaMap);
            final clienteNombre = facturaMap['ClienteNombre'] ?? 'Cliente desconocido';

            return TweenAnimationBuilder<double>(
              duration: Duration(milliseconds: 300 + (index * 100)),
              tween: Tween(begin: 0.0, end: 1.0),
              builder: (context, value, child) {
                return Transform.translate(
                  offset: Offset(0, 50 * (1 - value)),
                  child: Opacity(
                    opacity: value,
                    child: _buildFacturaCard(factura, clienteNombre),
                  ),
                );
              },
            );
          },
        ),
      ),
    );
  }

  Widget _buildGridView() {
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: FutureBuilder<List<Map<String, dynamic>>>(
          future: _cargarFacturasPaginadas(limit: 100),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            }

            if (snapshot.hasError || !snapshot.hasData) {
              return const Center(child: Text('Error al cargar facturas'));
            }

            final facturas = snapshot.data!;

            return GridView.builder(
              padding: const EdgeInsets.all(20),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 0.8,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
              ),
              itemCount: facturas.length,
              itemBuilder: (context, index) {
                final facturaMap = facturas[index];
                final factura = Factura.fromMap(facturaMap);
                final clienteNombre = facturaMap['ClienteNombre'] ?? 'Cliente desconocido';

                return TweenAnimationBuilder<double>(
                  duration: Duration(milliseconds: 300 + (index * 50)),
                  tween: Tween(begin: 0.0, end: 1.0),
                  builder: (context, value, child) {
                    return Transform.scale(
                      scale: value,
                      child: _buildFacturaGridCard(factura, clienteNombre),
                    );
                  },
                );
              },
            );
          },
        ),
      ),
    );
  }

  Widget _buildAnalyticsView() {
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              _buildAnalyticsHeader(),
              const SizedBox(height: 20),
              _buildChartsSection(),
              const SizedBox(height: 20),
              _buildMetricsSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFacturaCard(Factura factura, String clienteNombre) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [context.adaptiveShadow],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () {
            Navigator.pushNamed(
              context,
              AppRoutes.detalleFactura,
              arguments: factura.idFactura,
            );
          },
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.receipt_long,
                    color: Theme.of(context).primaryColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Factura #${factura.idFactura}',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        clienteNombre,
                        style: TextStyle(
                          color: context.adaptiveTextColor.withValues(alpha: 0.7),
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        factura.metodoPago,
                        style: TextStyle(
                          color: context.adaptiveTextColor.withValues(alpha: 0.5),
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '\$${factura.total.toStringAsFixed(2)}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                        color: Colors.green,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        _buildActionButton(
                          Icons.picture_as_pdf,
                          Colors.blue,
                          () => _adjuntarComprobante(factura.idFactura),
                        ),
                        const SizedBox(width: 8),
                        _buildActionButton(
                          Icons.delete,
                          Colors.red,
                          () => _eliminarFactura(factura.idFactura),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton(IconData icon, Color color, VoidCallback onPressed) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(icon, color: color, size: 16),
      ),
    );
  }

  Widget _buildFacturaGridCard(Factura factura, String clienteNombre) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [context.adaptiveShadow],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () {
            Navigator.pushNamed(
              context,
              AppRoutes.detalleFactura,
              arguments: factura.idFactura,
            );
          },
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.receipt_long,
                        color: Theme.of(context).primaryColor,
                        size: 20,
                      ),
                    ),
                    const Spacer(),
                    _buildActionButton(
                      Icons.more_vert,
                      Colors.grey,
                      () {},
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Text(
                  'Factura #${factura.idFactura}',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
                Text(
                  clienteNombre,
                  style: TextStyle(
                    color: context.adaptiveTextColor.withValues(alpha: 0.7),
                    fontSize: 12,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const Spacer(),
                Row(
                  children: [
                    Icon(
                      Icons.payment,
                      size: 14,
                      color: context.adaptiveTextColor.withValues(alpha: 0.5),
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        factura.metodoPago,
                        style: TextStyle(
                          color: context.adaptiveTextColor.withValues(alpha: 0.5),
                          fontSize: 11,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  '\$${factura.total.toStringAsFixed(2)}',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAnalyticsHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [context.adaptiveShadow],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Análisis de Facturas',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Visualización detallada de métricas y tendencias',
            style: TextStyle(
              color: context.adaptiveTextColor.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChartsSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [context.adaptiveShadow],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Gráficos de Tendencias',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),

          // Gráfico de ventas por método de pago
          if (_estadisticas['facturasPorMetodo'] != null &&
              (_estadisticas['facturasPorMetodo'] as Map).isNotEmpty)
            SimpleBarChart(
              data: _convertirFacturasPorMetodo(),
              title: 'Facturas por Método de Pago',
              color: Colors.blue,
              height: 180,
            )
          else
            _buildEmptyChart('No hay datos de métodos de pago'),

          const SizedBox(height: 20),

          // Gráfico de tendencia de ventas (simulado)
          if (_estadisticas['ventasPorMes'] != null &&
              (_estadisticas['ventasPorMes'] as Map).isNotEmpty)
            SimpleLineChart(
              data: _estadisticas['ventasPorMes'] as Map<String, double>,
              title: 'Tendencia de Ventas Mensuales',
              color: Colors.green,
              height: 180,
            )
          else
            _buildEmptyChart('No hay datos de ventas mensuales'),
        ],
      ),
    );
  }

  Map<String, double> _convertirFacturasPorMetodo() {
    final facturasPorMetodo = _estadisticas['facturasPorMetodo'] as Map<String, int>? ?? {};
    final resultado = <String, double>{};

    for (var entry in facturasPorMetodo.entries) {
      resultado[entry.key] = entry.value.toDouble();
    }

    return resultado;
  }

  Widget _buildEmptyChart(String message) {
    return Container(
      height: 180,
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.bar_chart,
              size: 48,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 8),
            Text(
              message,
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricsSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [context.adaptiveShadow],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Métricas Detalladas',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),
          if (_estadisticas.isNotEmpty) ...[
            _buildMetricRow('Factura más alta', '\$${(_estadisticas['facturaMaxima'] ?? 0).toStringAsFixed(2)}'),
            _buildMetricRow('Factura más baja', '\$${(_estadisticas['facturaMinima'] ?? 0).toStringAsFixed(2)}'),
            _buildMetricRow('Método más usado', _estadisticas['metodoMasUsado'] ?? 'N/A'),
            _buildMetricRow('Crecimiento mensual', _formatearCrecimiento(_estadisticas['crecimientoMensual'] ?? 0)),
            _buildMetricRow('Facturas pagadas', '${_estadisticas['facturasPagadas'] ?? 0}'),
            _buildMetricRow('Facturas vencidas', '${_estadisticas['facturasVencidas'] ?? 0}'),
          ] else
            const Center(child: CircularProgressIndicator()),
        ],
      ),
    );
  }

  Widget _buildMetricRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: Text(
              label,
              style: TextStyle(
                color: context.adaptiveTextColor.withValues(alpha: 0.7),
              ),
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  String _formatearCrecimiento(double crecimiento) {
    if (crecimiento == 0) return '0%';

    String signo = crecimiento > 0 ? '+' : '';
    return '$signo${crecimiento.toStringAsFixed(1)}%';
  }

  Future<void> _exportarFacturas() async {
    try {
      // Mostrar diálogo de carga
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Exportando facturas...'),
            ],
          ),
        ),
      );

      // Obtener todas las facturas
      final facturas = await FacturaService.obtenerFacturasPaginadas(limit: 1000);

      // Crear contenido CSV (simulado)
      final csvLines = <String>[];
      csvLines.add('ID Factura,Cliente,Total,Método de Pago,Fecha');

      for (var facturaMap in facturas) {
        final factura = Factura.fromMap(facturaMap);
        final clienteNombre = facturaMap['ClienteNombre'] ?? 'Cliente desconocido';

        csvLines.add('${factura.idFactura},"$clienteNombre",${factura.total},${factura.metodoPago},${DateTime.now().toIso8601String()}');
      }

      // Cerrar diálogo de carga
      if (mounted) Navigator.pop(context);

      // Mostrar opciones de exportación
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Exportar Facturas'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('Se han preparado ${facturas.length} facturas para exportar.'),
                const SizedBox(height: 16),
                Text('${csvLines.length - 1} registros listos para descargar.'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cerrar'),
              ),
              ElevatedButton.icon(
                onPressed: () {
                  // En una app real, aquí se descargaría el archivo
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Exportación simulada completada'),
                      backgroundColor: Colors.green,
                    ),
                  );
                },
                icon: const Icon(Icons.download),
                label: const Text('Descargar CSV'),
              ),
            ],
          ),
        );
      }

    } catch (e) {
      // Cerrar diálogo de carga si está abierto
      if (mounted && Navigator.canPop(context)) {
        Navigator.pop(context);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error al exportar: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Widget _buildAnimatedFAB() {
    return ScaleTransition(
      scale: _scaleAnimation,
      child: FloatingActionButton.extended(
        onPressed: () async {
          log("Navegando a pantalla de agregar factura");
          await Navigator.pushNamed(context, AppRoutes.agregarFactura);
          _loadEstadisticas();
          _listController.refresh();
        },
        icon: const Icon(Icons.add),
        label: const Text('Nueva Factura'),
        backgroundColor: Colors.teal.shade700,
        foregroundColor: Colors.white,
        elevation: 8,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
    );
  }
}
