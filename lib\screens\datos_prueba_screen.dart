import 'package:flutter/material.dart';
import '../services/datos_prueba_service.dart';
import '../widgets/modern_buttons.dart';
import '../widgets/modern_loading.dart';
import '../theme/app_gradients.dart';
import '../utils/error_handler.dart';

/// Pantalla para gestionar datos de prueba
class DatosPruebaScreen extends StatefulWidget {
  const DatosPruebaScreen({super.key});

  @override
  State<DatosPruebaScreen> createState() => _DatosPruebaScreenState();
}

class _DatosPruebaScreenState extends State<DatosPruebaScreen> {
  bool _isLoading = false;
  bool _existenDatos = false;
  Map<String, int> _estadisticas = {};

  @override
  void initState() {
    super.initState();
    _verificarDatos();
  }

  Future<void> _verificarDatos() async {
    try {
      setState(() => _isLoading = true);
      
      final existen = await DatosPruebaService.existenDatosPrueba();
      final stats = await DatosPruebaService.obtenerEstadisticasDatosPrueba();
      
      if (mounted) {
        setState(() {
          _existenDatos = existen;
          _estadisticas = stats;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        ErrorHandler.showError(context, e.toString());
      }
    }
  }

  Future<void> _generarDatos() async {
    try {
      setState(() => _isLoading = true);
      
      await DatosPruebaService.generarDatosPrueba();
      
      if (mounted) {
        ErrorHandler.showSuccess(context, 'Datos de prueba generados exitosamente');
        await _verificarDatos();
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        ErrorHandler.showError(context, e.toString());
      }
    }
  }

  Future<void> _limpiarDatos() async {
    final confirmar = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Confirmar eliminación'),
          content: const Text('¿Estás seguro de que quieres eliminar todos los datos de prueba? Esta acción no se puede deshacer.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancelar'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
              child: const Text('Eliminar', style: TextStyle(color: Colors.white)),
            ),
          ],
        );
      },
    ) ?? false;

    if (!confirmar) return;

    try {
      setState(() => _isLoading = true);
      
      await DatosPruebaService.limpiarDatosPrueba();
      
      if (mounted) {
        ErrorHandler.showSuccess(context, 'Datos de prueba eliminados exitosamente');
        await _verificarDatos();
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        ErrorHandler.showError(context, e.toString());
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Datos de Prueba'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(
              child: ModernLoading(
                message: 'Procesando datos...',
              ),
            )
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildInfoCard(),
                  const SizedBox(height: 20),
                  _buildEstadisticasCard(),
                  const SizedBox(height: 20),
                  _buildAccionesCard(),
                  const SizedBox(height: 20),
                  _buildDescripcionCard(),
                ],
              ),
            ),
    );
  }

  Widget _buildInfoCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: _existenDatos ? AppGradients.success : AppGradients.warning,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Icon(
            _existenDatos ? Icons.check_circle : Icons.warning,
            color: Colors.white,
            size: 32,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _existenDatos ? 'Datos de Prueba Disponibles' : 'Sin Datos de Prueba',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _existenDatos 
                      ? 'La aplicación tiene datos de prueba cargados'
                      : 'Genera datos de prueba para probar la funcionalidad',
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEstadisticasCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.analytics, color: Colors.blue),
              SizedBox(width: 8),
              Text(
                'Estadísticas Actuales',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  'Clientes',
                  _estadisticas['clientes']?.toString() ?? '0',
                  Icons.people,
                  const Color(0xFF4CAF50),
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  'Facturas',
                  _estadisticas['facturas']?.toString() ?? '0',
                  Icons.receipt,
                  const Color(0xFF2196F3),
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  'Módulos',
                  _estadisticas['modulos']?.toString() ?? '0',
                  Icons.grid_view,
                  const Color(0xFF9C27B0),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 32),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }

  Widget _buildAccionesCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.settings, color: Colors.orange),
              SizedBox(width: 8),
              Text(
                'Acciones',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (!_existenDatos) ...[
            ModernButton(
              text: 'Generar Datos de Prueba',
              icon: Icons.add_circle,
              onPressed: _generarDatos,
              color: const Color(0xFF4CAF50),
              width: double.infinity,
            ),
          ] else ...[
            ModernButton(
              text: 'Regenerar Datos',
              icon: Icons.refresh,
              onPressed: () async {
                await _limpiarDatos();
                if (mounted && !_existenDatos) {
                  await _generarDatos();
                }
              },
              color: const Color(0xFF2196F3),
              width: double.infinity,
            ),
            const SizedBox(height: 12),
            ModernButton(
              text: 'Eliminar Datos',
              icon: Icons.delete,
              onPressed: _limpiarDatos,
              color: const Color(0xFFE53935),
              width: double.infinity,
            ),
          ],
          const SizedBox(height: 12),
          ModernButton(
            text: 'Actualizar Estado',
            icon: Icons.sync,
            onPressed: _verificarDatos,
            isOutlined: true,
            color: const Color(0xFF607D8B),
            width: double.infinity,
          ),
        ],
      ),
    );
  }

  Widget _buildDescripcionCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info, color: Colors.blue.shade700),
              const SizedBox(width: 8),
              Text(
                'Información sobre Datos de Prueba',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'Los datos de prueba incluyen:',
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: Colors.blue.shade800,
            ),
          ),
          const SizedBox(height: 8),
          _buildInfoItem('• 25 clientes con diferentes tipos de suscripción'),
          _buildInfoItem('• 5 módulos (4 activos, 1 inactivo)'),
          _buildInfoItem('• 50 facturas de los últimos 6 meses'),
          _buildInfoItem('• Estados realistas (activo, vencido, suspendido)'),
          _buildInfoItem('• Fechas y montos realistas'),
          const SizedBox(height: 12),
          Text(
            'Esto te permitirá probar todas las funcionalidades de la aplicación con datos realistas.',
            style: TextStyle(
              fontSize: 14,
              color: Colors.blue.shade600,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 14,
          color: Colors.blue.shade700,
        ),
      ),
    );
  }
}
