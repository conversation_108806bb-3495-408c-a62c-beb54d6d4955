import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../services/modulo_service.dart';

class DebugModulosScreen extends StatefulWidget {
  const DebugModulosScreen({super.key});

  @override
  State<DebugModulosScreen> createState() => _DebugModulosScreenState();
}

class _DebugModulosScreenState extends State<DebugModulosScreen> {
  final List<String> _debugLogs = [];
  bool _isLoading = false;

  void _addLog(String message) {
    setState(() {
      _debugLogs.add('${DateTime.now().toIso8601String()}: $message');
    });
    log(message);
  }

  Future<void> _verificarModulosDirectos() async {
    setState(() {
      _isLoading = true;
      _debugLogs.clear();
    });

    _addLog('🔍 Verificando módulos directamente en Firebase...');

    try {
      // Consulta directa a Firebase
      final snapshot =
          await FirebaseFirestore.instance.collection('modulos').get();

      _addLog(
          '📊 Documentos encontrados en colección "modulos": ${snapshot.docs.length}');

      for (var doc in snapshot.docs) {
        final data = doc.data();
        _addLog('📄 Documento ${doc.id}: $data');
      }

      if (snapshot.docs.isEmpty) {
        _addLog('⚠️ No hay documentos en la colección "modulos"');
      }
    } catch (e) {
      _addLog('❌ Error consultando Firebase directamente: $e');
    }

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _verificarModulosServicio() async {
    setState(() {
      _isLoading = true;
    });

    _addLog('🔍 Verificando módulos usando ModuloService...');

    try {
      // Usar el servicio
      final modulos = await ModuloService.obtenerModulosPaginados(
        limit: 50,
        offset: 0,
      );

      _addLog('📊 Módulos obtenidos por servicio: ${modulos.length}');

      for (var modulo in modulos) {
        _addLog('📄 Módulo procesado: $modulo');
      }

      if (modulos.isEmpty) {
        _addLog('⚠️ El servicio no devolvió módulos');
      }
    } catch (e) {
      _addLog('❌ Error usando ModuloService: $e');
    }

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _contarModulos() async {
    setState(() {
      _isLoading = true;
    });

    _addLog('🔢 Contando módulos...');

    try {
      final count = await ModuloService.contarModulos();
      _addLog('📊 Total de módulos según servicio: $count');
    } catch (e) {
      _addLog('❌ Error contando módulos: $e');
    }

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _crearModuloPrueba() async {
    setState(() {
      _isLoading = true;
    });

    _addLog('🧪 Creando módulos de prueba...');

    try {
      // Crear múltiples módulos de prueba
      for (int i = 1; i <= 3; i++) {
        final idModulo =
            'MOD_DEBUG_${DateTime.now().millisecondsSinceEpoch}_$i';

        await FirebaseFirestore.instance
            .collection('modulos')
            .doc(idModulo)
            .set({
          'nombre': 'Módulo Debug $i',
          'precio': (25000.0 * i),
          'descripcion': 'Módulo de prueba $i',
          'activo': true,
          'fechaCreacion': FieldValue.serverTimestamp(),
        });

        _addLog('✅ Módulo de prueba creado: $idModulo');

        // Pequeña pausa entre creaciones
        await Future.delayed(const Duration(milliseconds: 500));
      }

      // Verificar que se crearon
      await Future.delayed(const Duration(seconds: 2));
      await _verificarModulosDirectos();
    } catch (e) {
      _addLog('❌ Error creando módulos de prueba: $e');
    }

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _arreglarModulosExistentes() async {
    setState(() {
      _isLoading = true;
    });

    _addLog('🔧 Arreglando módulos existentes...');

    try {
      // Obtener todos los módulos sin filtro
      final snapshot =
          await FirebaseFirestore.instance.collection('modulos').get();

      _addLog('📊 Módulos encontrados para arreglar: ${snapshot.docs.length}');

      int arreglados = 0;
      for (var doc in snapshot.docs) {
        final data = doc.data();

        // Si no tiene el campo activo o es null, establecerlo como true
        if (!data.containsKey('activo') || data['activo'] == null) {
          await doc.reference.update({'activo': true});
          _addLog('✅ Módulo arreglado: ${doc.id} - ${data['nombre']}');
          arreglados++;
        } else {
          _addLog(
              'ℹ️ Módulo ya tiene campo activo: ${doc.id} - activo: ${data['activo']}');
        }
      }

      _addLog('🎉 Proceso completado. Módulos arreglados: $arreglados');

      // Verificar resultado
      await Future.delayed(const Duration(seconds: 1));
      await _verificarModulosServicio();
    } catch (e) {
      _addLog('❌ Error arreglando módulos: $e');
    }

    setState(() {
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Debug Módulos'),
        backgroundColor: Colors.orange.shade700,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // Botones de acción
          Container(
            padding: const EdgeInsets.all(16),
            child: Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _verificarModulosDirectos,
                  icon: const Icon(Icons.search),
                  label: const Text('Verificar Firebase'),
                ),
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _verificarModulosServicio,
                  icon: const Icon(Icons.api),
                  label: const Text('Verificar Servicio'),
                ),
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _contarModulos,
                  icon: const Icon(Icons.numbers),
                  label: const Text('Contar Módulos'),
                ),
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _crearModuloPrueba,
                  icon: const Icon(Icons.add),
                  label: const Text('Crear Prueba'),
                ),
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _arreglarModulosExistentes,
                  icon: const Icon(Icons.build, color: Colors.orange),
                  label: const Text('Arreglar Módulos'),
                ),
                ElevatedButton.icon(
                  onPressed: () {
                    setState(() {
                      _debugLogs.clear();
                    });
                  },
                  icon: const Icon(Icons.clear),
                  label: const Text('Limpiar'),
                ),
              ],
            ),
          ),

          // Indicador de carga
          if (_isLoading) const LinearProgressIndicator(),

          // Logs
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.black87,
                borderRadius: BorderRadius.circular(8),
              ),
              child: ListView.builder(
                itemCount: _debugLogs.length,
                itemBuilder: (context, index) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 2),
                    child: Text(
                      _debugLogs[index],
                      style: const TextStyle(
                        color: Colors.green,
                        fontFamily: 'monospace',
                        fontSize: 12,
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
