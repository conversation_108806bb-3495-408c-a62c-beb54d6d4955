import 'package:flutter/material.dart';
import 'dart:ui';
import 'dart:math' as math;

/// Widget con efecto Glassmorphism
class GlassmorphicContainer extends StatelessWidget {
  final Widget child;
  final double width;
  final double height;
  final BorderRadius? borderRadius;
  final double blur;
  final double opacity;
  final Color? color;
  final Border? border;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;

  const GlassmorphicContainer({
    super.key,
    required this.child,
    required this.width,
    required this.height,
    this.borderRadius,
    this.blur = 10.0,
    this.opacity = 0.1,
    this.color,
    this.border,
    this.padding,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      child: ClipRRect(
        borderRadius: borderRadius ?? BorderRadius.circular(16),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: blur, sigmaY: blur),
          child: Container(
            width: width,
            height: height,
            padding: padding,
            decoration: BoxDecoration(
              color: (color ?? Colors.white).withValues(alpha: opacity),
              borderRadius: borderRadius ?? BorderRadius.circular(16),
              border: border ?? Border.all(
                color: Colors.white.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: child,
          ),
        ),
      ),
    );
  }
}

/// Widget con efecto Neumorphism
class NeumorphicContainer extends StatefulWidget {
  final Widget child;
  final double width;
  final double height;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final BorderRadius? borderRadius;
  final Color? color;
  final double depth;
  final bool isPressed;
  final VoidCallback? onTap;
  final Duration animationDuration;

  const NeumorphicContainer({
    super.key,
    required this.child,
    required this.width,
    required this.height,
    this.padding,
    this.margin,
    this.borderRadius,
    this.color,
    this.depth = 8.0,
    this.isPressed = false,
    this.onTap,
    this.animationDuration = const Duration(milliseconds: 150),
  });

  @override
  State<NeumorphicContainer> createState() => _NeumorphicContainerState();
}

class _NeumorphicContainerState extends State<NeumorphicContainer>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    _animation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    setState(() => _isPressed = true);
    _controller.forward();
  }

  void _handleTapUp(TapUpDetails details) {
    setState(() => _isPressed = false);
    _controller.reverse();
    widget.onTap?.call();
  }

  void _handleTapCancel() {
    setState(() => _isPressed = false);
    _controller.reverse();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final backgroundColor = widget.color ?? theme.colorScheme.surface;
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      margin: widget.margin,
      child: GestureDetector(
        onTapDown: widget.onTap != null ? _handleTapDown : null,
        onTapUp: widget.onTap != null ? _handleTapUp : null,
        onTapCancel: widget.onTap != null ? _handleTapCancel : null,
        child: AnimatedBuilder(
          animation: _animation,
          builder: (context, child) {
            return Transform.scale(
              scale: _animation.value,
              child: Container(
                width: widget.width,
                height: widget.height,
                padding: widget.padding,
                decoration: BoxDecoration(
                  color: backgroundColor,
                  borderRadius: widget.borderRadius ?? BorderRadius.circular(16),
                  boxShadow: _isPressed || widget.isPressed
                      ? _getInsetShadows(backgroundColor, isDark)
                      : _getOutsetShadows(backgroundColor, isDark),
                ),
                child: widget.child,
              ),
            );
          },
        ),
      ),
    );
  }

  List<BoxShadow> _getOutsetShadows(Color backgroundColor, bool isDark) {
    final shadowColor = isDark ? Colors.black : Colors.grey.shade400;
    final highlightColor = isDark ? Colors.white.withValues(alpha: 0.1) : Colors.white;

    return [
      BoxShadow(
        color: shadowColor.withValues(alpha: 0.3),
        offset: Offset(widget.depth, widget.depth),
        blurRadius: widget.depth * 2,
        spreadRadius: 0,
      ),
      BoxShadow(
        color: highlightColor,
        offset: Offset(-widget.depth / 2, -widget.depth / 2),
        blurRadius: widget.depth,
        spreadRadius: 0,
      ),
    ];
  }

  List<BoxShadow> _getInsetShadows(Color backgroundColor, bool isDark) {
    final shadowColor = isDark ? Colors.black : Colors.grey.shade400;
    final highlightColor = isDark ? Colors.white.withValues(alpha: 0.05) : Colors.white.withValues(alpha: 0.7);

    return [
      BoxShadow(
        color: shadowColor.withValues(alpha: 0.2),
        offset: Offset(-widget.depth / 2, -widget.depth / 2),
        blurRadius: widget.depth,
        spreadRadius: 0,
      ),
      BoxShadow(
        color: highlightColor,
        offset: Offset(widget.depth / 2, widget.depth / 2),
        blurRadius: widget.depth,
        spreadRadius: 0,
      ),
    ];
  }
}

/// Widget con efecto de partículas flotantes
class FloatingParticles extends StatefulWidget {
  final int particleCount;
  final Color particleColor;
  final double minSize;
  final double maxSize;
  final double speed;
  final Widget child;

  const FloatingParticles({
    super.key,
    this.particleCount = 20,
    this.particleColor = Colors.white,
    this.minSize = 2.0,
    this.maxSize = 6.0,
    this.speed = 1.0,
    required this.child,
  });

  @override
  State<FloatingParticles> createState() => _FloatingParticlesState();
}

class _FloatingParticlesState extends State<FloatingParticles>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late List<Particle> _particles;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 10),
      vsync: this,
    )..repeat();
    
    _particles = _generateParticles();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  List<Particle> _generateParticles() {
    final random = math.Random();
    return List.generate(widget.particleCount, (index) {
      return Particle(
        x: random.nextDouble(),
        y: random.nextDouble(),
        size: widget.minSize + random.nextDouble() * (widget.maxSize - widget.minSize),
        speedX: (random.nextDouble() - 0.5) * widget.speed,
        speedY: (random.nextDouble() - 0.5) * widget.speed,
        opacity: 0.3 + random.nextDouble() * 0.7,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        widget.child,
        Positioned.fill(
          child: AnimatedBuilder(
            animation: _controller,
            builder: (context, child) {
              return CustomPaint(
                painter: ParticlesPainter(
                  particles: _particles,
                  animation: _controller.value,
                  color: widget.particleColor,
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}

class Particle {
  double x;
  double y;
  final double size;
  final double speedX;
  final double speedY;
  final double opacity;

  Particle({
    required this.x,
    required this.y,
    required this.size,
    required this.speedX,
    required this.speedY,
    required this.opacity,
  });
}

class ParticlesPainter extends CustomPainter {
  final List<Particle> particles;
  final double animation;
  final Color color;

  ParticlesPainter({
    required this.particles,
    required this.animation,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    for (final particle in particles) {
      // Actualizar posición
      particle.x += particle.speedX * 0.01;
      particle.y += particle.speedY * 0.01;

      // Wrap around edges
      if (particle.x < 0) particle.x = 1;
      if (particle.x > 1) particle.x = 0;
      if (particle.y < 0) particle.y = 1;
      if (particle.y > 1) particle.y = 0;

      // Dibujar partícula
      paint.color = color.withValues(alpha: particle.opacity);
      canvas.drawCircle(
        Offset(particle.x * size.width, particle.y * size.height),
        particle.size,
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// Widget con efecto de parallax
class ParallaxContainer extends StatefulWidget {
  final Widget child;
  final double parallaxFactor;
  final Axis direction;

  const ParallaxContainer({
    super.key,
    required this.child,
    this.parallaxFactor = 0.5,
    this.direction = Axis.vertical,
  });

  @override
  State<ParallaxContainer> createState() => _ParallaxContainerState();
}

class _ParallaxContainerState extends State<ParallaxContainer> {
  double _offset = 0.0;

  @override
  Widget build(BuildContext context) {
    return NotificationListener<ScrollNotification>(
      onNotification: (notification) {
        if (notification is ScrollUpdateNotification) {
          setState(() {
            _offset = notification.metrics.pixels * widget.parallaxFactor;
          });
        }
        return false;
      },
      child: Transform.translate(
        offset: widget.direction == Axis.vertical
            ? Offset(0, -_offset)
            : Offset(-_offset, 0),
        child: widget.child,
      ),
    );
  }
}

/// Widget con efecto de breathing (respiración)
class BreathingWidget extends StatefulWidget {
  final Widget child;
  final Duration duration;
  final double minScale;
  final double maxScale;

  const BreathingWidget({
    super.key,
    required this.child,
    this.duration = const Duration(seconds: 3),
    this.minScale = 0.95,
    this.maxScale = 1.05,
  });

  @override
  State<BreathingWidget> createState() => _BreathingWidgetState();
}

class _BreathingWidgetState extends State<BreathingWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(duration: widget.duration, vsync: this)
      ..repeat(reverse: true);
    
    _animation = Tween<double>(
      begin: widget.minScale,
      end: widget.maxScale,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Transform.scale(
          scale: _animation.value,
          child: widget.child,
        );
      },
    );
  }
}

/// Widget con efecto de glow (resplandor)
class GlowContainer extends StatelessWidget {
  final Widget child;
  final Color glowColor;
  final double glowRadius;
  final double glowOpacity;

  const GlowContainer({
    super.key,
    required this.child,
    required this.glowColor,
    this.glowRadius = 20.0,
    this.glowOpacity = 0.3,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
            color: glowColor.withValues(alpha: glowOpacity),
            blurRadius: glowRadius,
            spreadRadius: glowRadius / 4,
          ),
        ],
      ),
      child: child,
    );
  }
}

/// Widget con efecto de morphing entre formas
class MorphingShape extends StatefulWidget {
  final List<ShapeData> shapes;
  final Duration duration;
  final Color color;
  final double size;

  const MorphingShape({
    super.key,
    required this.shapes,
    this.duration = const Duration(seconds: 2),
    this.color = Colors.blue,
    this.size = 100,
  });

  @override
  State<MorphingShape> createState() => _MorphingShapeState();
}

class _MorphingShapeState extends State<MorphingShape>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  int _currentShapeIndex = 0;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(duration: widget.duration, vsync: this)
      ..addStatusListener((status) {
        if (status == AnimationStatus.completed) {
          setState(() {
            _currentShapeIndex = (_currentShapeIndex + 1) % widget.shapes.length;
          });
          _controller.reset();
          _controller.forward();
        }
      });
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return CustomPaint(
          size: Size(widget.size, widget.size),
          painter: MorphingShapePainter(
            currentShape: widget.shapes[_currentShapeIndex],
            nextShape: widget.shapes[(_currentShapeIndex + 1) % widget.shapes.length],
            progress: _controller.value,
            color: widget.color,
          ),
        );
      },
    );
  }
}

class ShapeData {
  final List<Offset> points;
  final String name;

  ShapeData({required this.points, required this.name});

  static ShapeData circle(double radius) {
    final points = <Offset>[];
    for (int i = 0; i < 8; i++) {
      final angle = (i / 8) * 2 * math.pi;
      points.add(Offset(
        radius + radius * math.cos(angle),
        radius + radius * math.sin(angle),
      ));
    }
    return ShapeData(points: points, name: 'circle');
  }

  static ShapeData square(double size) {
    return ShapeData(
      points: [
        Offset(0, 0),
        Offset(size, 0),
        Offset(size, size),
        Offset(0, size),
        Offset(0, 0),
        Offset(size, 0),
        Offset(size, size),
        Offset(0, size),
      ],
      name: 'square',
    );
  }

  static ShapeData triangle(double size) {
    return ShapeData(
      points: [
        Offset(size / 2, 0),
        Offset(size, size),
        Offset(0, size),
        Offset(size / 2, 0),
        Offset(size, size),
        Offset(0, size),
        Offset(size / 2, 0),
        Offset(size, size),
      ],
      name: 'triangle',
    );
  }
}

class MorphingShapePainter extends CustomPainter {
  final ShapeData currentShape;
  final ShapeData nextShape;
  final double progress;
  final Color color;

  MorphingShapePainter({
    required this.currentShape,
    required this.nextShape,
    required this.progress,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final path = Path();
    
    // Interpolar entre formas
    for (int i = 0; i < currentShape.points.length; i++) {
      final currentPoint = currentShape.points[i];
      final nextPoint = nextShape.points[i % nextShape.points.length];
      final interpolatedPoint = Offset.lerp(currentPoint, nextPoint, progress)!;
      
      if (i == 0) {
        path.moveTo(interpolatedPoint.dx, interpolatedPoint.dy);
      } else {
        path.lineTo(interpolatedPoint.dx, interpolatedPoint.dy);
      }
    }
    
    path.close();
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
