import 'package:flutter_test/flutter_test.dart';
import 'package:shop_3m/models/modulo_model.dart';

void main() {
  group('Modulo Model Tests', () {
    group('Constructor and Validation Tests', () {
      test('should create a valid Modulo with correct data', () {
        final modulo = Modulo(
          idModulo: 'MOD001',
          nombre: 'Módu<PERSON> de Ventas',
          precio: 50000.0,
        );

        expect(modulo.idModulo, equals('MOD001'));
        expect(modulo.nombre, equals('Módulo de Ventas'));
        expect(modulo.precio, equals(50000.0));
      });

      test('should throw ArgumentError for empty nombre', () {
        expect(
          () => Mo<PERSON><PERSON>(
            idModulo: 'MOD001',
            nombre: '',
            precio: 50000.0,
          ),
          throwsA(isA<ArgumentError>()),
        );
      });

      test('should throw ArgumentError for short nombre', () {
        expect(
          () => Modulo(
            idModulo: 'MOD001',
            nombre: 'A',
            precio: 50000.0,
          ),
          throwsA(isA<ArgumentError>()),
        );
      });

      test('should throw ArgumentError for negative precio', () {
        expect(
          () => Modulo(
            idModulo: 'MOD001',
            nombre: 'Módulo Test',
            precio: -100.0,
          ),
          throwsA(isA<ArgumentError>()),
        );
      });

      test('should throw ArgumentError for zero precio', () {
        expect(
          () => Modulo(
            idModulo: 'MOD001',
            nombre: 'Módulo Test',
            precio: 0.0,
          ),
          throwsA(isA<ArgumentError>()),
        );
      });
    });

    group('Validation Methods Tests', () {
      test('isValidNombre should return true for valid names', () {
        expect(Modulo.isValidNombre('Módulo Test'), isTrue);
        expect(Modulo.isValidNombre('AB'), isTrue);
        expect(Modulo.isValidNombre('  Valid Name  '), isTrue);
      });

      test('isValidNombre should return false for invalid names', () {
        expect(Modulo.isValidNombre(''), isFalse);
        expect(Modulo.isValidNombre('A'), isFalse);
        expect(Modulo.isValidNombre('   '), isFalse);
      });

      test('isValidPrecio should return true for valid prices', () {
        expect(Modulo.isValidPrecio(0.01), isTrue);
        expect(Modulo.isValidPrecio(50000.0), isTrue);
        expect(Modulo.isValidPrecio(999999999.0), isTrue);
      });

      test('isValidPrecio should return false for invalid prices', () {
        expect(Modulo.isValidPrecio(0.0), isFalse);
        expect(Modulo.isValidPrecio(-1.0), isFalse);
      });

      test('validateNombre should return null for valid names', () {
        expect(Modulo.validateNombre('Módulo Test'), isNull);
        expect(Modulo.validateNombre('AB'), isNull);
      });

      test('validateNombre should return error for invalid names', () {
        expect(Modulo.validateNombre(''), isNotNull);
        expect(Modulo.validateNombre('A'), isNotNull);
        expect(Modulo.validateNombre('A' * 101), isNotNull);
        expect(Modulo.validateNombre('Test@#\$'), isNotNull);
      });

      test('validatePrecio should return null for valid prices', () {
        expect(Modulo.validatePrecio('50000'), isNull);
        expect(Modulo.validatePrecio('0.01'), isNull);
      });

      test('validatePrecio should return error for invalid prices', () {
        expect(Modulo.validatePrecio(''), isNotNull);
        expect(Modulo.validatePrecio('0'), isNotNull);
        expect(Modulo.validatePrecio('-100'), isNotNull);
        expect(Modulo.validatePrecio('abc'), isNotNull);
        expect(Modulo.validatePrecio('1000000000'), isNotNull);
      });
    });

    group('validateAll Tests', () {
      test('should return empty map for valid data', () {
        final errors = Modulo.validateAll(
          nombre: 'Módulo Test',
          precioStr: '50000',
        );
        expect(errors, isEmpty);
      });

      test('should return errors for invalid data', () {
        final errors = Modulo.validateAll(
          nombre: '',
          precioStr: '0',
        );
        expect(errors, isNotEmpty);
        expect(errors.containsKey('nombre'), isTrue);
        expect(errors.containsKey('precio'), isTrue);
      });
    });

    group('Serialization Tests', () {
      test('toMap should return correct Map', () {
        final modulo = Modulo(
          idModulo: 'MOD001',
          nombre: '  Módulo Test  ',
          precio: 50000.0,
        );

        final map = modulo.toMap();
        expect(map['IdModulo'], equals('MOD001'));
        expect(map['Nombre'], equals('Módulo Test')); // Trimmed
        expect(map['Precio'], equals(50000.0));
      });

      test('fromMap should create correct Modulo', () {
        final map = {
          'IdModulo': 'MOD001',
          'Nombre': 'Módulo Test',
          'Precio': 50000.0,
        };

        final modulo = Modulo.fromMap(map);
        expect(modulo.idModulo, equals('MOD001'));
        expect(modulo.nombre, equals('Módulo Test'));
        expect(modulo.precio, equals(50000.0));
      });

      test('fromMap should handle integer precio', () {
        final map = {
          'IdModulo': 'MOD001',
          'Nombre': 'Módulo Test',
          'Precio': 50000, // Integer
        };

        final modulo = Modulo.fromMap(map);
        expect(modulo.precio, equals(50000.0));
      });

      test('fromMap should handle missing values', () {
        final map = <String, dynamic>{};

        expect(
          () => Modulo.fromMap(map),
          throwsA(isA<FormatException>()),
        );
      });

      test('fromMap should throw FormatException for invalid data', () {
        final map = {
          'IdModulo': 'MOD001',
          'Nombre': '', // Invalid
          'Precio': 50000.0,
        };

        expect(
          () => Modulo.fromMap(map),
          throwsA(isA<FormatException>()),
        );
      });
    });

    group('Utility Methods Tests', () {
      test('copyWith should create modified copy', () {
        final original = Modulo(
          idModulo: 'MOD001',
          nombre: 'Original',
          precio: 50000.0,
        );

        final modified = original.copyWith(
          nombre: 'Modified',
          precio: 75000.0,
        );

        expect(modified.idModulo, equals('MOD001'));
        expect(modified.nombre, equals('Modified'));
        expect(modified.precio, equals(75000.0));
      });

      test('copyWith should keep original values when no parameters provided', () {
        final original = Modulo(
          idModulo: 'MOD001',
          nombre: 'Original',
          precio: 50000.0,
        );

        final copy = original.copyWith();

        expect(copy.idModulo, equals(original.idModulo));
        expect(copy.nombre, equals(original.nombre));
        expect(copy.precio, equals(original.precio));
      });

      test('toString should return formatted string', () {
        final modulo = Modulo(
          idModulo: 'MOD001',
          nombre: 'Test Module',
          precio: 50000.0,
        );

        final str = modulo.toString();
        expect(str, contains('MOD001'));
        expect(str, contains('Test Module'));
        expect(str, contains('50000.0'));
      });

      test('precioFormateado should return formatted price', () {
        final modulo = Modulo(
          idModulo: 'MOD001',
          nombre: 'Test',
          precio: 50000.0,
        );

        expect(modulo.precioFormateado, equals('\$50000.00'));
      });

      test('nombreNormalizado should return lowercase trimmed name', () {
        final modulo = Modulo(
          idModulo: 'MOD001',
          nombre: '  Test MODULE  ',
          precio: 50000.0,
        );

        expect(modulo.nombreNormalizado, equals('test module'));
      });
    });

    group('Equality Tests', () {
      test('should be equal for same values', () {
        final modulo1 = Modulo(
          idModulo: 'MOD001',
          nombre: 'Test',
          precio: 50000.0,
        );

        final modulo2 = Modulo(
          idModulo: 'MOD001',
          nombre: 'Test',
          precio: 50000.0,
        );

        expect(modulo1, equals(modulo2));
        expect(modulo1.hashCode, equals(modulo2.hashCode));
      });

      test('should not be equal for different values', () {
        final modulo1 = Modulo(
          idModulo: 'MOD001',
          nombre: 'Test',
          precio: 50000.0,
        );

        final modulo2 = Modulo(
          idModulo: 'MOD002',
          nombre: 'Test',
          precio: 50000.0,
        );

        expect(modulo1, isNot(equals(modulo2)));
      });
    });
  });
}
