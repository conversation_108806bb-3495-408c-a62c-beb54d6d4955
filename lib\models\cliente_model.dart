import 'package:cloud_firestore/cloud_firestore.dart';

class Cliente {
  final String idCliente;
  final String nombre;
  final String telefono;
  final String email;
  final String? imagenUrl; // Nueva propiedad para imagen de perfil
  final DateTime? fechaInicioSuscripcion;
  final DateTime? fechaVencimientoSuscripcion;
  final String? tipoSuscripcion;
  final double? montoSuscripcion;
  final String? estadoSuscripcion; // 'activa', 'vencida', 'suspendida'

  Cliente({
    required this.idCliente,
    required this.nombre,
    required this.telefono,
    required this.email,
    this.imagenUrl,
    this.fechaInicioSuscripcion,
    this.fechaVencimientoSuscripcion,
    this.tipoSuscripcion,
    this.montoSuscripcion,
    this.estadoSuscripcion,
  }) {
    // Validaciones en el constructor
    _validateNombre(nombre);
    _validateTelefono(telefono);
    _validateEmail(email);
  }

  // Validaciones privadas
  static void _validateNombre(String nombre) {
    if (nombre.trim().isEmpty) {
      throw ArgumentError('El nombre no puede estar vacío');
    }
    if (nombre.trim().length < 2) {
      throw ArgumentError('El nombre debe tener al menos 2 caracteres');
    }
    if (nombre.length > 100) {
      throw ArgumentError('El nombre no puede exceder 100 caracteres');
    }
  }

  static void _validateTelefono(String telefono) {
    if (telefono.trim().isEmpty) {
      throw ArgumentError('El teléfono no puede estar vacío');
    }
    // Regex para teléfono: permite números, espacios, guiones, paréntesis y +
    final telefonoRegex = RegExp(r'^[\+]?[0-9\s\-\(\)]{7,20}$');
    if (!telefonoRegex.hasMatch(telefono.trim())) {
      throw ArgumentError('Formato de teléfono inválido');
    }
  }

  static void _validateEmail(String email) {
    if (email.trim().isEmpty) {
      throw ArgumentError('El email no puede estar vacío');
    }
    // Regex para email más robusta
    final emailRegex =
        RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
    if (!emailRegex.hasMatch(email.trim())) {
      throw ArgumentError('Formato de email inválido');
    }
    if (email.length > 255) {
      throw ArgumentError('El email no puede exceder 255 caracteres');
    }
  }

  // Métodos de validación estáticos públicos
  static bool isValidNombre(String nombre) {
    try {
      _validateNombre(nombre);
      return true;
    } catch (e) {
      return false;
    }
  }

  static bool isValidTelefono(String telefono) {
    try {
      _validateTelefono(telefono);
      return true;
    } catch (e) {
      return false;
    }
  }

  static bool isValidEmail(String email) {
    try {
      _validateEmail(email);
      return true;
    } catch (e) {
      return false;
    }
  }

  // Método para validar todos los campos y retornar errores
  static Map<String, String> validateAll({
    required String nombre,
    required String telefono,
    required String email,
  }) {
    Map<String, String> errores = {};

    try {
      _validateNombre(nombre);
    } catch (e) {
      errores['nombre'] = e.toString().replaceFirst('ArgumentError: ', '');
    }

    try {
      _validateTelefono(telefono);
    } catch (e) {
      errores['telefono'] = e.toString().replaceFirst('ArgumentError: ', '');
    }

    try {
      _validateEmail(email);
    } catch (e) {
      errores['email'] = e.toString().replaceFirst('ArgumentError: ', '');
    }

    return errores;
  }

  // Convertir Cliente a Map (para Firebase)
  Map<String, dynamic> toMap() {
    return {
      'IdCliente': idCliente,
      'Nombre': nombre.trim(),
      'Telefono': telefono.trim(),
      'Email': email.trim().toLowerCase(),
      'imagenUrl': imagenUrl,
      'fechaInicioSuscripcion': fechaInicioSuscripcion?.millisecondsSinceEpoch,
      'fechaVencimientoSuscripcion':
          fechaVencimientoSuscripcion?.millisecondsSinceEpoch,
      'tipoSuscripcion': tipoSuscripcion,
      'montoSuscripcion': montoSuscripcion,
      'estadoSuscripcion': estadoSuscripcion,
    };
  }

  // Método auxiliar para parsear fechas que pueden estar en diferentes formatos
  static DateTime? _parseFecha(dynamic fecha) {
    if (fecha == null) return null;

    try {
      // Si es un string (formato ISO8601)
      if (fecha is String) {
        return DateTime.parse(fecha);
      }
      // Si es un número (milliseconds)
      if (fecha is int) {
        return DateTime.fromMillisecondsSinceEpoch(fecha);
      }
      return null;
    } catch (e) {
      // Error parseando fecha: $fecha - $e
      return null;
    }
  }

  // Crear Cliente desde un Map (al recuperar datos) con validación
  factory Cliente.fromMap(Map<String, dynamic> map) {
    try {
      return Cliente(
        idCliente: map['IdCliente'] ?? "",
        nombre: map['Nombre'] ?? "",
        telefono: map['Telefono'] ?? "",
        email: map['Email'] ?? "",
        imagenUrl: map['imagenUrl'],
        fechaInicioSuscripcion: _parseFecha(map['fechaInicioSuscripcion']),
        fechaVencimientoSuscripcion:
            _parseFecha(map['fechaVencimientoSuscripcion']),
        tipoSuscripcion: map['tipoSuscripcion'],
        montoSuscripcion: map['montoSuscripcion']?.toDouble(),
        estadoSuscripcion: map['estadoSuscripcion'],
      );
    } catch (e) {
      throw FormatException('Error al crear Cliente desde Map: $e');
    }
  }

  // Crear Cliente desde Firestore
  factory Cliente.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return Cliente(
      idCliente: data['idCliente'] ?? doc.id,
      nombre: data['nombre'] ?? '',
      telefono: data['telefono'] ?? '',
      email: data['email'] ?? '',
      imagenUrl: data['imagenUrl'],
      fechaInicioSuscripcion: data['fechaInicioSuscripcion'] != null
          ? (data['fechaInicioSuscripcion'] as Timestamp).toDate()
          : null,
      fechaVencimientoSuscripcion: data['fechaVencimientoSuscripcion'] != null
          ? (data['fechaVencimientoSuscripcion'] as Timestamp).toDate()
          : null,
      tipoSuscripcion: data['tipoSuscripcion'],
      montoSuscripcion: data['montoSuscripcion']?.toDouble(),
      estadoSuscripcion: data['estadoSuscripcion'],
    );
  }

  // Método copyWith para inmutabilidad
  Cliente copyWith({
    String? idCliente,
    String? nombre,
    String? telefono,
    String? email,
    String? imagenUrl,
    DateTime? fechaInicioSuscripcion,
    DateTime? fechaVencimientoSuscripcion,
    String? tipoSuscripcion,
    double? montoSuscripcion,
    String? estadoSuscripcion,
  }) {
    return Cliente(
      idCliente: idCliente ?? this.idCliente,
      nombre: nombre ?? this.nombre,
      telefono: telefono ?? this.telefono,
      email: email ?? this.email,
      imagenUrl: imagenUrl ?? this.imagenUrl,
      fechaInicioSuscripcion:
          fechaInicioSuscripcion ?? this.fechaInicioSuscripcion,
      fechaVencimientoSuscripcion:
          fechaVencimientoSuscripcion ?? this.fechaVencimientoSuscripcion,
      tipoSuscripcion: tipoSuscripcion ?? this.tipoSuscripcion,
      montoSuscripcion: montoSuscripcion ?? this.montoSuscripcion,
      estadoSuscripcion: estadoSuscripcion ?? this.estadoSuscripcion,
    );
  }

  // Método toString para debugging
  @override
  String toString() {
    return 'Cliente(idCliente: $idCliente, nombre: $nombre, '
        'telefono: $telefono, email: $email)';
  }

  // Método para obtener nombre completo formateado
  String get nombreFormateado => nombre.trim();

  // Método para obtener email normalizado
  String get emailNormalizado => email.trim().toLowerCase();

  // Métodos para suscripciones
  bool get tieneSuscripcion => fechaVencimientoSuscripcion != null;

  bool get suscripcionActiva =>
      tieneSuscripcion &&
      fechaVencimientoSuscripcion!.isAfter(DateTime.now()) &&
      estadoSuscripcion == 'activa';

  bool get suscripcionVencida =>
      tieneSuscripcion && fechaVencimientoSuscripcion!.isBefore(DateTime.now());

  int get diasParaVencer {
    if (!tieneSuscripcion) return -1;
    final diferencia = fechaVencimientoSuscripcion!.difference(DateTime.now());
    return diferencia.inDays;
  }

  bool get necesitaNotificacion => diasParaVencer <= 3 && diasParaVencer >= 0;

  String get estadoSuscripcionCalculado {
    if (!tieneSuscripcion) return 'sin_suscripcion';
    if (suscripcionVencida) return 'vencida';
    if (diasParaVencer <= 3) return 'por_vencer';
    return 'activa';
  }
}
