import 'package:cloud_firestore/cloud_firestore.dart';

class Factura {
  final String idFactura;
  final String cliente;
  final String metodoPago;
  final double total;
  final String? comprobante;
  final DateTime fecha;
  final String estado; // 'pendiente', 'pagada', 'cancelada'
  final List<String> modulos; // IDs de módulos incluidos

  Factura({
    required this.idFactura,
    required this.cliente,
    required this.metodoPago,
    required this.total,
    required this.fecha,
    required this.estado,
    this.comprobante,
    this.modulos = const [],
  });

  factory Factura.fromMap(Map<String, dynamic> json) {
    return Factura(
      idFactura: json["IdFactura"] ?? json["idFactura"] ?? '',
      cliente: json["Cliente"] ?? json["cliente"] ?? '',
      metodoPago: json["MetodoPago"] ?? json["metodoPago"] ?? '',
      total: (json["Total"] ?? json["total"] ?? 0).toDouble(),
      fecha: json["fecha"] != null
          ? DateTime.parse(json["fecha"])
          : DateTime.now(),
      estado: json["estado"] ?? 'pendiente',
      comprobante: json["Comprobante"] ?? json["comprobante"],
      modulos: List<String>.from(json["modulos"] ?? []),
    );
  }

  factory Factura.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return Factura(
      idFactura: data['idFactura'] ?? doc.id,
      cliente: data['cliente'] ?? data['clienteId'] ?? '',
      metodoPago: data['metodoPago'] ?? '',
      total: (data['total'] ?? 0).toDouble(),
      fecha: data['fecha'] != null
          ? (data['fecha'] as Timestamp).toDate()
          : DateTime.now(),
      estado: data['estado'] ?? 'pendiente',
      comprobante: data['comprobante'],
      modulos: List<String>.from(data['modulos'] ?? []),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      "idFactura": idFactura,
      "cliente": cliente,
      "metodoPago": metodoPago,
      "total": total,
      "fecha": fecha.toIso8601String(),
      "estado": estado,
      "comprobante": comprobante,
      "modulos": modulos,
    };
  }

  Map<String, dynamic> toFirestore() {
    return {
      "idFactura": idFactura,
      "cliente": cliente,
      "metodoPago": metodoPago,
      "total": total,
      "fecha": Timestamp.fromDate(fecha),
      "estado": estado,
      "comprobante": comprobante,
      "modulos": modulos,
    };
  }
}