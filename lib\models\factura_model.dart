class Factura {
  final String idFactura;
  final String cliente;
  final String metodoPago;
  final double total;
  final String? comprobante;

  Factura({
    required this.idFactura,
    required this.cliente,
    required this.metodoPago,
    required this.total,
    this.comprobante,
  });

  factory Factura.fromMap(Map<String, dynamic> json) {
    return Factura(
      idFactura: json["IdFactura"],
      cliente: json["Cliente"],
      metodoPago: json["MetodoPago"],
      total: json["Total"].toDouble(),
      comprobante: json["Comprobante"],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      "IdFactura": idFactura,
      "Cliente": cliente,
      "MetodoPago": metodoPago,
      "Total": total,
      "Comprobante": comprobante,
    };
  }
}