import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math' as math;

/// Sistema de gamificación sutil para aplicaciones empresariales
class GamificationSystem {
  static final GamificationSystem _instance = GamificationSystem._internal();
  factory GamificationSystem() => _instance;
  GamificationSystem._internal();

  final List<Achievement> _achievements = [];
  final List<ProgressGoal> _goals = [];
  int _currentStreak = 0;
  int _totalPoints = 0;

  // Getters
  List<Achievement> get achievements => List.unmodifiable(_achievements);
  List<ProgressGoal> get goals => List.unmodifiable(_goals);
  int get currentStreak => _currentStreak;
  int get totalPoints => _totalPoints;

  /// Agregar logro
  void addAchievement(Achievement achievement) {
    _achievements.add(achievement);
    _totalPoints += achievement.points;
  }

  /// Agregar objetivo
  void addGoal(ProgressGoal goal) {
    _goals.add(goal);
  }

  /// Actualizar racha
  void updateStreak(int streak) {
    _currentStreak = streak;
  }

  /// Verificar si se desbloqueó un logro
  bool checkAchievement(String id) {
    return _achievements.any((a) => a.id == id && a.isUnlocked);
  }
}

/// Modelo de logro
class Achievement {
  final String id;
  final String title;
  final String description;
  final IconData icon;
  final Color color;
  final int points;
  final bool isUnlocked;
  final DateTime? unlockedAt;

  Achievement({
    required this.id,
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
    required this.points,
    this.isUnlocked = false,
    this.unlockedAt,
  });

  Achievement copyWith({
    bool? isUnlocked,
    DateTime? unlockedAt,
  }) {
    return Achievement(
      id: id,
      title: title,
      description: description,
      icon: icon,
      color: color,
      points: points,
      isUnlocked: isUnlocked ?? this.isUnlocked,
      unlockedAt: unlockedAt ?? this.unlockedAt,
    );
  }
}

/// Modelo de objetivo de progreso
class ProgressGoal {
  final String id;
  final String title;
  final String description;
  final double currentValue;
  final double targetValue;
  final String unit;
  final Color color;
  final IconData icon;

  ProgressGoal({
    required this.id,
    required this.title,
    required this.description,
    required this.currentValue,
    required this.targetValue,
    required this.unit,
    required this.color,
    required this.icon,
  });

  double get progress => (currentValue / targetValue).clamp(0.0, 1.0);
  bool get isCompleted => currentValue >= targetValue;
}

/// Widget para mostrar logros
class AchievementBadge extends StatefulWidget {
  final Achievement achievement;
  final VoidCallback? onTap;

  const AchievementBadge({
    super.key,
    required this.achievement,
    this.onTap,
  });

  @override
  State<AchievementBadge> createState() => _AchievementBadgeState();
}

class _AchievementBadgeState extends State<AchievementBadge>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.elasticOut),
    );

    _rotationAnimation = Tween<double>(begin: 0.0, end: 2 * math.pi).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );

    if (widget.achievement.isUnlocked) {
      _controller.forward();
    }
  }

  @override
  void didUpdateWidget(AchievementBadge oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.achievement.isUnlocked && !oldWidget.achievement.isUnlocked) {
      _controller.forward();
      _showUnlockAnimation();
    }
  }

  void _showUnlockAnimation() {
    HapticFeedback.mediumImpact();
    // Aquí se podría agregar confetti o partículas
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Transform.rotate(
              angle: widget.achievement.isUnlocked ? 0 : _rotationAnimation.value,
              child: Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: widget.achievement.isUnlocked
                      ? widget.achievement.color
                      : Colors.grey.shade300,
                  boxShadow: widget.achievement.isUnlocked
                      ? [
                          BoxShadow(
                            color: widget.achievement.color.withValues(alpha: 0.3),
                            blurRadius: 12,
                            spreadRadius: 2,
                          ),
                        ]
                      : null,
                ),
                child: Icon(
                  widget.achievement.icon,
                  color: widget.achievement.isUnlocked
                      ? Colors.white
                      : Colors.grey.shade600,
                  size: 32,
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

/// Widget para mostrar progreso de objetivos
class ProgressGoalCard extends StatefulWidget {
  final ProgressGoal goal;

  const ProgressGoalCard({
    super.key,
    required this.goal,
  });

  @override
  State<ProgressGoalCard> createState() => _ProgressGoalCardState();
}

class _ProgressGoalCardState extends State<ProgressGoalCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _progressAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _progressAnimation = Tween<double>(begin: 0.0, end: widget.goal.progress).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeOutCubic),
    );

    _controller.forward();
  }

  @override
  void didUpdateWidget(ProgressGoalCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.goal.progress != oldWidget.goal.progress) {
      _progressAnimation = Tween<double>(
        begin: _progressAnimation.value,
        end: widget.goal.progress,
      ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeOutCubic));
      _controller.forward(from: 0);
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: widget.goal.color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    widget.goal.icon,
                    color: widget.goal.color,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.goal.title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        widget.goal.description,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            AnimatedBuilder(
              animation: _progressAnimation,
              builder: (context, child) {
                return Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '${(widget.goal.currentValue * _progressAnimation.value / widget.goal.progress).toStringAsFixed(0)}${widget.goal.unit}',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: widget.goal.color,
                          ),
                        ),
                        Text(
                          '${widget.goal.targetValue.toStringAsFixed(0)}${widget.goal.unit}',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    LinearProgressIndicator(
                      value: _progressAnimation.value,
                      backgroundColor: Colors.grey.shade300,
                      valueColor: AlwaysStoppedAnimation<Color>(widget.goal.color),
                      minHeight: 8,
                    ),
                    const SizedBox(height: 4),
                    Align(
                      alignment: Alignment.centerRight,
                      child: Text(
                        '${(_progressAnimation.value * 100).toStringAsFixed(0)}%',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}

/// Widget para mostrar racha actual
class StreakIndicator extends StatefulWidget {
  final int streak;
  final String label;
  final Color color;

  const StreakIndicator({
    super.key,
    required this.streak,
    this.label = 'Racha',
    this.color = Colors.orange,
  });

  @override
  State<StreakIndicator> createState() => _StreakIndicatorState();
}

class _StreakIndicatorState extends State<StreakIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _flameAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    )..repeat(reverse: true);

    _flameAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: widget.color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: widget.color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          AnimatedBuilder(
            animation: _flameAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: widget.streak > 0 ? _flameAnimation.value : 1.0,
                child: Icon(
                  Icons.local_fire_department,
                  color: widget.streak > 0 ? widget.color : Colors.grey,
                  size: 20,
                ),
              );
            },
          ),
          const SizedBox(width: 8),
          Text(
            '${widget.streak} ${widget.label}',
            style: TextStyle(
              color: widget.streak > 0 ? widget.color : Colors.grey,
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }
}

/// Widget para celebración de logros
class CelebrationOverlay extends StatefulWidget {
  final String message;
  final Color color;
  final VoidCallback onComplete;

  const CelebrationOverlay({
    super.key,
    required this.message,
    required this.color,
    required this.onComplete,
  });

  @override
  State<CelebrationOverlay> createState() => _CelebrationOverlayState();
}

class _CelebrationOverlayState extends State<CelebrationOverlay>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late AnimationController _particleController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _particleController = AnimationController(
      duration: const Duration(milliseconds: 3000),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.6, curve: Curves.elasticOut),
      ),
    );

    _fadeAnimation = Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.7, 1.0, curve: Curves.easeOut),
      ),
    );

    _controller.forward().then((_) {
      widget.onComplete();
    });

    _particleController.forward();

    HapticFeedback.heavyImpact();
  }

  @override
  void dispose() {
    _controller.dispose();
    _particleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.black.withValues(alpha: 0.5),
      child: Stack(
        children: [
          // Partículas de celebración
          Positioned.fill(
            child: AnimatedBuilder(
              animation: _particleController,
              builder: (context, child) {
                return CustomPaint(
                  painter: CelebrationParticlesPainter(
                    animation: _particleController.value,
                    color: widget.color,
                  ),
                );
              },
            ),
          ),
          
          // Mensaje principal
          Center(
            child: AnimatedBuilder(
              animation: Listenable.merge([_scaleAnimation, _fadeAnimation]),
              builder: (context, child) {
                return Opacity(
                  opacity: _fadeAnimation.value,
                  child: Transform.scale(
                    scale: _scaleAnimation.value,
                    child: Container(
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: widget.color.withValues(alpha: 0.3),
                            blurRadius: 20,
                            spreadRadius: 5,
                          ),
                        ],
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.celebration,
                            color: widget.color,
                            size: 48,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            '¡Felicitaciones!',
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: widget.color,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            widget.message,
                            textAlign: TextAlign.center,
                            style: const TextStyle(
                              fontSize: 16,
                              color: Colors.black87,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

/// Painter para partículas de celebración
class CelebrationParticlesPainter extends CustomPainter {
  final double animation;
  final Color color;
  final List<CelebrationParticle> particles;

  CelebrationParticlesPainter({
    required this.animation,
    required this.color,
  }) : particles = _generateParticles(color);

  static List<CelebrationParticle> _generateParticles(Color color) {
    final random = math.Random();
    return List.generate(30, (index) {
      return CelebrationParticle(
        x: 0.5,
        y: 0.5,
        velocityX: (random.nextDouble() - 0.5) * 4,
        velocityY: (random.nextDouble() - 0.5) * 4,
        size: random.nextDouble() * 6 + 2,
        color: Color.lerp(color, Colors.white, random.nextDouble() * 0.3) ?? color,
        life: 1.0,
      );
    });
  }

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    for (final particle in particles) {
      // Actualizar posición
      particle.x += particle.velocityX * animation * 0.02;
      particle.y += particle.velocityY * animation * 0.02;
      particle.life = 1.0 - animation;

      if (particle.life <= 0) continue;

      paint.color = particle.color.withValues(alpha: particle.life);
      
      canvas.drawCircle(
        Offset(particle.x * size.width, particle.y * size.height),
        particle.size * particle.life,
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class CelebrationParticle {
  double x;
  double y;
  final double velocityX;
  final double velocityY;
  final double size;
  final Color color;
  double life;

  CelebrationParticle({
    required this.x,
    required this.y,
    required this.velocityX,
    required this.velocityY,
    required this.size,
    required this.color,
    required this.life,
  });
}
