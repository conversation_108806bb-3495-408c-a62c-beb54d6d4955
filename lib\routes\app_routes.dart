import 'package:flutter/material.dart';
import '../screens/auth_wrapper.dart';
import '../screens/auth_screen.dart';
import '../screens/home_screen.dart';
import '../screens/clientes_screen.dart';
import '../screens/agregar_cliente_screen.dart';
import '../screens/editar_cliente_screen.dart';
import '../screens/facturas_screen.dart';
import '../screens/agregar_factura_screen.dart';
import '../screens/detalle_factura_screen.dart';
import '../screens/modulos_screen.dart';
import '../screens/reportes_screen.dart';
import '../screens/configuracion_screen.dart';
import '../screens/debug_facturas_screen.dart';
import '../screens/debug_modulos_screen.dart';

import '../models/cliente_model.dart';

/// Clase que define todas las rutas de la aplicación
class AppRoutes {
  // Nombres de rutas constantes
  static const String root = '/';
  static const String auth = '/auth';
  static const String home = '/home';
  static const String clientes = '/clientes';
  static const String agregarCliente = '/clientes/agregar';
  static const String editarCliente = '/clientes/editar';
  static const String facturas = '/facturas';
  static const String agregarFactura = '/facturas/agregar';
  static const String detalleFactura = '/facturas/detalle';
  static const String modulos = '/modulos';
  static const String reportes = '/reportes';
  static const String configuracion = '/configuracion';
  static const String debugFacturas = '/debug-facturas';
  static const String debugModulos = '/debug-modulos';

  /// Mapa de rutas de la aplicación
  static Map<String, WidgetBuilder> get routes => {
        root: (context) => const AuthWrapper(),
        auth: (context) => const AuthScreen(),
        home: (context) => const HomeScreen(),
        clientes: (context) => const ClientesScreen(),
        agregarCliente: (context) => const AgregarClienteScreen(),
        facturas: (context) => const FacturasScreen(),
        agregarFactura: (context) => const AgregarFacturaScreen(),
        modulos: (context) => const ModulosScreen(),
        reportes: (context) => const ReportesScreen(),
        configuracion: (context) => const ConfiguracionScreen(),
        debugFacturas: (context) => const DebugFacturasScreen(),
        debugModulos: (context) => const DebugModulosScreen(),
      };

  /// Generador de rutas para manejar rutas con parámetros
  static Route<dynamic>? onGenerateRoute(RouteSettings settings) {
    switch (settings.name) {
      case editarCliente:
        if (settings.arguments is Cliente) {
          final cliente = settings.arguments as Cliente;
          return MaterialPageRoute(
            builder: (context) => EditarClienteScreen(cliente: cliente),
            settings: settings,
          );
        }
        return _errorRoute('Cliente no válido para editar');

      case detalleFactura:
        if (settings.arguments is String) {
          final idFactura = settings.arguments as String;
          return MaterialPageRoute(
            builder: (context) =>
                DetalleFacturaScreen(idFactura: idFactura),
            settings: settings,
          );
        }
        return _errorRoute('ID de factura no válido');

      default:
        return null; // Deja que Flutter maneje las rutas estáticas
    }
  }

  /// Ruta de error para casos no válidos
  static Route<dynamic> _errorRoute(String message) {
    return MaterialPageRoute(
      builder: (context) => Scaffold(
        appBar: AppBar(title: const Text('Error')),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              Text(
                'Error de navegación',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              Text(
                message,
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pushNamedAndRemoveUntil(
                  home,
                  (route) => false,
                ),
                child: const Text('Volver al inicio'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Extensión para facilitar la navegación con rutas nombradas
extension NavigationExtension on BuildContext {
  /// Navegar a una ruta nombrada
  Future<T?> pushNamed<T extends Object?>(String routeName,
      {Object? arguments}) {
    return Navigator.of(this).pushNamed<T>(routeName, arguments: arguments);
  }

  /// Navegar y reemplazar la ruta actual
  Future<T?> pushReplacementNamed<T extends Object?, TO extends Object?>(
    String routeName, {
    Object? arguments,
    TO? result,
  }) {
    return Navigator.of(this).pushReplacementNamed<T, TO>(
      routeName,
      arguments: arguments,
      result: result,
    );
  }

  /// Navegar y limpiar el stack de navegación
  Future<T?> pushNamedAndRemoveUntil<T extends Object?>(
    String routeName,
    bool Function(Route<dynamic>) predicate, {
    Object? arguments,
  }) {
    return Navigator.of(this).pushNamedAndRemoveUntil<T>(
      routeName,
      predicate,
      arguments: arguments,
    );
  }

  /// Volver a la pantalla anterior
  void pop<T extends Object?>([T? result]) {
    Navigator.of(this).pop<T>(result);
  }

  /// Verificar si se puede volver atrás
  bool canPop() {
    return Navigator.of(this).canPop();
  }
}
