import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:developer';

/// Servicio de caché para mejorar el rendimiento de la aplicación
class CacheService {
  static SharedPreferences? _prefs;
  static const Duration _defaultExpiration = Duration(minutes: 30);
  
  /// Inicializar el servicio de caché
  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
    log('🗄️ CacheService inicializado');
  }
  
  /// Obtener instancia de SharedPreferences
  static SharedPreferences get _preferences {
    if (_prefs == null) {
      throw Exception('CacheService no ha sido inicializado. Llama a CacheService.init() primero.');
    }
    return _prefs!;
  }
  
  /// Guardar datos en caché con expiración
  static Future<bool> set<T>(
    String key, 
    T value, {
    Duration? expiration,
  }) async {
    try {
      final expirationTime = DateTime.now().add(expiration ?? _defaultExpiration);
      
      final cacheData = {
        'value': value,
        'expiration': expirationTime.millisecondsSinceEpoch,
        'type': T.toString(),
      };
      
      final jsonString = jsonEncode(cacheData);
      final success = await _preferences.setString(key, jsonString);
      
      if (success) {
        log('💾 Guardado en caché: $key (expira: ${expirationTime.toLocal()})');
      }
      
      return success;
    } catch (e) {
      log('❌ Error guardando en caché $key: $e');
      return false;
    }
  }
  
  /// Obtener datos del caché
  static T? get<T>(String key) {
    try {
      final jsonString = _preferences.getString(key);
      if (jsonString == null) return null;
      
      final cacheData = jsonDecode(jsonString) as Map<String, dynamic>;
      final expirationTime = DateTime.fromMillisecondsSinceEpoch(cacheData['expiration']);
      
      // Verificar si ha expirado
      if (DateTime.now().isAfter(expirationTime)) {
        log('⏰ Caché expirado para: $key');
        remove(key);
        return null;
      }
      
      log('✅ Obtenido del caché: $key');
      return cacheData['value'] as T;
    } catch (e) {
      log('❌ Error obteniendo del caché $key: $e');
      return null;
    }
  }
  
  /// Verificar si existe una clave en caché y no ha expirado
  static bool has(String key) {
    return get<dynamic>(key) != null;
  }
  
  /// Remover una clave del caché
  static Future<bool> remove(String key) async {
    try {
      final success = await _preferences.remove(key);
      if (success) {
        log('🗑️ Removido del caché: $key');
      }
      return success;
    } catch (e) {
      log('❌ Error removiendo del caché $key: $e');
      return false;
    }
  }
  
  /// Limpiar todo el caché
  static Future<bool> clear() async {
    try {
      final success = await _preferences.clear();
      if (success) {
        log('🧹 Caché completamente limpiado');
      }
      return success;
    } catch (e) {
      log('❌ Error limpiando caché: $e');
      return false;
    }
  }
  
  /// Limpiar caché expirado
  static Future<void> clearExpired() async {
    try {
      final keys = _preferences.getKeys();
      final now = DateTime.now();
      int removedCount = 0;
      
      for (final key in keys) {
        final jsonString = _preferences.getString(key);
        if (jsonString == null) continue;
        
        try {
          final cacheData = jsonDecode(jsonString) as Map<String, dynamic>;
          final expirationTime = DateTime.fromMillisecondsSinceEpoch(cacheData['expiration']);
          
          if (now.isAfter(expirationTime)) {
            await _preferences.remove(key);
            removedCount++;
          }
        } catch (e) {
          // Si no se puede parsear, probablemente no es un elemento de caché
          continue;
        }
      }
      
      if (removedCount > 0) {
        log('🧹 Limpiados $removedCount elementos expirados del caché');
      }
    } catch (e) {
      log('❌ Error limpiando caché expirado: $e');
    }
  }
  
  /// Obtener información del caché
  static Map<String, dynamic> getCacheInfo() {
    try {
      final keys = _preferences.getKeys();
      final now = DateTime.now();
      int totalItems = 0;
      int expiredItems = 0;
      int validItems = 0;
      
      for (final key in keys) {
        final jsonString = _preferences.getString(key);
        if (jsonString == null) continue;
        
        try {
          final cacheData = jsonDecode(jsonString) as Map<String, dynamic>;
          final expirationTime = DateTime.fromMillisecondsSinceEpoch(cacheData['expiration']);
          
          totalItems++;
          if (now.isAfter(expirationTime)) {
            expiredItems++;
          } else {
            validItems++;
          }
        } catch (e) {
          // No es un elemento de caché válido
          continue;
        }
      }
      
      return {
        'totalItems': totalItems,
        'validItems': validItems,
        'expiredItems': expiredItems,
        'cacheHitRate': totalItems > 0 ? (validItems / totalItems * 100).toStringAsFixed(1) : '0.0',
      };
    } catch (e) {
      log('❌ Error obteniendo información del caché: $e');
      return {
        'totalItems': 0,
        'validItems': 0,
        'expiredItems': 0,
        'cacheHitRate': '0.0',
      };
    }
  }
  
  /// Claves predefinidas para diferentes tipos de datos
  static class CacheKeys {
    static const String clientes = 'cache_clientes';
    static const String modulos = 'cache_modulos';
    static const String facturas = 'cache_facturas';
    static const String estadisticas = 'cache_estadisticas';
    static const String configuracion = 'cache_configuracion';
    static const String usuario = 'cache_usuario';
    
    // Claves con parámetros
    static String clientesPaginados(int page, int limit) => 'cache_clientes_${page}_$limit';
    static String modulosPaginados(int page, int limit) => 'cache_modulos_${page}_$limit';
    static String facturasPaginadas(int page, int limit) => 'cache_facturas_${page}_$limit';
    static String detalleFactura(String facturaId) => 'cache_detalle_factura_$facturaId';
    static String busquedaClientes(String query) => 'cache_busqueda_clientes_$query';
    static String busquedaModulos(String query) => 'cache_busqueda_modulos_$query';
  }
}

/// Mixin para agregar funcionalidad de caché a los servicios
mixin CacheableMixin {
  /// Obtener datos con caché automático
  Future<T> getWithCache<T>(
    String cacheKey,
    Future<T> Function() fetchFunction, {
    Duration? expiration,
    bool forceRefresh = false,
  }) async {
    // Si se fuerza el refresh, no usar caché
    if (!forceRefresh) {
      final cachedData = CacheService.get<T>(cacheKey);
      if (cachedData != null) {
        log('📦 Usando datos del caché para: $cacheKey');
        return cachedData;
      }
    }
    
    // Obtener datos frescos
    log('🌐 Obteniendo datos frescos para: $cacheKey');
    final freshData = await fetchFunction();
    
    // Guardar en caché
    await CacheService.set(cacheKey, freshData, expiration: expiration);
    
    return freshData;
  }
  
  /// Invalidar caché relacionado
  Future<void> invalidateCache(List<String> keys) async {
    for (final key in keys) {
      await CacheService.remove(key);
    }
    log('🗑️ Invalidado caché para: ${keys.join(', ')}');
  }
}
