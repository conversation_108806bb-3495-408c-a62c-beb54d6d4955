import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'dart:developer';

class FirebaseService {
  // Instancias de Firebase
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseStorage _storage = FirebaseStorage.instance;

  // Getters para acceso externo
  static FirebaseAuth get auth => _auth;
  static FirebaseFirestore get firestore => _firestore;
  static FirebaseStorage get storage => _storage;

  // Usuario actual
  static User? get currentUser => _auth.currentUser;
  static bool get isLoggedIn => _auth.currentUser != null;

  // Stream del estado de autenticación
  static Stream<User?> get authStateChanges => _auth.authStateChanges();

  // AUTENTICACIÓN
  
  /// Registrar usuario con email y contraseña
  static Future<UserCredential?> registerWithEmailPassword({
    required String email,
    required String password,
    required String displayName,
  }) async {
    try {
      final credential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      // Actualizar el nombre del usuario
      await credential.user?.updateDisplayName(displayName);
      
      // Crear documento del usuario en Firestore
      await _createUserDocument(credential.user!, displayName);
      
      log('Usuario registrado exitosamente: ${credential.user?.email}');
      return credential;
    } on FirebaseAuthException catch (e) {
      log('Error al registrar usuario: ${e.message}');
      throw _handleAuthException(e);
    } catch (e) {
      log('Error inesperado al registrar: $e');
      rethrow;
    }
  }

  /// Iniciar sesión con email y contraseña
  static Future<UserCredential?> signInWithEmailPassword({
    required String email,
    required String password,
  }) async {
    try {
      final credential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      log('Usuario autenticado exitosamente: ${credential.user?.email}');
      return credential;
    } on FirebaseAuthException catch (e) {
      log('Error al iniciar sesión: ${e.message}');
      throw _handleAuthException(e);
    } catch (e) {
      log('Error inesperado al iniciar sesión: $e');
      rethrow;
    }
  }

  /// Cerrar sesión
  static Future<void> signOut() async {
    try {
      await _auth.signOut();
      log('Usuario cerró sesión exitosamente');
    } catch (e) {
      log('Error al cerrar sesión: $e');
      rethrow;
    }
  }

  /// Restablecer contraseña
  static Future<void> resetPassword(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
      log('Email de restablecimiento enviado a: $email');
    } on FirebaseAuthException catch (e) {
      log('Error al enviar email de restablecimiento: ${e.message}');
      throw _handleAuthException(e);
    } catch (e) {
      log('Error inesperado al restablecer contraseña: $e');
      rethrow;
    }
  }

  // FIRESTORE - OPERACIONES CRUD

  /// Crear documento en una colección
  static Future<void> createDocument({
    required String collection,
    required String docId,
    required Map<String, dynamic> data,
  }) async {
    try {
      await _firestore.collection(collection).doc(docId).set({
        ...data,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });
      log('Documento creado en $collection/$docId');
    } catch (e) {
      log('Error al crear documento: $e');
      rethrow;
    }
  }

  /// Obtener documento por ID
  static Future<DocumentSnapshot> getDocument({
    required String collection,
    required String docId,
  }) async {
    try {
      final doc = await _firestore.collection(collection).doc(docId).get();
      log('Documento obtenido de $collection/$docId');
      return doc;
    } catch (e) {
      log('Error al obtener documento: $e');
      rethrow;
    }
  }

  /// Obtener colección con filtros opcionales
  static Future<QuerySnapshot> getCollection({
    required String collection,
    Query Function(Query)? queryBuilder,
  }) async {
    try {
      Query query = _firestore.collection(collection);
      if (queryBuilder != null) {
        query = queryBuilder(query);
      }
      final snapshot = await query.get();
      log('Colección obtenida: $collection (${snapshot.docs.length} documentos)');
      return snapshot;
    } catch (e) {
      log('Error al obtener colección: $e');
      rethrow;
    }
  }

  /// Actualizar documento
  static Future<void> updateDocument({
    required String collection,
    required String docId,
    required Map<String, dynamic> data,
  }) async {
    try {
      await _firestore.collection(collection).doc(docId).update({
        ...data,
        'updatedAt': FieldValue.serverTimestamp(),
      });
      log('Documento actualizado en $collection/$docId');
    } catch (e) {
      log('Error al actualizar documento: $e');
      rethrow;
    }
  }

  /// Eliminar documento
  static Future<void> deleteDocument({
    required String collection,
    required String docId,
  }) async {
    try {
      await _firestore.collection(collection).doc(docId).delete();
      log('Documento eliminado de $collection/$docId');
    } catch (e) {
      log('Error al eliminar documento: $e');
      rethrow;
    }
  }

  // MÉTODOS PRIVADOS

  /// Crear documento del usuario en Firestore
  static Future<void> _createUserDocument(User user, String displayName) async {
    await createDocument(
      collection: 'users',
      docId: user.uid,
      data: {
        'email': user.email,
        'displayName': displayName,
        'photoURL': user.photoURL,
        'emailVerified': user.emailVerified,
      },
    );
  }

  /// Manejar excepciones de autenticación
  static String _handleAuthException(FirebaseAuthException e) {
    switch (e.code) {
      case 'weak-password':
        return 'La contraseña es muy débil.';
      case 'email-already-in-use':
        return 'Ya existe una cuenta con este email.';
      case 'user-not-found':
        return 'No se encontró usuario con este email.';
      case 'wrong-password':
        return 'Contraseña incorrecta.';
      case 'invalid-email':
        return 'Email inválido.';
      case 'user-disabled':
        return 'Esta cuenta ha sido deshabilitada.';
      case 'too-many-requests':
        return 'Demasiados intentos. Intenta más tarde.';
      default:
        return e.message ?? 'Error de autenticación desconocido.';
    }
  }
}
