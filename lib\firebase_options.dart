// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyDWIW0ASD6GGocBsjYs_O1c245iH04yTTo',
    appId: '1:741892752866:web:YOUR_WEB_APP_ID',
    messagingSenderId: '741892752866',
    projectId: 'm-shop-58899',
    authDomain: 'm-shop-58899.firebaseapp.com',
    storageBucket: 'm-shop-58899.firebasestorage.app',
    measurementId: 'YOUR_MEASUREMENT_ID',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDWIW0ASD6GGocBsjYs_O1c245iH04yTTo',
    appId: '1:741892752866:android:68bc11c6dadc7f028d65bb',
    messagingSenderId: '741892752866',
    projectId: 'm-shop-58899',
    storageBucket: 'm-shop-58899.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDWIW0ASD6GGocBsjYs_O1c245iH04yTTo',
    appId: '1:741892752866:ios:YOUR_IOS_APP_ID',
    messagingSenderId: '741892752866',
    projectId: 'm-shop-58899',
    storageBucket: 'm-shop-58899.firebasestorage.app',
    iosBundleId: 'com.example.shop3m',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDWIW0ASD6GGocBsjYs_O1c245iH04yTTo',
    appId: '1:741892752866:macos:YOUR_MACOS_APP_ID',
    messagingSenderId: '741892752866',
    projectId: 'm-shop-58899',
    storageBucket: 'm-shop-58899.firebasestorage.app',
    iosBundleId: 'com.example.shop3m',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyDWIW0ASD6GGocBsjYs_O1c245iH04yTTo',
    appId: '1:741892752866:windows:YOUR_WINDOWS_APP_ID',
    messagingSenderId: '741892752866',
    projectId: 'm-shop-58899',
    storageBucket: 'm-shop-58899.firebasestorage.app',
  );
}
