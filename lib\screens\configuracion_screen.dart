import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../widgets/gradient_background.dart';
import '../services/firebase_service.dart';
import '../routes/app_routes.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:developer';

class ConfiguracionScreen extends StatefulWidget {
  const ConfiguracionScreen({super.key});

  @override
  State<ConfiguracionScreen> createState() => _ConfiguracionScreenState();
}

class _ConfiguracionScreenState extends State<ConfiguracionScreen> {
  // Configuraciones de apariencia
  bool modoOscuro = false;
  String idioma = "Español";
  String moneda = "COP";

  // Configuraciones de negocio
  bool notificacionesActivas = true;
  bool backupAutomatico = true;
  bool sincronizacionOffline = true;

  // Información de la app
  String versionApp = "Cargando...";
  String buildNumber = "Cargando...";

  // Estado de Firebase
  bool firebaseConectado = false;
  String usuarioActual = "No autenticado";

  @override
  void initState() {
    super.initState();
    _cargarPreferencias();
    _cargarInformacionApp();
    _verificarEstadoFirebase();
  }

  Future<void> _cargarPreferencias() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      // Configuraciones de apariencia
      modoOscuro = prefs.getBool("modoOscuro") ?? false;
      idioma = prefs.getString("idioma") ?? "Español";
      moneda = prefs.getString("moneda") ?? "COP";

      // Configuraciones de negocio
      notificacionesActivas = prefs.getBool("notificacionesActivas") ?? true;
      backupAutomatico = prefs.getBool("backupAutomatico") ?? true;
      sincronizacionOffline = prefs.getBool("sincronizacionOffline") ?? true;
    });
  }

  Future<void> _cargarInformacionApp() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      setState(() {
        versionApp = packageInfo.version;
        buildNumber = packageInfo.buildNumber;
      });
    } catch (e) {
      log('Error cargando información de la app: $e');
      setState(() {
        versionApp = "Error";
        buildNumber = "Error";
      });
    }
  }

  Future<void> _verificarEstadoFirebase() async {
    try {
      final user = FirebaseService.currentUser;
      setState(() {
        firebaseConectado = user != null;
        usuarioActual = user?.email ?? "No autenticado";
      });
    } catch (e) {
      log('Error verificando estado de Firebase: $e');
      setState(() {
        firebaseConectado = false;
        usuarioActual = "Error de conexión";
      });
    }
  }

  Future<void> _guardarPreferencia(String clave, dynamic valor) async {
    final prefs = await SharedPreferences.getInstance();
    if (valor is bool) {
      await prefs.setBool(clave, valor);
    } else if (valor is String) {
      await prefs.setString(clave, valor);
    }
    _cargarPreferencias();
  }

  Future<void> _cerrarSesion() async {
    try {
      await FirebaseService.signOut();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Sesión cerrada exitosamente'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pushNamedAndRemoveUntil(
          AppRoutes.auth,
          (route) => false,
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error al cerrar sesión: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _abrirUrl(String url) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        throw 'No se puede abrir $url';
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error al abrir enlace: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _mostrarDialogoAcercaDe() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Acerca de 3M Shop'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Versión: $versionApp'),
              Text('Build: $buildNumber'),
              const SizedBox(height: 16),
              const Text(
                '3M Shop es una aplicación de facturación moderna '
                'desarrollada con Flutter y Firebase.',
                style: TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 16),
              const Text(
                '© 2024 3M Shop. Todos los derechos reservados.',
                style: TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cerrar'),
            ),
          ],
        );
      },
    );
  }

  void _copiarInformacionSistema() {
    final info = '''
3M Shop - Información del Sistema

Versión: $versionApp
Build: $buildNumber
Firebase: ${firebaseConectado ? 'Conectado' : 'Desconectado'}
Usuario: $usuarioActual
Idioma: $idioma
Moneda: $moneda
Modo Oscuro: ${modoOscuro ? 'Activado' : 'Desactivado'}
Notificaciones: ${notificacionesActivas ? 'Activadas' : 'Desactivadas'}
Backup Automático: ${backupAutomatico ? 'Activado' : 'Desactivado'}
Sincronización Offline: ${sincronizacionOffline ? 'Activada' : 'Desactivada'}
''';

    Clipboard.setData(ClipboardData(text: info));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Información del sistema copiada al portapapeles'),
        backgroundColor: Colors.green,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("Configuración"),
        backgroundColor: Colors.indigo.shade600,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: _mostrarDialogoAcercaDe,
            tooltip: 'Acerca de',
          ),
          IconButton(
            icon: const Icon(Icons.copy),
            onPressed: _copiarInformacionSistema,
            tooltip: 'Copiar información del sistema',
          ),
        ],
      ),
      body: GradientBackground(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Header con información de Firebase
              _buildFirebaseStatusCard(),
              const SizedBox(height: 16),

              // Configuraciones de Apariencia
              _buildAparienciaCard(),
              const SizedBox(height: 16),

              // Configuraciones de Negocio
              _buildNegocioCard(),
              const SizedBox(height: 16),

              // Información de la App
              _buildInformacionCard(),
              const SizedBox(height: 16),

              // Herramientas de Desarrollo
              _buildDesarrolloCard(),
              const SizedBox(height: 16),

              // Acciones
              _buildAccionesCard(),
              const SizedBox(height: 16),

              // Enlaces útiles
              _buildEnlacesCard(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFirebaseStatusCard() {
    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      elevation: 8,
      shadowColor: firebaseConectado
          ? Colors.green.withValues(alpha: 0.3)
          : Colors.red.withValues(alpha: 0.3),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: LinearGradient(
            colors: firebaseConectado
                ? [const Color(0xFF4CAF50), const Color(0xFF2E7D32)]
                : [const Color(0xFFE53935), const Color(0xFFC62828)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          boxShadow: [
            BoxShadow(
              color: firebaseConectado
                  ? Colors.green.withValues(alpha: 0.2)
                  : Colors.red.withValues(alpha: 0.2),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Row(
          children: [
            Icon(
              firebaseConectado ? Icons.cloud_done : Icons.cloud_off,
              color: Colors.white,
              size: 32,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    firebaseConectado ? 'Firebase Conectado' : 'Firebase Desconectado',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    usuarioActual,
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            if (firebaseConectado)
              IconButton(
                icon: const Icon(Icons.refresh, color: Colors.white),
                onPressed: _verificarEstadoFirebase,
                tooltip: 'Actualizar estado',
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildAparienciaCard() {
    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      elevation: 4,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.indigo.shade50,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.palette, color: Colors.indigo.shade600),
                const SizedBox(width: 12),
                Text(
                  'Apariencia',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.indigo.shade600,
                  ),
                ),
              ],
            ),
          ),
          SwitchListTile(
            title: const Text("Modo Oscuro"),
            subtitle: const Text("Cambiar entre tema claro y oscuro"),
            secondary: const Icon(Icons.dark_mode),
            value: modoOscuro,
            onChanged: (value) => _guardarPreferencia("modoOscuro", value),
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.language),
            title: const Text("Idioma"),
            subtitle: const Text("Seleccionar idioma de la aplicación"),
            trailing: DropdownButton<String>(
              value: idioma,
              underline: const SizedBox(),
              items: ["Español", "Inglés"].map((String valor) {
                return DropdownMenuItem(value: valor, child: Text(valor));
              }).toList(),
              onChanged: (value) => _guardarPreferencia("idioma", value),
            ),
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.attach_money),
            title: const Text("Moneda"),
            subtitle: const Text("Moneda para mostrar precios"),
            trailing: DropdownButton<String>(
              value: moneda,
              underline: const SizedBox(),
              items: ["COP", "USD", "EUR"].map((String valor) {
                return DropdownMenuItem(value: valor, child: Text(valor));
              }).toList(),
              onChanged: (value) => _guardarPreferencia("moneda", value),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNegocioCard() {
    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      elevation: 4,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.orange.shade50,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.business, color: Colors.orange.shade600),
                const SizedBox(width: 12),
                Text(
                  'Configuración de Negocio',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.orange.shade600,
                  ),
                ),
              ],
            ),
          ),
          SwitchListTile(
            title: const Text("Notificaciones"),
            subtitle: const Text("Recibir notificaciones de la app"),
            secondary: const Icon(Icons.notifications),
            value: notificacionesActivas,
            onChanged: (value) => _guardarPreferencia("notificacionesActivas", value),
          ),
          const Divider(height: 1),
          SwitchListTile(
            title: const Text("Backup Automático"),
            subtitle: const Text("Respaldar datos automáticamente"),
            secondary: const Icon(Icons.backup),
            value: backupAutomatico,
            onChanged: (value) => _guardarPreferencia("backupAutomatico", value),
          ),
          const Divider(height: 1),
          SwitchListTile(
            title: const Text("Sincronización Offline"),
            subtitle: const Text("Sincronizar cuando vuelva la conexión"),
            secondary: const Icon(Icons.sync),
            value: sincronizacionOffline,
            onChanged: (value) => _guardarPreferencia("sincronizacionOffline", value),
          ),
        ],
      ),
    );
  }

  Widget _buildInformacionCard() {
    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      elevation: 4,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.info, color: Colors.blue.shade600),
                const SizedBox(width: 12),
                Text(
                  'Información de la App',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue.shade600,
                  ),
                ),
              ],
            ),
          ),
          ListTile(
            leading: const Icon(Icons.apps),
            title: const Text("Versión"),
            trailing: Text(versionApp),
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.build),
            title: const Text("Build"),
            trailing: Text(buildNumber),
          ),
          const Divider(height: 1),
          ListTile(
            leading: Icon(
              firebaseConectado ? Icons.cloud_done : Icons.cloud_off,
              color: firebaseConectado ? Colors.green : Colors.red,
            ),
            title: const Text("Estado de Firebase"),
            trailing: Text(
              firebaseConectado ? 'Conectado' : 'Desconectado',
              style: TextStyle(
                color: firebaseConectado ? Colors.green : Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAccionesCard() {
    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      elevation: 4,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.red.shade50,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.settings_applications, color: Colors.red.shade600),
                const SizedBox(width: 12),
                Text(
                  'Acciones',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.red.shade600,
                  ),
                ),
              ],
            ),
          ),
          ListTile(
            leading: const Icon(Icons.refresh, color: Colors.blue),
            title: const Text("Actualizar Estado Firebase"),
            subtitle: const Text("Verificar conexión y estado actual"),
            onTap: _verificarEstadoFirebase,
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.cloud, color: Colors.orange),
            title: const Text("Ir a Firebase Test"),
            subtitle: const Text("Probar funcionalidades de Firebase"),
            onTap: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Busca "Firebase Test" en el menú principal'),
                  backgroundColor: Colors.orange,
                ),
              );
            },
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.logout, color: Colors.red),
            title: const Text("Cerrar Sesión"),
            subtitle: const Text("Salir de la aplicación"),
            onTap: _cerrarSesion,
          ),
        ],
      ),
    );
  }

  Widget _buildEnlacesCard() {
    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      elevation: 4,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.green.shade50,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.link, color: Colors.green.shade600),
                const SizedBox(width: 12),
                Text(
                  'Enlaces Útiles',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.green.shade600,
                  ),
                ),
              ],
            ),
          ),
          ListTile(
            leading: const Icon(Icons.help, color: Colors.blue),
            title: const Text("Documentación"),
            subtitle: const Text("Guía de uso de Flutter y Firebase"),
            trailing: const Icon(Icons.open_in_new),
            onTap: () => _abrirUrl('https://docs.flutter.dev/'),
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.bug_report, color: Colors.orange),
            title: const Text("Reportar Problema"),
            subtitle: const Text("Enviar feedback por email"),
            trailing: const Icon(Icons.email),
            onTap: () => _enviarEmail(),
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.privacy_tip, color: Colors.purple),
            title: const Text("Acerca de la App"),
            subtitle: const Text("Información y términos de uso"),
            trailing: const Icon(Icons.info),
            onTap: () => _mostrarPoliticaPrivacidad(),
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.code, color: Colors.teal),
            title: const Text("Código Fuente"),
            subtitle: const Text("Ver el proyecto en GitHub"),
            trailing: const Icon(Icons.open_in_new),
            onTap: () => _abrirUrl('https://github.com/flutter/flutter'),
          ),
        ],
      ),
    );
  }

  Future<void> _enviarEmail() async {
    final String subject = Uri.encodeComponent('Feedback - 3M Shop App');
    final String body = Uri.encodeComponent('''
Hola,

Me gustaría reportar lo siguiente sobre la aplicación 3M Shop:

[Describe tu problema o sugerencia aquí]

---
Información del sistema:
Versión: $versionApp
Build: $buildNumber
Firebase: ${firebaseConectado ? 'Conectado' : 'Desconectado'}
Usuario: $usuarioActual

Gracias.
''');

    final String emailUrl = 'mailto:<EMAIL>?subject=$subject&body=$body';

    try {
      final uri = Uri.parse(emailUrl);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
      } else {
        // Fallback: copiar email al portapapeles
        await Clipboard.setData(const ClipboardData(text: '<EMAIL>'));
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Email copiado al portapapeles: <EMAIL>'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error al abrir email: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _mostrarPoliticaPrivacidad() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Acerca de 3M Shop'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Versión: $versionApp (Build: $buildNumber)',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                const Text(
                  'Descripción:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                const Text(
                  '3M Shop es una aplicación moderna de facturación desarrollada con Flutter y Firebase. '
                  'Permite gestionar clientes, módulos, facturas y generar reportes de manera eficiente.',
                ),
                const SizedBox(height: 16),
                const Text(
                  'Características:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                const Text(
                  '• Gestión de clientes y módulos\n'
                  '• Facturación electrónica\n'
                  '• Sincronización en la nube\n'
                  '• Reportes y estadísticas\n'
                  '• Interfaz moderna y responsive\n'
                  '• Backup automático',
                ),
                const SizedBox(height: 16),
                const Text(
                  'Tecnologías:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                const Text(
                  '• Flutter (Framework UI)\n'
                  '• Firebase (Backend)\n'
                  '• Dart (Lenguaje de programación)',
                ),
                const SizedBox(height: 16),
                const Text(
                  'Privacidad:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Tus datos están protegidos por Firebase Security Rules. '
                  'Solo tú tienes acceso a tu información de facturación. '
                  'No compartimos datos personales con terceros.',
                ),
                const SizedBox(height: 16),
                const Text(
                  '© 2024 3M Shop. Todos los derechos reservados.',
                  style: TextStyle(fontSize: 12, color: Colors.grey),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => _abrirUrl('https://firebase.google.com/support/privacy'),
              child: const Text('Política Firebase'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cerrar'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildDesarrolloCard() {
    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      elevation: 6,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: const LinearGradient(
            colors: [Color(0xFF607D8B), Color(0xFF455A64)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.developer_mode, color: Colors.white, size: 24),
                SizedBox(width: 12),
                Text(
                  'Herramientas de Desarrollo',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Botón de datos de prueba
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () {
                  Navigator.of(context).pushNamed('/datos-prueba');
                },
                icon: const Icon(Icons.data_usage, color: Colors.white),
                label: const Text(
                  'Gestionar Datos de Prueba',
                  style: TextStyle(color: Colors.white),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.white.withValues(alpha: 0.2),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),

            const SizedBox(height: 8),

            // Descripción
            const Text(
              'Genera datos de prueba realistas para probar todas las funcionalidades de la aplicación.',
              style: TextStyle(
                color: Colors.white70,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
