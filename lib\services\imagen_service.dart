import 'dart:io';
import 'dart:typed_data';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:image_picker/image_picker.dart';
import 'package:flutter/foundation.dart' show kIsWeb;

class ImagenService {
  static final FirebaseStorage _storage = FirebaseStorage.instance;
  static final ImagePicker _picker = ImagePicker();

  /// Seleccionar imagen desde galería o cámara
  static Future<XFile?> seleccionarImagen({
    ImageSource source = ImageSource.gallery,
    int maxWidth = 800,
    int maxHeight = 800,
    int imageQuality = 85,
  }) async {
    try {
      final XFile? imagen = await _picker.pickImage(
        source: source,
        maxWidth: maxWidth.toDouble(),
        maxHeight: maxHeight.toDouble(),
        imageQuality: imageQuality,
      );
      return imagen;
    } catch (e) {
      // Error seleccionando imagen: $e
      return null;
    }
  }

  /// Subir imagen a Firebase Storage
  static Future<String?> subirImagen({
    required XFile imagen,
    required String carpeta, // 'clientes' o 'modulos'
    required String id,
  }) async {
    try {
      // Crear referencia única
      final String fileName =
          '${id}_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final Reference ref = _storage.ref().child('$carpeta/$fileName');

      // Subir imagen
      UploadTask uploadTask;

      if (kIsWeb) {
        // Para web, usar bytes
        final Uint8List bytes = await imagen.readAsBytes();
        uploadTask = ref.putData(
          bytes,
          SettableMetadata(
            contentType: 'image/jpeg',
            customMetadata: {
              'uploaded_by': 'shop_3m_app',
              'upload_time': DateTime.now().toIso8601String(),
            },
          ),
        );
      } else {
        // Para móvil, usar archivo
        final File file = File(imagen.path);
        uploadTask = ref.putFile(
          file,
          SettableMetadata(
            contentType: 'image/jpeg',
            customMetadata: {
              'uploaded_by': 'shop_3m_app',
              'upload_time': DateTime.now().toIso8601String(),
            },
          ),
        );
      }

      // Esperar a que termine la subida
      final TaskSnapshot snapshot = await uploadTask;

      // Obtener URL de descarga
      final String downloadUrl = await snapshot.ref.getDownloadURL();

      // print('✅ Imagen subida exitosamente: $downloadUrl');
      return downloadUrl;
    } catch (e) {
      // print('❌ Error subiendo imagen: $e');
      return null;
    }
  }

  /// Eliminar imagen de Firebase Storage
  static Future<bool> eliminarImagen(String imageUrl) async {
    try {
      // Extraer la referencia de la URL
      final Reference ref = _storage.refFromURL(imageUrl);
      await ref.delete();
      // print('✅ Imagen eliminada exitosamente');
      return true;
    } catch (e) {
      // print('❌ Error eliminando imagen: $e');
      return false;
    }
  }

  /// Mostrar opciones para seleccionar imagen (galería o cámara)
  static Future<XFile?> mostrarOpcionesImagen() async {
    // En web solo galería está disponible
    if (kIsWeb) {
      return await seleccionarImagen(source: ImageSource.gallery);
    }

    // En móvil, se podría mostrar un diálogo para elegir
    // Por ahora, usamos galería por defecto
    return await seleccionarImagen(source: ImageSource.gallery);
  }

  /// Redimensionar imagen antes de subir (para optimización)
  static Future<XFile?> redimensionarImagen(XFile imagen) async {
    try {
      // Para implementación futura con paquetes como image
      // Por ahora, el ImagePicker ya redimensiona con los parámetros dados
      return imagen;
    } catch (e) {
      // print('Error redimensionando imagen: $e');
      return imagen;
    }
  }

  /// Validar que el archivo sea una imagen válida
  static bool esImagenValida(XFile archivo) {
    final String extension = archivo.path.toLowerCase().split('.').last;
    final List<String> extensionesValidas = [
      'jpg',
      'jpeg',
      'png',
      'gif',
      'webp'
    ];
    return extensionesValidas.contains(extension);
  }

  /// Obtener tamaño del archivo en MB
  static Future<double> obtenerTamanoArchivo(XFile archivo) async {
    try {
      final int bytes = await archivo.length();
      return bytes / (1024 * 1024); // Convertir a MB
    } catch (e) {
      // print('Error obteniendo tamaño del archivo: $e');
      return 0.0;
    }
  }

  /// Validar tamaño máximo del archivo (en MB)
  static Future<bool> validarTamanoMaximo(XFile archivo,
      {double maxMB = 5.0}) async {
    final double tamano = await obtenerTamanoArchivo(archivo);
    return tamano <= maxMB;
  }

  /// Proceso completo: seleccionar, validar y subir imagen
  static Future<String?> procesarYSubirImagen({
    required String carpeta,
    required String id,
    double maxMB = 5.0,
  }) async {
    try {
      // 1. Seleccionar imagen
      final XFile? imagen = await mostrarOpcionesImagen();
      if (imagen == null) return null;

      // 2. Validar que sea imagen
      if (!esImagenValida(imagen)) {
        // print('❌ Archivo no es una imagen válida');
        return null;
      }

      // 3. Validar tamaño
      if (!await validarTamanoMaximo(imagen, maxMB: maxMB)) {
        // print('❌ Imagen demasiado grande (máximo ${maxMB}MB)');
        return null;
      }

      // 4. Subir imagen
      final String? url = await subirImagen(
        imagen: imagen,
        carpeta: carpeta,
        id: id,
      );

      return url;
    } catch (e) {
      // print('❌ Error en proceso completo de imagen: $e');
      return null;
    }
  }
}
