import 'package:flutter/material.dart';

class GradientBackground extends StatelessWidget {
  final Widget child;

  const GradientBackground({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Colors.indigo, Colors.teal],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: const [
          BoxShadow(
            color: Color.fromARGB(130, 50, 50, 50), // Usando ARGB directamente
            blurRadius: 10,
            spreadRadius: 3,
            offset: Offset(4, 4),
          ),
        ],
      ),
      child: child,
    );
  }
}
