import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'dart:math' as math;
import 'dart:ui' as ui;

/// Heatmap para mostrar actividad temporal
class HeatmapChart extends StatefulWidget {
  final Map<DateTime, double> data;
  final Color lowColor;
  final Color highColor;
  final double cellSize;
  final int daysToShow;

  const HeatmapChart({
    super.key,
    required this.data,
    this.lowColor = Colors.grey,
    this.highColor = Colors.green,
    this.cellSize = 12.0,
    this.daysToShow = 365,
  });

  @override
  State<HeatmapChart> createState() => _HeatmapChartState();
}

class _HeatmapChartState extends State<HeatmapChart>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = CurvedAnimation(parent: _controller, curve: Curves.easeOutCubic);
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return CustomPaint(
          size: Size(
            (widget.cellSize + 2) * 53, // 53 semanas
            (widget.cellSize + 2) * 7,  // 7 días
          ),
          painter: HeatmapPainter(
            data: widget.data,
            lowColor: widget.lowColor,
            highColor: widget.highColor,
            cellSize: widget.cellSize,
            animation: _animation.value,
            daysToShow: widget.daysToShow,
          ),
        );
      },
    );
  }
}

class HeatmapPainter extends CustomPainter {
  final Map<DateTime, double> data;
  final Color lowColor;
  final Color highColor;
  final double cellSize;
  final double animation;
  final int daysToShow;

  HeatmapPainter({
    required this.data,
    required this.lowColor,
    required this.highColor,
    required this.cellSize,
    required this.animation,
    required this.daysToShow,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;
    final now = DateTime.now();
    final startDate = now.subtract(Duration(days: daysToShow));

    // Encontrar valores min y max para normalización
    final values = data.values.where((v) => v > 0).toList();
    if (values.isEmpty) return;
    
    final minValue = values.reduce(math.min);
    final maxValue = values.reduce(math.max);

    for (int week = 0; week < 53; week++) {
      for (int day = 0; day < 7; day++) {
        final date = startDate.add(Duration(days: week * 7 + day));
        if (date.isAfter(now)) continue;

        final value = data[DateTime(date.year, date.month, date.day)] ?? 0;
        final normalizedValue = maxValue > minValue 
            ? (value - minValue) / (maxValue - minValue)
            : 0.0;

        final color = Color.lerp(lowColor, highColor, normalizedValue) ?? lowColor;
        paint.color = color.withValues(alpha: animation);

        final rect = Rect.fromLTWH(
          week * (cellSize + 2),
          day * (cellSize + 2),
          cellSize,
          cellSize,
        );

        canvas.drawRRect(
          RRect.fromRectAndRadius(rect, const Radius.circular(2)),
          paint,
        );
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// Gráfico de radar para comparaciones multidimensionales
class RadarChart extends StatefulWidget {
  final List<RadarDataSet> dataSets;
  final List<String> labels;
  final double maxValue;
  final Color gridColor;
  final int gridLevels;

  const RadarChart({
    super.key,
    required this.dataSets,
    required this.labels,
    required this.maxValue,
    this.gridColor = Colors.grey,
    this.gridLevels = 5,
  });

  @override
  State<RadarChart> createState() => _RadarChartState();
}

class _RadarChartState extends State<RadarChart>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    _animation = CurvedAnimation(parent: _controller, curve: Curves.elasticOut);
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return CustomPaint(
          size: const Size(300, 300),
          painter: RadarPainter(
            dataSets: widget.dataSets,
            labels: widget.labels,
            maxValue: widget.maxValue,
            gridColor: widget.gridColor,
            gridLevels: widget.gridLevels,
            animation: _animation.value,
          ),
        );
      },
    );
  }
}

class RadarDataSet {
  final List<double> values;
  final Color color;
  final String label;
  final double strokeWidth;

  RadarDataSet({
    required this.values,
    required this.color,
    required this.label,
    this.strokeWidth = 2.0,
  });
}

class RadarPainter extends CustomPainter {
  final List<RadarDataSet> dataSets;
  final List<String> labels;
  final double maxValue;
  final Color gridColor;
  final int gridLevels;
  final double animation;

  RadarPainter({
    required this.dataSets,
    required this.labels,
    required this.maxValue,
    required this.gridColor,
    required this.gridLevels,
    required this.animation,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = math.min(size.width, size.height) / 2 - 40;
    final angleStep = 2 * math.pi / labels.length;

    // Dibujar grid
    _drawGrid(canvas, center, radius, angleStep);

    // Dibujar labels
    _drawLabels(canvas, center, radius, angleStep);

    // Dibujar datasets
    for (final dataSet in dataSets) {
      _drawDataSet(canvas, center, radius, angleStep, dataSet);
    }
  }

  void _drawGrid(Canvas canvas, Offset center, double radius, double angleStep) {
    final paint = Paint()
      ..color = gridColor.withValues(alpha: 0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;

    // Círculos concéntricos
    for (int i = 1; i <= gridLevels; i++) {
      final levelRadius = (radius / gridLevels) * i * animation;
      canvas.drawCircle(center, levelRadius, paint);
    }

    // Líneas radiales
    for (int i = 0; i < labels.length; i++) {
      final angle = i * angleStep - math.pi / 2;
      final endPoint = Offset(
        center.dx + radius * math.cos(angle) * animation,
        center.dy + radius * math.sin(angle) * animation,
      );
      canvas.drawLine(center, endPoint, paint);
    }
  }

  void _drawLabels(Canvas canvas, Offset center, double radius, double angleStep) {
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );

    for (int i = 0; i < labels.length; i++) {
      final angle = i * angleStep - math.pi / 2;
      final labelRadius = radius + 20;
      final labelPosition = Offset(
        center.dx + labelRadius * math.cos(angle) * animation,
        center.dy + labelRadius * math.sin(angle) * animation,
      );

      textPainter.text = TextSpan(
        text: labels[i],
        style: const TextStyle(
          color: Colors.black,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(
          labelPosition.dx - textPainter.width / 2,
          labelPosition.dy - textPainter.height / 2,
        ),
      );
    }
  }

  void _drawDataSet(Canvas canvas, Offset center, double radius, double angleStep, RadarDataSet dataSet) {
    final paint = Paint()
      ..color = dataSet.color
      ..style = PaintingStyle.stroke
      ..strokeWidth = dataSet.strokeWidth;

    final fillPaint = Paint()
      ..color = dataSet.color.withValues(alpha: 0.2)
      ..style = PaintingStyle.fill;

    final path = Path();
    final points = <Offset>[];

    for (int i = 0; i < dataSet.values.length; i++) {
      final angle = i * angleStep - math.pi / 2;
      final value = (dataSet.values[i] / maxValue).clamp(0.0, 1.0);
      final pointRadius = radius * value * animation;
      
      final point = Offset(
        center.dx + pointRadius * math.cos(angle),
        center.dy + pointRadius * math.sin(angle),
      );
      
      points.add(point);
      
      if (i == 0) {
        path.moveTo(point.dx, point.dy);
      } else {
        path.lineTo(point.dx, point.dy);
      }
    }
    
    path.close();

    // Dibujar área rellena
    canvas.drawPath(path, fillPaint);
    
    // Dibujar línea
    canvas.drawPath(path, paint);

    // Dibujar puntos
    final pointPaint = Paint()
      ..color = dataSet.color
      ..style = PaintingStyle.fill;

    for (final point in points) {
      canvas.drawCircle(point, 4, pointPaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// Gráfico Gauge para KPIs
class GaugeChart extends StatefulWidget {
  final double value;
  final double maxValue;
  final String title;
  final String unit;
  final Color color;
  final List<GaugeRange>? ranges;

  const GaugeChart({
    super.key,
    required this.value,
    required this.maxValue,
    required this.title,
    this.unit = '',
    this.color = Colors.blue,
    this.ranges,
  });

  @override
  State<GaugeChart> createState() => _GaugeChartState();
}

class _GaugeChartState extends State<GaugeChart>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    _animation = CurvedAnimation(parent: _controller, curve: Curves.elasticOut);
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CustomPaint(
              size: const Size(200, 120),
              painter: GaugePainter(
                value: widget.value,
                maxValue: widget.maxValue,
                color: widget.color,
                ranges: widget.ranges,
                animation: _animation.value,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              widget.title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              '${(widget.value * _animation.value).toStringAsFixed(1)}${widget.unit}',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: widget.color,
              ),
            ),
          ],
        );
      },
    );
  }
}

class GaugeRange {
  final double start;
  final double end;
  final Color color;

  GaugeRange({
    required this.start,
    required this.end,
    required this.color,
  });
}

class GaugePainter extends CustomPainter {
  final double value;
  final double maxValue;
  final Color color;
  final List<GaugeRange>? ranges;
  final double animation;

  GaugePainter({
    required this.value,
    required this.maxValue,
    required this.color,
    this.ranges,
    required this.animation,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height);
    final radius = size.width / 2 - 20;
    const startAngle = math.pi;
    const sweepAngle = math.pi;

    // Dibujar fondo del gauge
    final backgroundPaint = Paint()
      ..color = Colors.grey.shade300
      ..style = PaintingStyle.stroke
      ..strokeWidth = 20
      ..strokeCap = StrokeCap.round;

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      startAngle,
      sweepAngle,
      false,
      backgroundPaint,
    );

    // Dibujar rangos si existen
    if (ranges != null) {
      for (final range in ranges!) {
        final rangePaint = Paint()
          ..color = range.color
          ..style = PaintingStyle.stroke
          ..strokeWidth = 20
          ..strokeCap = StrokeCap.round;

        final rangeStartAngle = startAngle + (range.start / maxValue) * sweepAngle;
        final rangeSweepAngle = ((range.end - range.start) / maxValue) * sweepAngle * animation;

        canvas.drawArc(
          Rect.fromCircle(center: center, radius: radius),
          rangeStartAngle,
          rangeSweepAngle,
          false,
          rangePaint,
        );
      }
    } else {
      // Dibujar valor
      final valuePaint = Paint()
        ..color = color
        ..style = PaintingStyle.stroke
        ..strokeWidth = 20
        ..strokeCap = StrokeCap.round;

      final valueAngle = (value / maxValue) * sweepAngle * animation;

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        valueAngle,
        false,
        valuePaint,
      );
    }

    // Dibujar indicador
    final indicatorAngle = startAngle + (value / maxValue) * sweepAngle * animation;
    final indicatorEnd = Offset(
      center.dx + (radius - 10) * math.cos(indicatorAngle),
      center.dy + (radius - 10) * math.sin(indicatorAngle),
    );

    final indicatorPaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3
      ..strokeCap = StrokeCap.round;

    canvas.drawLine(center, indicatorEnd, indicatorPaint);

    // Dibujar centro
    final centerPaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.fill;

    canvas.drawCircle(center, 8, centerPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// Treemap para visualizar jerarquías
class TreemapChart extends StatefulWidget {
  final List<TreemapData> data;
  final double width;
  final double height;

  const TreemapChart({
    super.key,
    required this.data,
    required this.width,
    required this.height,
  });

  @override
  State<TreemapChart> createState() => _TreemapChartState();
}

class _TreemapChartState extends State<TreemapChart>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = CurvedAnimation(parent: _controller, curve: Curves.easeOutCubic);
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return CustomPaint(
          size: Size(widget.width, widget.height),
          painter: TreemapPainter(
            data: widget.data,
            animation: _animation.value,
          ),
        );
      },
    );
  }
}

class TreemapData {
  final String label;
  final double value;
  final Color color;

  TreemapData({
    required this.label,
    required this.value,
    required this.color,
  });
}

class TreemapPainter extends CustomPainter {
  final List<TreemapData> data;
  final double animation;

  TreemapPainter({
    required this.data,
    required this.animation,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (data.isEmpty) return;

    final totalValue = data.fold(0.0, (sum, item) => sum + item.value);
    final rectangles = _calculateRectangles(size, totalValue);

    for (int i = 0; i < data.length && i < rectangles.length; i++) {
      final rect = rectangles[i];
      final item = data[i];

      // Animar el tamaño del rectángulo
      final animatedRect = Rect.fromLTWH(
        rect.left,
        rect.top,
        rect.width * animation,
        rect.height * animation,
      );

      // Dibujar rectángulo
      final paint = Paint()
        ..color = item.color.withValues(alpha: 0.8)
        ..style = PaintingStyle.fill;

      canvas.drawRRect(
        RRect.fromRectAndRadius(animatedRect, const Radius.circular(4)),
        paint,
      );

      // Dibujar borde
      final borderPaint = Paint()
        ..color = Colors.white
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2;

      canvas.drawRRect(
        RRect.fromRectAndRadius(animatedRect, const Radius.circular(4)),
        borderPaint,
      );

      // Dibujar texto si hay espacio
      if (animatedRect.width > 60 && animatedRect.height > 30) {
        final textPainter = TextPainter(
          text: TextSpan(
            text: item.label,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
          textDirection: TextDirection.ltr,
          textAlign: TextAlign.center,
        );

        textPainter.layout(maxWidth: animatedRect.width - 8);
        
        final textOffset = Offset(
          animatedRect.left + (animatedRect.width - textPainter.width) / 2,
          animatedRect.top + (animatedRect.height - textPainter.height) / 2,
        );

        textPainter.paint(canvas, textOffset);
      }
    }
  }

  List<Rect> _calculateRectangles(Size size, double totalValue) {
    final rectangles = <Rect>[];
    double currentX = 0;
    double currentY = 0;
    double remainingWidth = size.width;
    double remainingHeight = size.height;

    for (final item in data) {
      final ratio = item.value / totalValue;
      final area = size.width * size.height * ratio;
      
      double width, height;
      
      if (remainingWidth > remainingHeight) {
        width = area / remainingHeight;
        height = remainingHeight;
        
        if (width > remainingWidth) {
          width = remainingWidth;
          height = area / width;
        }
      } else {
        height = area / remainingWidth;
        width = remainingWidth;
        
        if (height > remainingHeight) {
          height = remainingHeight;
          width = area / height;
        }
      }

      rectangles.add(Rect.fromLTWH(currentX, currentY, width, height));

      if (remainingWidth > remainingHeight) {
        currentX += width;
        remainingWidth -= width;
      } else {
        currentY += height;
        remainingHeight -= height;
      }
    }

    return rectangles;
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
