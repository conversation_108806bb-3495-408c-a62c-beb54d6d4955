import 'package:flutter/material.dart';
import 'dart:math' as math;

/// Sistema de transiciones avanzadas con efectos cinematográficos
class AdvancedPageTransition<T> extends PageRouteBuilder<T> {
  final Widget child;
  final TransitionType type;
  final Duration duration;
  final Curve curve;

  AdvancedPageTransition({
    required this.child,
    this.type = TransitionType.slideFromRight,
    this.duration = const Duration(milliseconds: 400),
    this.curve = Curves.easeInOutCubic,
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => child,
          transitionDuration: duration,
          reverseTransitionDuration: duration,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return _buildTransition(type, animation, secondaryAnimation, child, curve);
          },
        );

  static Widget _buildTransition(
    TransitionType type,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
    Curve curve,
  ) {
    final curvedAnimation = CurvedAnimation(parent: animation, curve: curve);

    switch (type) {
      case TransitionType.morphing:
        return _buildMorphingTransition(curvedAnimation, secondaryAnimation, child);
      case TransitionType.liquid:
        return _buildLiquidTransition(curvedAnimation, child);
      case TransitionType.particle:
        return _buildParticleTransition(curvedAnimation, child);
      case TransitionType.fold:
        return _buildFoldTransition(curvedAnimation, child);
      case TransitionType.cube:
        return _buildCubeTransition(curvedAnimation, child);
      case TransitionType.ripple:
        return _buildRippleTransition(curvedAnimation, child);
      default:
        return _buildSlideTransition(curvedAnimation, child);
    }
  }

  static Widget _buildMorphingTransition(
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    return AnimatedBuilder(
      animation: animation,
      builder: (context, child) {
        return Transform(
          alignment: Alignment.center,
          transform: Matrix4.identity()
            ..setEntry(3, 2, 0.001)
            ..rotateY(animation.value * math.pi / 2),
          child: animation.value <= 0.5
              ? Container()
              : Transform(
                  alignment: Alignment.center,
                  transform: Matrix4.identity()
                    ..setEntry(3, 2, 0.001)
                    ..rotateY((1 - animation.value) * math.pi / 2),
                  child: child,
                ),
        );
      },
      child: child,
    );
  }

  static Widget _buildLiquidTransition(Animation<double> animation, Widget child) {
    return ClipPath(
      clipper: LiquidClipper(animation.value),
      child: child,
    );
  }

  static Widget _buildParticleTransition(Animation<double> animation, Widget child) {
    return Stack(
      children: [
        Opacity(
          opacity: animation.value,
          child: child,
        ),
        if (animation.value < 1.0)
          Positioned.fill(
            child: CustomPaint(
              painter: ParticleTransitionPainter(animation.value),
            ),
          ),
      ],
    );
  }

  static Widget _buildFoldTransition(Animation<double> animation, Widget child) {
    return Transform(
      alignment: Alignment.centerLeft,
      transform: Matrix4.identity()
        ..setEntry(3, 2, 0.001)
        ..rotateY(-animation.value * math.pi / 2),
      child: child,
    );
  }

  static Widget _buildCubeTransition(Animation<double> animation, Widget child) {
    return Transform(
      alignment: Alignment.center,
      transform: Matrix4.identity()
        ..setEntry(3, 2, 0.001)
        ..translate(0.0, 0.0, -200 * (1 - animation.value))
        ..rotateX(animation.value * math.pi / 4),
      child: child,
    );
  }

  static Widget _buildRippleTransition(Animation<double> animation, Widget child) {
    return ClipPath(
      clipper: RippleClipper(animation.value),
      child: child,
    );
  }

  static Widget _buildSlideTransition(Animation<double> animation, Widget child) {
    return SlideTransition(
      position: Tween<Offset>(
        begin: const Offset(1.0, 0.0),
        end: Offset.zero,
      ).animate(animation),
      child: child,
    );
  }
}

enum TransitionType {
  slideFromRight,
  morphing,
  liquid,
  particle,
  fold,
  cube,
  ripple,
}

/// Clipper para efecto líquido
class LiquidClipper extends CustomClipper<Path> {
  final double progress;

  LiquidClipper(this.progress);

  @override
  Path getClip(Size size) {
    final path = Path();
    
    if (progress <= 0) return path;
    if (progress >= 1) {
      path.addRect(Rect.fromLTWH(0, 0, size.width, size.height));
      return path;
    }

    final waveHeight = 20.0;
    final waveLength = size.width / 3;
    final centerY = size.height * progress;

    path.moveTo(0, centerY);

    for (double x = 0; x <= size.width; x += 1) {
      final waveY = math.sin((x / waveLength) * 2 * math.pi) * waveHeight * (1 - progress);
      path.lineTo(x, centerY + waveY);
    }

    path.lineTo(size.width, size.height);
    path.lineTo(0, size.height);
    path.close();

    return path;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) => true;
}

/// Clipper para efecto ripple
class RippleClipper extends CustomClipper<Path> {
  final double progress;

  RippleClipper(this.progress);

  @override
  Path getClip(Size size) {
    final path = Path();
    
    if (progress <= 0) return path;
    if (progress >= 1) {
      path.addRect(Rect.fromLTWH(0, 0, size.width, size.height));
      return path;
    }

    final center = Offset(size.width / 2, size.height / 2);
    final maxRadius = math.sqrt(size.width * size.width + size.height * size.height) / 2;
    final radius = maxRadius * progress;

    path.addOval(Rect.fromCircle(center: center, radius: radius));
    return path;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) => true;
}

/// Painter para efecto de partículas
class ParticleTransitionPainter extends CustomPainter {
  final double progress;
  final List<Particle> particles;

  ParticleTransitionPainter(this.progress) : particles = _generateParticles();

  static List<Particle> _generateParticles() {
    final random = math.Random();
    return List.generate(50, (index) {
      return Particle(
        x: random.nextDouble(),
        y: random.nextDouble(),
        size: random.nextDouble() * 4 + 1,
        speed: random.nextDouble() * 2 + 1,
        color: Colors.primaries[random.nextInt(Colors.primaries.length)],
      );
    });
  }

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    for (final particle in particles) {
      final x = particle.x * size.width;
      final y = particle.y * size.height + (progress * particle.speed * size.height);
      
      paint.color = particle.color.withValues(alpha: 1 - progress);
      canvas.drawCircle(Offset(x, y % size.height), particle.size, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class Particle {
  final double x;
  final double y;
  final double size;
  final double speed;
  final Color color;

  Particle({
    required this.x,
    required this.y,
    required this.size,
    required this.speed,
    required this.color,
  });
}

/// Widget para shared element transitions
class SharedElementTransition extends StatelessWidget {
  final String tag;
  final Widget child;
  final VoidCallback? onTap;

  const SharedElementTransition({
    super.key,
    required this.tag,
    required this.child,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Hero(
      tag: tag,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          child: child,
        ),
      ),
    );
  }
}

/// Widget para transiciones de morphing entre elementos
class MorphingContainer extends StatefulWidget {
  final Widget child;
  final Duration duration;
  final Curve curve;

  const MorphingContainer({
    super.key,
    required this.child,
    this.duration = const Duration(milliseconds: 300),
    this.curve = Curves.easeInOut,
  });

  @override
  State<MorphingContainer> createState() => _MorphingContainerState();
}

class _MorphingContainerState extends State<MorphingContainer>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  Widget? _previousChild;
  Widget? _currentChild;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(duration: widget.duration, vsync: this);
    _currentChild = widget.child;
  }

  @override
  void didUpdateWidget(MorphingContainer oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.child != oldWidget.child) {
      _previousChild = oldWidget.child;
      _currentChild = widget.child;
      _controller.forward(from: 0);
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        if (_previousChild == null) {
          return _currentChild!;
        }

        final animation = CurvedAnimation(parent: _controller, curve: widget.curve);
        
        return Stack(
          children: [
            Opacity(
              opacity: 1 - animation.value,
              child: Transform.scale(
                scale: 1 - (animation.value * 0.1),
                child: _previousChild,
              ),
            ),
            Opacity(
              opacity: animation.value,
              child: Transform.scale(
                scale: 0.9 + (animation.value * 0.1),
                child: _currentChild,
              ),
            ),
          ],
        );
      },
    );
  }
}

/// Extension para navegación con transiciones avanzadas
extension AdvancedNavigation on BuildContext {
  Future<T?> pushWithAdvancedTransition<T>(
    Widget page, {
    TransitionType type = TransitionType.slideFromRight,
    Duration duration = const Duration(milliseconds: 400),
    Curve curve = Curves.easeInOutCubic,
  }) {
    return Navigator.of(this).push<T>(
      AdvancedPageTransition<T>(
        child: page,
        type: type,
        duration: duration,
        curve: curve,
      ),
    );
  }
}

/// Widget para loading skeleton animado
class AnimatedSkeleton extends StatefulWidget {
  final double width;
  final double height;
  final BorderRadius? borderRadius;

  const AnimatedSkeleton({
    super.key,
    required this.width,
    required this.height,
    this.borderRadius,
  });

  @override
  State<AnimatedSkeleton> createState() => _AnimatedSkeletonState();
}

class _AnimatedSkeletonState extends State<AnimatedSkeleton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat();
    
    _animation = Tween<double>(begin: -1, end: 2).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          width: widget.width,
          height: widget.height,
          decoration: BoxDecoration(
            borderRadius: widget.borderRadius ?? BorderRadius.circular(4),
            gradient: LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: [
                Colors.grey.shade300,
                Colors.grey.shade100,
                Colors.grey.shade300,
              ],
              stops: [
                math.max(0, _animation.value - 0.3),
                _animation.value,
                math.min(1, _animation.value + 0.3),
              ],
            ),
          ),
        );
      },
    );
  }
}
