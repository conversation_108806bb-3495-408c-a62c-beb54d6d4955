import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math' as math;

/// Dashboard reorganizable con drag & drop
class ReorganizableDashboard extends StatefulWidget {
  final List<DashboardWidget> widgets;
  final Function(List<DashboardWidget>)? onReorder;
  final int crossAxisCount;
  final double childAspectRatio;
  final EdgeInsetsGeometry? padding;

  const ReorganizableDashboard({
    super.key,
    required this.widgets,
    this.onReorder,
    this.crossAxisCount = 2,
    this.childAspectRatio = 1.0,
    this.padding,
  });

  @override
  State<ReorganizableDashboard> createState() => _ReorganizableDashboardState();
}

class _ReorganizableDashboardState extends State<ReorganizableDashboard>
    with TickerProviderStateMixin {
  late List<DashboardWidget> _widgets;
  int? _draggedIndex;
  int? _hoveredIndex;
  late AnimationController _dragController;
  late Animation<double> _dragAnimation;

  @override
  void initState() {
    super.initState();
    _widgets = List.from(widget.widgets);
    
    _dragController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _dragAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(parent: _dragController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _dragController.dispose();
    super.dispose();
  }

  void _onReorder(int oldIndex, int newIndex) {
    setState(() {
      if (newIndex > oldIndex) {
        newIndex -= 1;
      }
      final item = _widgets.removeAt(oldIndex);
      _widgets.insert(newIndex, item);
    });
    
    widget.onReorder?.call(_widgets);
    HapticFeedback.mediumImpact();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: widget.padding ?? const EdgeInsets.all(16),
      child: ReorderableGridView(
        crossAxisCount: widget.crossAxisCount,
        childAspectRatio: widget.childAspectRatio,
        onReorder: _onReorder,
        children: _widgets.asMap().entries.map((entry) {
          final index = entry.key;
          final dashboardWidget = entry.value;
          
          return _buildDashboardItem(dashboardWidget, index);
        }).toList(),
      ),
    );
  }

  Widget _buildDashboardItem(DashboardWidget dashboardWidget, int index) {
    final isBeingDragged = _draggedIndex == index;
    final isHovered = _hoveredIndex == index;

    return AnimatedBuilder(
      animation: _dragAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: isBeingDragged ? _dragAnimation.value : 1.0,
          child: Container(
            key: ValueKey(dashboardWidget.id),
            margin: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: isBeingDragged || isHovered
                      ? Colors.blue.withValues(alpha: 0.3)
                      : Colors.black.withValues(alpha: 0.1),
                  blurRadius: isBeingDragged ? 20 : 8,
                  spreadRadius: isBeingDragged ? 2 : 0,
                  offset: Offset(0, isBeingDragged ? 8 : 2),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: Stack(
                children: [
                  dashboardWidget.child,
                  
                  // Overlay para drag handle
                  Positioned(
                    top: 8,
                    right: 8,
                    child: GestureDetector(
                      onTapDown: (_) {
                        setState(() => _draggedIndex = index);
                        _dragController.forward();
                        HapticFeedback.selectionClick();
                      },
                      onTapUp: (_) {
                        setState(() => _draggedIndex = null);
                        _dragController.reverse();
                      },
                      onTapCancel: () {
                        setState(() => _draggedIndex = null);
                        _dragController.reverse();
                      },
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          Icons.drag_handle,
                          size: 16,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Widget personalizable para el dashboard
class DashboardWidget {
  final String id;
  final String title;
  final Widget child;
  final DashboardWidgetSize size;
  final Color? color;
  final bool isResizable;

  DashboardWidget({
    required this.id,
    required this.title,
    required this.child,
    this.size = DashboardWidgetSize.medium,
    this.color,
    this.isResizable = false,
  });
}

enum DashboardWidgetSize {
  small,
  medium,
  large,
  extraLarge,
}

/// GridView reorganizable personalizado
class ReorderableGridView extends StatefulWidget {
  final List<Widget> children;
  final Function(int, int) onReorder;
  final int crossAxisCount;
  final double childAspectRatio;

  const ReorderableGridView({
    super.key,
    required this.children,
    required this.onReorder,
    this.crossAxisCount = 2,
    this.childAspectRatio = 1.0,
  });

  @override
  State<ReorderableGridView> createState() => _ReorderableGridViewState();
}

class _ReorderableGridViewState extends State<ReorderableGridView> {
  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: widget.crossAxisCount,
        childAspectRatio: widget.childAspectRatio,
      ),
      itemCount: widget.children.length,
      itemBuilder: (context, index) {
        return LongPressDraggable<int>(
          data: index,
          feedback: Material(
            color: Colors.transparent,
            child: Transform.scale(
              scale: 1.1,
              child: Opacity(
                opacity: 0.8,
                child: widget.children[index],
              ),
            ),
          ),
          childWhenDragging: Opacity(
            opacity: 0.3,
            child: widget.children[index],
          ),
          child: DragTarget<int>(
            onAccept: (draggedIndex) {
              widget.onReorder(draggedIndex, index);
            },
            builder: (context, candidateData, rejectedData) {
              return widget.children[index];
            },
          ),
        );
      },
    );
  }
}

/// Widget de estadística personalizable
class CustomStatWidget extends StatefulWidget {
  final String title;
  final String value;
  final String? subtitle;
  final IconData icon;
  final Color color;
  final List<double>? trendData;
  final bool showTrend;

  const CustomStatWidget({
    super.key,
    required this.title,
    required this.value,
    this.subtitle,
    required this.icon,
    required this.color,
    this.trendData,
    this.showTrend = false,
  });

  @override
  State<CustomStatWidget> createState() => _CustomStatWidgetState();
}

class _CustomStatWidgetState extends State<CustomStatWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.elasticOut),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeIn),
    );

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: widget.color.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: widget.color.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          widget.icon,
                          color: widget.color,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          widget.title,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Colors.grey,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    widget.value,
                    style: TextStyle(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                      color: widget.color,
                    ),
                  ),
                  if (widget.subtitle != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      widget.subtitle!,
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                  if (widget.showTrend && widget.trendData != null) ...[
                    const SizedBox(height: 12),
                    SizedBox(
                      height: 40,
                      child: CustomPaint(
                        painter: MiniTrendPainter(
                          data: widget.trendData!,
                          color: widget.color,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Painter para mini gráfico de tendencia
class MiniTrendPainter extends CustomPainter {
  final List<double> data;
  final Color color;

  MiniTrendPainter({required this.data, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    if (data.length < 2) return;

    final paint = Paint()
      ..color = color.withValues(alpha: 0.6)
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final path = Path();
    final maxValue = data.reduce(math.max);
    final minValue = data.reduce(math.min);
    final range = maxValue - minValue;

    if (range == 0) return;

    for (int i = 0; i < data.length; i++) {
      final x = (i / (data.length - 1)) * size.width;
      final y = size.height - ((data[i] - minValue) / range) * size.height;

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }

    canvas.drawPath(path, paint);

    // Área bajo la curva
    final fillPath = Path.from(path);
    fillPath.lineTo(size.width, size.height);
    fillPath.lineTo(0, size.height);
    fillPath.close();

    final fillPaint = Paint()
      ..color = color.withValues(alpha: 0.1)
      ..style = PaintingStyle.fill;

    canvas.drawPath(fillPath, fillPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// Factory para crear widgets de dashboard comunes
class DashboardWidgetFactory {
  static DashboardWidget createStatWidget({
    required String id,
    required String title,
    required String value,
    String? subtitle,
    required IconData icon,
    required Color color,
    List<double>? trendData,
    bool showTrend = false,
  }) {
    return DashboardWidget(
      id: id,
      title: title,
      child: CustomStatWidget(
        title: title,
        value: value,
        subtitle: subtitle,
        icon: icon,
        color: color,
        trendData: trendData,
        showTrend: showTrend,
      ),
    );
  }

  static DashboardWidget createChartWidget({
    required String id,
    required String title,
    required Widget chart,
    Color? color,
  }) {
    return DashboardWidget(
      id: id,
      title: title,
      size: DashboardWidgetSize.large,
      color: color,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Expanded(child: chart),
          ],
        ),
      ),
    );
  }

  static DashboardWidget createActionWidget({
    required String id,
    required String title,
    required IconData icon,
    required VoidCallback onTap,
    Color? color,
  }) {
    return DashboardWidget(
      id: id,
      title: title,
      color: color,
      child: Material(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  size: 32,
                  color: color ?? Colors.blue,
                ),
                const SizedBox(height: 8),
                Text(
                  title,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: color ?? Colors.blue,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
