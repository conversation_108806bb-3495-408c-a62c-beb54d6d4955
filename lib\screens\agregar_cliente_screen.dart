import 'package:flutter/material.dart';
import '../widgets/gradient_background.dart'; // ✅ Importar fondo degradado
import '../widgets/selector_imagen.dart';
import '../services/cliente_service.dart';
import '../models/cliente_model.dart';

class AgregarClienteScreen extends StatefulWidget {
  const AgregarClienteScreen({super.key});

  @override
  State<AgregarClienteScreen> createState() => _AgregarClienteScreenState();
}

class _AgregarClienteScreenState extends State<AgregarClienteScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController nombreController = TextEditingController();
  final TextEditingController telefonoController = TextEditingController();
  final TextEditingController emailController = TextEditingController();

  // Variables para manejar errores en tiempo real
  String? nombreError;
  String? telefonoError;
  String? emailError;
  bool _isLoading = false;

  // Variable para imagen de perfil
  String? _imagenUrl;

  // Métodos de validación en tiempo real
  void _validateNombre(String value) {
    setState(() {
      if (value.isEmpty) {
        nombreError = 'El nombre es requerido';
      } else if (!Cliente.isValidNombre(value)) {
        nombreError = 'Nombre debe tener al menos 2 caracteres';
      } else {
        nombreError = null;
      }
    });
  }

  void _validateTelefono(String value) {
    setState(() {
      if (value.isEmpty) {
        telefonoError = 'El teléfono es requerido';
      } else if (!Cliente.isValidTelefono(value)) {
        telefonoError = 'Formato de teléfono inválido';
      } else {
        telefonoError = null;
      }
    });
  }

  void _validateEmail(String value) {
    setState(() {
      if (value.isEmpty) {
        emailError = 'El email es requerido';
      } else if (!Cliente.isValidEmail(value)) {
        emailError = 'Formato de email inválido';
      } else {
        emailError = null;
      }
    });
  }

  Future<void> _guardarCliente() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final nuevoCliente = Cliente(
        idCliente: DateTime.now().millisecondsSinceEpoch.toString(),
        nombre: nombreController.text.trim(),
        telefono: telefonoController.text.trim(),
        email: emailController.text.trim(),
        imagenUrl: _imagenUrl,
      );

      await ClienteService.insertarCliente(nuevoCliente);
      _mostrarMensaje("Cliente agregado correctamente!");
      if (!mounted) return;
      Navigator.pop(context);
    } catch (e) {
      _mostrarMensaje("Error al agregar cliente: $e");
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _mostrarMensaje(String mensaje) {
    ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(mensaje), duration: const Duration(seconds: 2)));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text("Agregar Cliente")),
      body: GradientBackground(
        // ✅ Aplicar fondo degradado
        child: Center(
          // ✅ Centrar el formulario
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Card(
              // ✅ Tarjeta con sombra y bordes redondeados
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              elevation: 6,
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Selector de imagen de perfil
                      Center(
                        child: SelectorImagen(
                          imagenUrl: _imagenUrl,
                          nombre: nombreController.text.isNotEmpty
                              ? nombreController.text
                              : 'Nuevo Cliente',
                          carpeta: 'clientes',
                          id: DateTime.now().millisecondsSinceEpoch.toString(),
                          onImagenCambiada: (nuevaUrl) {
                            setState(() {
                              _imagenUrl = nuevaUrl;
                            });
                          },
                          size: 100,
                          esCircular: true,
                          iconoPorDefecto: Icons.person,
                        ),
                      ),
                      const SizedBox(height: 24),

                      TextFormField(
                        controller: nombreController,
                        decoration: InputDecoration(
                          labelText: "Nombre",
                          errorText: nombreError,
                          prefixIcon: const Icon(Icons.person),
                          border: const OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'El nombre es requerido';
                          }
                          if (!Cliente.isValidNombre(value)) {
                            return 'Nombre debe tener al menos 2 caracteres';
                          }
                          return null;
                        },
                        onChanged: _validateNombre,
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: telefonoController,
                        decoration: InputDecoration(
                          labelText: "Teléfono",
                          errorText: telefonoError,
                          prefixIcon: const Icon(Icons.phone),
                          border: const OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.phone,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'El teléfono es requerido';
                          }
                          if (!Cliente.isValidTelefono(value)) {
                            return 'Formato de teléfono inválido';
                          }
                          return null;
                        },
                        onChanged: _validateTelefono,
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: emailController,
                        decoration: InputDecoration(
                          labelText: "Email",
                          errorText: emailError,
                          prefixIcon: const Icon(Icons.email),
                          border: const OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.emailAddress,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'El email es requerido';
                          }
                          if (!Cliente.isValidEmail(value)) {
                            return 'Formato de email inválido';
                          }
                          return null;
                        },
                        onChanged: _validateEmail,
                      ),
                      const SizedBox(height: 20),
                      SizedBox(
                        width: double.infinity,
                        child: FilledButton(
                          onPressed: _isLoading ? null : _guardarCliente,
                          style: FilledButton.styleFrom(
                            backgroundColor:
                                Theme.of(context).colorScheme.primary,
                            elevation: 4,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                          ),
                          child: _isLoading
                              ? const SizedBox(
                                  height: 20,
                                  width: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.white),
                                  ),
                                )
                              : const Text("Guardar Cliente"),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
