import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../widgets/gradient_background.dart';
import '../widgets/selector_imagen.dart';
import '../widgets/advanced_visual_effects.dart';
import '../widgets/theme_toggle_button.dart';
import '../services/cliente_service.dart';
import '../models/cliente_model.dart';
import 'dart:math' as math;

class AgregarClienteScreen extends StatefulWidget {
  const AgregarClienteScreen({super.key});

  @override
  State<AgregarClienteScreen> createState() => _AgregarClienteScreenState();
}

class _AgregarClienteScreenState extends State<AgregarClienteScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController nombreController = TextEditingController();
  final TextEditingController telefonoController = TextEditingController();
  final TextEditingController emailController = TextEditingController();

  // Controladores de animación
  late AnimationController _stepController;
  late AnimationController _slideController;
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late AnimationController _progressController;
  late AnimationController _successController;

  // Animaciones
  late Animation<double> _stepAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _progressAnimation;
  late Animation<double> _successAnimation;

  // Variables para manejar errores en tiempo real
  String? nombreError;
  String? telefonoError;
  String? emailError;
  bool _isLoading = false;
  bool _showSuccess = false;

  // Variable para imagen de perfil
  String? _imagenUrl;

  // Sistema de pasos
  int _currentStep = 0;
  final int _totalSteps = 4;

  // Focus nodes
  final FocusNode _nombreFocus = FocusNode();
  final FocusNode _telefonoFocus = FocusNode();
  final FocusNode _emailFocus = FocusNode();

  // Validación de pasos
  bool _step1Valid = false; // Imagen
  bool _step2Valid = false; // Nombre
  bool _step3Valid = false; // Teléfono
  bool _step4Valid = false; // Email

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startEntryAnimations();
  }

  void _initializeAnimations() {
    // Controladores de animación
    _stepController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _progressController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _successController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    // Animaciones
    _stepAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _stepController,
      curve: Curves.easeInOutCubic,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(1, 0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeInOut,
    ));

    _successAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _successController,
      curve: Curves.elasticOut,
    ));
  }

  void _startEntryAnimations() {
    Future.delayed(const Duration(milliseconds: 100), () {
      _fadeController.forward();
    });

    Future.delayed(const Duration(milliseconds: 200), () {
      _scaleController.forward();
    });

    Future.delayed(const Duration(milliseconds: 300), () {
      _slideController.forward();
    });

    Future.delayed(const Duration(milliseconds: 400), () {
      _stepController.forward();
    });
  }

  @override
  void dispose() {
    // Dispose controllers
    nombreController.dispose();
    telefonoController.dispose();
    emailController.dispose();

    // Dispose focus nodes
    _nombreFocus.dispose();
    _telefonoFocus.dispose();
    _emailFocus.dispose();

    // Dispose animation controllers
    _stepController.dispose();
    _slideController.dispose();
    _fadeController.dispose();
    _scaleController.dispose();
    _progressController.dispose();
    _successController.dispose();

    super.dispose();
  }

  // Métodos de navegación de pasos
  void _nextStep() {
    if (_currentStep < _totalSteps - 1) {
      setState(() {
        _currentStep++;
      });
      _animateStepTransition();
      HapticFeedback.selectionClick();
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      _animateStepTransition();
      HapticFeedback.selectionClick();
    }
  }

  void _animateStepTransition() {
    _slideController.reset();
    _fadeController.reset();
    _scaleController.reset();

    Future.delayed(const Duration(milliseconds: 50), () {
      _fadeController.forward();
    });

    Future.delayed(const Duration(milliseconds: 100), () {
      _scaleController.forward();
    });

    Future.delayed(const Duration(milliseconds: 150), () {
      _slideController.forward();
    });
  }

  bool _canProceedToNextStep() {
    switch (_currentStep) {
      case 0:
        return _step1Valid;
      case 1:
        return _step2Valid;
      case 2:
        return _step3Valid;
      case 3:
        return _step4Valid;
      default:
        return false;
    }
  }

  // Métodos de validación en tiempo real
  void _validateNombre(String value) {
    setState(() {
      if (value.isEmpty) {
        nombreError = 'El nombre es requerido';
        _step2Valid = false;
      } else if (!Cliente.isValidNombre(value)) {
        nombreError = 'Nombre debe tener al menos 2 caracteres';
        _step2Valid = false;
      } else {
        nombreError = null;
        _step2Valid = true;
      }
    });
  }

  void _validateTelefono(String value) {
    setState(() {
      if (value.isEmpty) {
        telefonoError = 'El teléfono es requerido';
        _step3Valid = false;
      } else if (!Cliente.isValidTelefono(value)) {
        telefonoError = 'Formato de teléfono inválido';
        _step3Valid = false;
      } else {
        telefonoError = null;
        _step3Valid = true;
      }
    });
  }

  void _validateEmail(String value) {
    setState(() {
      if (value.isEmpty) {
        emailError = 'El email es requerido';
        _step4Valid = false;
      } else if (!Cliente.isValidEmail(value)) {
        emailError = 'Formato de email inválido';
        _step4Valid = false;
      } else {
        emailError = null;
        _step4Valid = true;
      }
    });
  }

  void _onImagenCambiada(String? nuevaUrl) {
    setState(() {
      _imagenUrl = nuevaUrl;
      _step1Valid = nuevaUrl != null && nuevaUrl.isNotEmpty;
    });
  }

  Future<void> _guardarCliente() async {
    if (!_formKey.currentState!.validate()) {
      _shakeForm();
      return;
    }

    setState(() {
      _isLoading = true;
    });

    // Animación de progreso
    _progressController.forward();

    try {
      final nuevoCliente = Cliente(
        idCliente: DateTime.now().millisecondsSinceEpoch.toString(),
        nombre: nombreController.text.trim(),
        telefono: telefonoController.text.trim(),
        email: emailController.text.trim(),
        imagenUrl: _imagenUrl,
      );

      await ClienteService.insertarCliente(nuevoCliente);

      // Animación de éxito
      await _showSuccessAnimation();

      if (!mounted) return;
      Navigator.pop(context, nuevoCliente);
    } catch (e) {
      _mostrarMensaje("Error al agregar cliente: $e", isError: true);
      _shakeForm();
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        _progressController.reset();
      }
    }
  }

  void _shakeForm() {
    HapticFeedback.heavyImpact();
    _scaleController.reverse().then((_) {
      _scaleController.forward();
    });
  }

  Future<void> _showSuccessAnimation() async {
    setState(() {
      _showSuccess = true;
    });

    HapticFeedback.mediumImpact();
    await _successController.forward();

    await Future.delayed(const Duration(milliseconds: 1000));

    if (mounted) {
      setState(() {
        _showSuccess = false;
      });
    }
  }

  void _mostrarMensaje(String mensaje, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              isError ? Icons.error : Icons.check_circle,
              color: Colors.white,
            ),
            const SizedBox(width: 8),
            Expanded(child: Text(mensaje)),
          ],
        ),
        backgroundColor: isError ? Colors.red : Colors.green,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      body: GradientBackground(
        child: Stack(
          children: [
            // Partículas de fondo
            FloatingParticles(
              particleCount: 25,
              particleColor: theme.primaryColor.withValues(alpha: 0.1),
              child: const SizedBox.expand(),
            ),

            // Contenido principal
            SafeArea(
              child: Column(
                children: [
                  _buildHeader(),
                  _buildProgressIndicator(),
                  Expanded(
                    child: _buildStepContent(),
                  ),
                  _buildNavigationButtons(),
                ],
              ),
            ),

            // Overlay de éxito
            if (_showSuccess) _buildSuccessOverlay(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            // Botón de regreso
            ScaleTransition(
              scale: _scaleAnimation,
              child: Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).cardColor,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [context.adaptiveShadow],
                ),
                child: IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.arrow_back),
                  tooltip: 'Volver',
                ),
              ),
            ),

            const SizedBox(width: 16),

            // Título
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Nuevo Cliente',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Paso ${_currentStep + 1} de $_totalSteps',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: context.adaptiveTextColor.withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ),
            ),

            // Toggle de tema
            ThemeToggleButton(
              onThemeChanged: (theme) {
                // Implementar cambio de tema si es necesario
              },
              currentTheme: ThemeMode.system,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
        child: Row(
          children: List.generate(_totalSteps, (index) {
            final isActive = index <= _currentStep;
            final isCurrent = index == _currentStep;

            return Expanded(
              child: Container(
                margin: EdgeInsets.only(
                  right: index < _totalSteps - 1 ? 8 : 0,
                ),
                child: AnimatedBuilder(
                  animation: _stepAnimation,
                  builder: (context, child) {
                    return Container(
                      height: 4,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(2),
                        color: isActive
                            ? Theme.of(context).primaryColor
                            : Theme.of(context).dividerColor.withValues(alpha: 0.3),
                      ),
                      child: isCurrent
                          ? LinearProgressIndicator(
                              value: _stepAnimation.value,
                              backgroundColor: Colors.transparent,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Theme.of(context).primaryColor.withValues(alpha: 0.8),
                              ),
                            )
                          : null,
                    );
                  },
                ),
              ),
            );
          }),
        ),
      ),
    );
  }

  Widget _buildStepContent() {
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: ScaleTransition(
          scale: _scaleAnimation,
          child: Container(
            margin: const EdgeInsets.all(20),
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Theme.of(context).cardColor,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [context.adaptiveShadow],
            ),
            child: Form(
              key: _formKey,
              child: _getCurrentStepWidget(),
            ),
          ),
        ),
      ),
    );
  }

  Widget _getCurrentStepWidget() {
    switch (_currentStep) {
      case 0:
        return _buildImageStep();
      case 1:
        return _buildNameStep();
      case 2:
        return _buildPhoneStep();
      case 3:
        return _buildEmailStep();
      default:
        return Container();
    }
  }

  Widget _buildImageStep() {
    return Column(
      children: [
        BreathingWidget(
          child: Icon(
            Icons.photo_camera,
            size: 64,
            color: Theme.of(context).primaryColor.withValues(alpha: 0.7),
          ),
        ),
        const SizedBox(height: 24),
        Text(
          'Foto de Perfil',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Selecciona una imagen para el cliente (opcional)',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: context.adaptiveTextColor.withValues(alpha: 0.7),
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 32),
        Center(
          child: GlowContainer(
            glowColor: _step1Valid ? Colors.green : Theme.of(context).primaryColor,
            child: SelectorImagen(
              imagenUrl: _imagenUrl,
              nombre: nombreController.text.isNotEmpty
                  ? nombreController.text
                  : 'Nuevo Cliente',
              carpeta: 'clientes',
              id: DateTime.now().millisecondsSinceEpoch.toString(),
              onImagenCambiada: _onImagenCambiada,
              size: 120,
              esCircular: true,
              iconoPorDefecto: Icons.person,
            ),
          ),
        ),
        const SizedBox(height: 24),
        if (_step1Valid)
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.check_circle, color: Colors.green, size: 20),
              const SizedBox(width: 8),
              Text(
                'Imagen seleccionada',
                style: TextStyle(
                  color: Colors.green,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          )
        else
          ElevatedButton.icon(
            onPressed: () {
              setState(() {
                _step1Valid = true; // Permitir continuar sin imagen
              });
            },
            icon: const Icon(Icons.skip_next),
            label: const Text('Continuar sin imagen'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.grey.shade600,
              foregroundColor: Colors.white,
            ),
          ),
      ],
    );
  }

  Widget _buildNameStep() {
    return Column(
      children: [
        BreathingWidget(
          child: Icon(
            Icons.person,
            size: 64,
            color: Theme.of(context).primaryColor.withValues(alpha: 0.7),
          ),
        ),
        const SizedBox(height: 24),
        Text(
          'Información Personal',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Ingresa el nombre completo del cliente',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: context.adaptiveTextColor.withValues(alpha: 0.7),
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 32),
        _buildAnimatedTextField(
          controller: nombreController,
          focusNode: _nombreFocus,
          label: 'Nombre completo',
          icon: Icons.person,
          keyboardType: TextInputType.name,
          errorText: nombreError,
          onChanged: _validateNombre,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'El nombre es requerido';
            }
            if (!Cliente.isValidNombre(value)) {
              return 'Nombre debe tener al menos 2 caracteres';
            }
            return null;
          },
        ),
        const SizedBox(height: 24),
        if (_step2Valid)
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.check_circle, color: Colors.green, size: 20),
              const SizedBox(width: 8),
              Text(
                'Nombre válido',
                style: TextStyle(
                  color: Colors.green,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
      ],
    );
  }

  Widget _buildPhoneStep() {
    return Column(
      children: [
        BreathingWidget(
          child: Icon(
            Icons.phone,
            size: 64,
            color: Theme.of(context).primaryColor.withValues(alpha: 0.7),
          ),
        ),
        const SizedBox(height: 24),
        Text(
          'Información de Contacto',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Ingresa el número de teléfono del cliente',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: context.adaptiveTextColor.withValues(alpha: 0.7),
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 32),
        _buildAnimatedTextField(
          controller: telefonoController,
          focusNode: _telefonoFocus,
          label: 'Número de teléfono',
          icon: Icons.phone,
          keyboardType: TextInputType.phone,
          errorText: telefonoError,
          onChanged: _validateTelefono,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'El teléfono es requerido';
            }
            if (!Cliente.isValidTelefono(value)) {
              return 'Formato de teléfono inválido';
            }
            return null;
          },
        ),
        const SizedBox(height: 24),
        if (_step3Valid)
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.check_circle, color: Colors.green, size: 20),
              const SizedBox(width: 8),
              Text(
                'Teléfono válido',
                style: TextStyle(
                  color: Colors.green,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
      ],
    );
  }

  Widget _buildEmailStep() {
    return Column(
      children: [
        BreathingWidget(
          child: Icon(
            Icons.email,
            size: 64,
            color: Theme.of(context).primaryColor.withValues(alpha: 0.7),
          ),
        ),
        const SizedBox(height: 24),
        Text(
          'Correo Electrónico',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Ingresa el correo electrónico del cliente',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: context.adaptiveTextColor.withValues(alpha: 0.7),
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 32),
        _buildAnimatedTextField(
          controller: emailController,
          focusNode: _emailFocus,
          label: 'Correo electrónico',
          icon: Icons.email,
          keyboardType: TextInputType.emailAddress,
          errorText: emailError,
          onChanged: _validateEmail,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'El email es requerido';
            }
            if (!Cliente.isValidEmail(value)) {
              return 'Formato de email inválido';
            }
            return null;
          },
        ),
        const SizedBox(height: 24),
        if (_step4Valid)
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.check_circle, color: Colors.green, size: 20),
              const SizedBox(width: 8),
              Text(
                'Email válido',
                style: TextStyle(
                  color: Colors.green,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        const SizedBox(height: 32),
        if (_step4Valid)
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.green.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
            ),
            child: Column(
              children: [
                Icon(Icons.check_circle, color: Colors.green, size: 32),
                const SizedBox(height: 8),
                Text(
                  '¡Listo para guardar!',
                  style: TextStyle(
                    color: Colors.green,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Toda la información está completa',
                  style: TextStyle(
                    color: Colors.green.shade700,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildAnimatedTextField({
    required TextEditingController controller,
    required FocusNode focusNode,
    required String label,
    required IconData icon,
    required TextInputType keyboardType,
    String? errorText,
    required Function(String) onChanged,
    String? Function(String?)? validator,
  }) {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 600),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 20 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Theme.of(context).shadowColor.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: TextFormField(
                controller: controller,
                focusNode: focusNode,
                keyboardType: keyboardType,
                onChanged: onChanged,
                validator: validator,
                decoration: InputDecoration(
                  labelText: label,
                  errorText: errorText,
                  prefixIcon: Icon(icon),
                  filled: true,
                  fillColor: Theme.of(context).cardColor,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(16),
                    borderSide: BorderSide.none,
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(16),
                    borderSide: BorderSide(
                      color: Theme.of(context).dividerColor.withValues(alpha: 0.3),
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(16),
                    borderSide: BorderSide(
                      color: Theme.of(context).primaryColor,
                      width: 2,
                    ),
                  ),
                  errorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(16),
                    borderSide: const BorderSide(
                      color: Colors.red,
                      width: 2,
                    ),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 16,
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildNavigationButtons() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            // Botón Anterior
            if (_currentStep > 0)
              Expanded(
                child: ScaleTransition(
                  scale: _scaleAnimation,
                  child: ElevatedButton.icon(
                    onPressed: _previousStep,
                    icon: const Icon(Icons.arrow_back),
                    label: const Text('Anterior'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey.shade600,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),
              ),

            if (_currentStep > 0) const SizedBox(width: 16),

            // Botón Siguiente/Guardar
            Expanded(
              flex: _currentStep == 0 ? 1 : 1,
              child: ScaleTransition(
                scale: _scaleAnimation,
                child: _currentStep == _totalSteps - 1
                    ? _buildSaveButton()
                    : ElevatedButton.icon(
                        onPressed: _canProceedToNextStep() ? _nextStep : null,
                        icon: const Icon(Icons.arrow_forward),
                        label: const Text('Siguiente'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Theme.of(context).primaryColor,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSaveButton() {
    return AnimatedBuilder(
      animation: _progressAnimation,
      builder: (context, child) {
        return ElevatedButton.icon(
          onPressed: _isLoading ? null : _guardarCliente,
          icon: _isLoading
              ? SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    value: _progressAnimation.value,
                  ),
                )
              : const Icon(Icons.save),
          label: Text(_isLoading ? 'Guardando...' : 'Guardar Cliente'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
            elevation: 8,
            shadowColor: Colors.green.withValues(alpha: 0.3),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSuccessOverlay() {
    return AnimatedBuilder(
      animation: _successAnimation,
      builder: (context, child) {
        return Container(
          color: Colors.black.withValues(alpha: 0.5 * _successAnimation.value),
          child: Center(
            child: Transform.scale(
              scale: _successAnimation.value,
              child: Container(
                margin: const EdgeInsets.all(40),
                padding: const EdgeInsets.all(32),
                decoration: BoxDecoration(
                  color: Colors.green,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.green.withValues(alpha: 0.3),
                      blurRadius: 20,
                      spreadRadius: 5,
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.check_circle,
                      color: Colors.white,
                      size: 64,
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      '¡Cliente creado!',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'El cliente se ha agregado correctamente',
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 16,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 24),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.person, color: Colors.white70, size: 16),
                        const SizedBox(width: 8),
                        Text(
                          nombreController.text,
                          style: const TextStyle(
                            color: Colors.white70,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
