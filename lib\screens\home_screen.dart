import 'package:flutter/material.dart';
import '../widgets/gradient_background.dart';
import '../widgets/responsive_container.dart';
import '../utils/responsive_utils.dart';
import '../routes/app_routes.dart';
import '../services/firebase_service.dart';
import '../services/estadisticas_service.dart';
import '../widgets/modern_loading.dart';
import '../widgets/animated_counter.dart';
import '../widgets/advanced_visual_effects.dart';
import '../widgets/gamification_system.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  DashboardStats? _stats;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _cargarEstadisticas();
  }

  Future<void> _cargarEstadisticas() async {
    try {
      setState(() => _isLoading = true);
      final stats = await EstadisticasService.obtenerEstadisticasDashboard();
      if (mounted) {
        setState(() {
          _stats = stats;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error cargando estadísticas: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _cerrarSesion() async {
    try {
      await FirebaseService.signOut();
      if (context.mounted) {
        Navigator.of(context).pushNamedAndRemoveUntil(
          AppRoutes.auth,
          (route) => false,
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error al cerrar sesión: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: ResponsiveText(
          "3M Shop",
          mobileFontSize: 18,
          tabletFontSize: 20,
          desktopFontSize: 22,
          fontWeight: FontWeight.w600,
        ),
        backgroundColor: Colors.indigo.shade600,
        elevation: 0,
        centerTitle: ResponsiveUtils.isMobile(context),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _cargarEstadisticas,
            tooltip: 'Actualizar estadísticas',
          ),
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: _cerrarSesion,
            tooltip: 'Cerrar Sesión',
          ),
        ],
      ),
      body: GradientBackground(
        child: ResponsiveContainer(
          child: Column(
            children: [
              ResponsiveSpacing(mobile: 16, tablet: 24, desktop: 32),
              // Header mejorado con información
              _buildWelcomeHeader(context),
              ResponsiveSpacing(mobile: 24, tablet: 32, desktop: 40),
              Expanded(
                child: ResponsiveGrid(
                  mobileColumns: 2,
                  tabletColumns: 3,
                  desktopColumns: 4,
                  children: [
                    _buildMenuCard(
                        context, "Clientes", Icons.people_outline, AppRoutes.clientes,
                        color: const Color(0xFF4CAF50), subtitle: "Gestionar clientes"),
                    _buildMenuCard(context, "Facturas", Icons.receipt_long_outlined,
                        AppRoutes.facturas,
                        color: const Color(0xFF2196F3), subtitle: "Crear facturas"),
                    _buildMenuCard(
                        context, "Módulos", Icons.grid_view_outlined, AppRoutes.modulos,
                        color: const Color(0xFF9C27B0), subtitle: "Gestionar productos"),
                    _buildMenuCard(context, "Reportes", Icons.bar_chart_outlined,
                        AppRoutes.reportes,
                        color: const Color(0xFFFF9800), subtitle: "Ver estadísticas"),
                    _buildMenuCard(context, "Configuración", Icons.settings_outlined,
                        AppRoutes.configuracion,
                        color: const Color(0xFF607D8B), subtitle: "Ajustes de app"),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMenuCard(
      BuildContext context, String titulo, IconData icono, String routeName,
      {Color? color, String? subtitle}) {
    final deviceType = ResponsiveUtils.getDeviceType(context);
    final isMobile = ResponsiveUtils.isMobile(context);

    // Tamaños adaptativos para iconos
    double iconSize;
    switch (deviceType) {
      case DeviceType.mobile:
        iconSize = 36;
        break;
      case DeviceType.tablet:
        iconSize = 44;
        break;
      case DeviceType.desktop:
      case DeviceType.largeDesktop:
        iconSize = 52;
        break;
    }

    // Border radius adaptativo
    final borderRadius = isMobile ? 16.0 : 20.0;

    return InkWell(
      onTap: () {
        // Usar transición específica según el módulo
        final moduleType = titulo.toLowerCase();
        Navigator.of(context).pushNamed(routeName);

        // TODO: Implementar transiciones personalizadas cuando se creen las pantallas
        // context.pushWithTransition(
        //   _getScreenForRoute(routeName),
        //   moduleType: moduleType,
        // );
      },
      borderRadius: BorderRadius.circular(borderRadius),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(borderRadius),
          gradient: LinearGradient(
            colors: color != null
                ? [color, color.withValues(alpha: 0.7)]
                : [Colors.indigo.shade500, Colors.teal.shade400],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black26,
              blurRadius: isMobile ? 4 : 6,
              offset: Offset(2, isMobile ? 2 : 4),
            ),
          ],
        ),
        padding: ResponsiveUtils.getAdaptivePadding(
          context,
          mobile: 12,
          tablet: 16,
          desktop: 20,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Icono con fondo circular
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Icon(icono, size: iconSize * 0.8, color: Colors.white),
            ),
            ResponsiveSpacing(mobile: 8, tablet: 12, desktop: 16),

            // Título
            Flexible(
              child: ResponsiveText(
                titulo,
                mobileFontSize: 14,
                tabletFontSize: 16,
                desktopFontSize: 18,
                color: Colors.white,
                fontWeight: FontWeight.bold,
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),

            // Subtítulo si existe
            if (subtitle != null) ...[
              ResponsiveSpacing(mobile: 2, tablet: 4, desktop: 6),
              Flexible(
                child: ResponsiveText(
                  subtitle,
                  mobileFontSize: 10,
                  tabletFontSize: 11,
                  desktopFontSize: 12,
                  color: Colors.white70,
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      margin: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // Icono principal con animación
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(50),
            ),
            child: const Icon(
              Icons.store,
              size: 48,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),

          // Texto de bienvenida
          ResponsiveText(
            'Bienvenido a 3M Shop',
            mobileFontSize: 22,
            tabletFontSize: 26,
            desktopFontSize: 30,
            fontWeight: FontWeight.bold,
            color: Colors.white,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),

          // Subtítulo
          ResponsiveText(
            'Sistema de Gestión Empresarial',
            mobileFontSize: 14,
            tabletFontSize: 16,
            desktopFontSize: 18,
            color: Colors.white70,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),

          // Información rápida
          _isLoading
            ? const Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  SizedBox(width: 60, height: 40, child: ModernLoading(size: 20, showMessage: false)),
                  SizedBox(width: 60, height: 40, child: ModernLoading(size: 20, showMessage: false)),
                  SizedBox(width: 60, height: 40, child: ModernLoading(size: 20, showMessage: false)),
                ],
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildQuickInfo(
                    Icons.people,
                    'Clientes',
                    _stats?.clientes.totalFormatted ?? '0'
                  ),
                  _buildQuickInfo(
                    Icons.receipt,
                    'Facturas',
                    _stats?.facturas.totalFormatted ?? '0'
                  ),
                  _buildQuickInfo(
                    Icons.trending_up,
                    'Ventas',
                    _stats?.ventas.totalFormatted ?? '\$0'
                  ),
                ],
              ),
        ],
      ),
    );
  }

  Widget _buildQuickInfo(IconData icon, String label, String value) {
    // Extraer número del valor para animación
    double numericValue = 0;
    String prefix = '';

    if (value.startsWith('\$')) {
      prefix = '\$';
      final cleanValue = value.substring(1);
      if (cleanValue.endsWith('K')) {
        numericValue = double.tryParse(cleanValue.substring(0, cleanValue.length - 1)) ?? 0;
        numericValue *= 1000;
      } else if (cleanValue.endsWith('M')) {
        numericValue = double.tryParse(cleanValue.substring(0, cleanValue.length - 1)) ?? 0;
        numericValue *= 1000000;
      } else {
        numericValue = double.tryParse(cleanValue) ?? 0;
      }
    } else {
      if (value.endsWith('K+')) {
        numericValue = double.tryParse(value.substring(0, value.length - 2)) ?? 0;
        numericValue *= 1000;
      } else if (value.endsWith('+')) {
        numericValue = double.tryParse(value.substring(0, value.length - 1)) ?? 0;
      } else {
        numericValue = double.tryParse(value) ?? 0;
      }
    }

    return BreathingWidget(
      duration: const Duration(seconds: 4),
      child: Column(
        children: [
          GlowContainer(
            glowColor: Colors.white,
            glowRadius: 10,
            child: Icon(icon, color: Colors.white70, size: 20),
          ),
          const SizedBox(height: 4),
          AnimatedCounter(
            value: numericValue,
            prefix: prefix,
            textStyle: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
            showTrendArrow: true,
            previousValue: numericValue * 0.9, // Simular valor anterior
          ),
          Text(
            label,
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }
}
