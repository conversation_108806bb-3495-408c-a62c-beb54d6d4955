import 'package:flutter/material.dart';
import '../widgets/gradient_background.dart';
import '../widgets/responsive_container.dart';
import '../utils/responsive_utils.dart';
import '../routes/app_routes.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: ResponsiveText(
          "Menú Principal",
          mobileFontSize: 18,
          tabletFontSize: 20,
          desktopFontSize: 22,
          fontWeight: FontWeight.w600,
        ),
        backgroundColor: Colors.indigo.shade600,
        elevation: 0,
        centerTitle: ResponsiveUtils.isMobile(context),
      ),
      body: GradientBackground(
        child: ResponsiveContainer(
          child: Column(
            children: [
              ResponsiveSpacing(mobile: 16, tablet: 24, desktop: 32),
              ResponsiveText(
                'Selecciona una opción',
                mobileFontSize: 20,
                tabletFontSize: 24,
                desktopFontSize: 28,
                fontWeight: FontWeight.bold,
                color: Colors.white,
                textAlign: TextAlign.center,
              ),
              ResponsiveSpacing(mobile: 16, tablet: 20, desktop: 24),
              Expanded(
                child: ResponsiveGrid(
                  mobileColumns: 2,
                  tabletColumns: 3,
                  desktopColumns: 4,
                  children: [
                    _buildMenuCard(
                        context, "Clientes", Icons.people, AppRoutes.clientes),
                    _buildMenuCard(context, "Facturas", Icons.receipt_long,
                        AppRoutes.facturas),
                    _buildMenuCard(
                        context, "Módulos", Icons.grid_view, AppRoutes.modulos),
                    _buildMenuCard(context, "Reportes", Icons.bar_chart,
                        AppRoutes.reportes),
                    _buildMenuCard(context, "Configuración", Icons.settings,
                        AppRoutes.configuracion),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMenuCard(
      BuildContext context, String titulo, IconData icono, String routeName,
      {Color? color}) {
    final deviceType = ResponsiveUtils.getDeviceType(context);
    final isMobile = ResponsiveUtils.isMobile(context);

    // Tamaños adaptativos para iconos
    double iconSize;
    switch (deviceType) {
      case DeviceType.mobile:
        iconSize = 36;
        break;
      case DeviceType.tablet:
        iconSize = 44;
        break;
      case DeviceType.desktop:
      case DeviceType.largeDesktop:
        iconSize = 52;
        break;
    }

    // Border radius adaptativo
    final borderRadius = isMobile ? 16.0 : 20.0;

    return InkWell(
      onTap: () {
        context.pushNamed(routeName);
      },
      borderRadius: BorderRadius.circular(borderRadius),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(borderRadius),
          gradient: LinearGradient(
            colors: color != null
                ? [color, color.withValues(alpha: 0.7)]
                : [Colors.indigo.shade500, Colors.teal.shade400],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black26,
              blurRadius: isMobile ? 4 : 6,
              offset: Offset(2, isMobile ? 2 : 4),
            ),
          ],
        ),
        padding: ResponsiveUtils.getAdaptivePadding(
          context,
          mobile: 12,
          tablet: 16,
          desktop: 20,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icono, size: iconSize, color: Colors.white),
            ResponsiveSpacing(mobile: 4, tablet: 8, desktop: 12),
            Flexible(
              child: ResponsiveText(
                titulo,
                mobileFontSize: 12,
                tabletFontSize: 14,
                desktopFontSize: 16,
                color: Colors.white,
                fontWeight: FontWeight.bold,
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
