# 3M Shop - Sistema de Gestión

Sistema completo de gestión de facturas y módulos desarrollado en Flutter con Firebase.

## 🚀 Características

- **Gestión de Clientes**: Crear, editar y administrar información de clientes
- **Facturación**: Sistema completo de creación y gestión de facturas
- **Módulos**: Administración de módulos con precios y descripciones
- **Autenticación**: Sistema seguro de login con Firebase Auth
- **Responsive**: Diseño adaptativo para móvil, tablet y desktop
- **Tiempo Real**: Actualizaciones automáticas con Firebase Firestore

## 🛠️ Tecnologías

- **Flutter 3.0+**: Framework de desarrollo multiplataforma
- **Firebase**: Backend como servicio (Auth, Firestore, Storage)
- **Material Design 3**: Sistema de diseño moderno
- **Arquitectura MVC**: Separación clara de responsabilidades

## 📱 Plataformas Soportadas

- ✅ Android
- ✅ iOS
- ✅ Web
- ✅ Windows
- ✅ macOS
- ✅ Linux

## 🔧 Instalación

1. **Clonar el repositorio**
   ```bash
   git clone [URL_DEL_REPOSITORIO]
   cd shop_3m
   ```

2. **Instalar dependencias**
   ```bash
   flutter pub get
   ```

3. **Configurar Firebase**
   - Crear proyecto en Firebase Console
   - Agregar aplicaciones (Android/iOS/Web)
   - Descargar archivos de configuración
   - Habilitar Authentication y Firestore

4. **Ejecutar la aplicación**
   ```bash
   flutter run
   ```

## 🏗️ Estructura del Proyecto

```
lib/
├── models/          # Modelos de datos
├── services/        # Servicios de Firebase y lógica de negocio
├── screens/         # Pantallas de la aplicación
├── widgets/         # Widgets reutilizables
├── utils/           # Utilidades y helpers
└── routes/          # Sistema de navegación
```

## 📦 Build para Producción

### Android
```bash
flutter build apk --release
flutter build appbundle --release
```

### iOS
```bash
flutter build ios --release
```

### Web
```bash
flutter build web --release
```

## 🔐 Configuración de Seguridad

- Reglas de Firestore configuradas
- Validación de entrada en formularios
- Manejo seguro de autenticación
- Encriptación de datos sensibles

## 📄 Licencia

Este proyecto está bajo la Licencia MIT - ver el archivo [LICENSE](LICENSE) para detalles.

## 👥 Contribuir

1. Fork el proyecto
2. Crear una rama para tu feature (`git checkout -b feature/AmazingFeature`)
3. Commit tus cambios (`git commit -m 'Add some AmazingFeature'`)
4. Push a la rama (`git push origin feature/AmazingFeature`)
5. Abrir un Pull Request

## 📞 Soporte

Para soporte técnico o consultas, contactar a [<EMAIL>]
