import 'package:cloud_firestore/cloud_firestore.dart';

class Modulo {
  final String idModulo;
  final String nombre;
  final double precio;
  final String? imagenUrl;
  final bool activo;
  final String? descripcion;
  final DateTime fechaCreacion;

  Mo<PERSON><PERSON>({
    required this.idModulo,
    required this.nombre,
    required this.precio,
    this.imagenUrl,
    this.activo = true,
    this.descripcion,
    DateTime? fechaCreacion,
  }) : fechaCreacion = fechaCreacion ?? DateTime.now() {
    // Validación en constructor
    if (nombre.trim().isEmpty) {
      throw ArgumentError('El nombre del módulo no puede estar vacío');
    }
    if (nombre.trim().length < 2) {
      throw ArgumentError(
          'El nombre del módulo debe tener al menos 2 caracteres');
    }
    if (precio < 0) {
      throw ArgumentError('El precio del módulo no puede ser negativo');
    }
    if (precio == 0) {
      throw ArgumentError('El precio del módulo debe ser mayor a 0');
    }
  }

  // Métodos de validación estática
  static bool isValidNombre(String nombre) {
    return nombre.trim().isNotEmpty && nombre.trim().length >= 2;
  }

  static bool isValidPrecio(double precio) {
    return precio > 0;
  }

  static String? validateNombre(String nombre) {
    if (nombre.trim().isEmpty) {
      return 'El nombre es obligatorio';
    }
    if (nombre.trim().length < 2) {
      return 'El nombre debe tener al menos 2 caracteres';
    }
    if (nombre.trim().length > 100) {
      return 'El nombre no puede exceder 100 caracteres';
    }
    // Validar caracteres especiales básicos
    if (!RegExp(r'^[a-zA-Z0-9\s\-_áéíóúÁÉÍÓÚñÑ]+$').hasMatch(nombre.trim())) {
      return 'El nombre contiene caracteres no válidos';
    }
    return null;
  }

  static String? validatePrecio(String precioStr) {
    if (precioStr.trim().isEmpty) {
      return 'El precio es obligatorio';
    }

    final precio = double.tryParse(precioStr.trim());
    if (precio == null) {
      return 'Ingrese un precio válido';
    }

    if (precio <= 0) {
      return 'El precio debe ser mayor a 0';
    }

    if (precio > 999999999) {
      return 'El precio es demasiado alto';
    }

    return null;
  }

  // Validar todos los campos de una vez
  static Map<String, String> validateAll({
    required String nombre,
    required String precioStr,
  }) {
    Map<String, String> errors = {};

    final nombreError = validateNombre(nombre);
    if (nombreError != null) {
      errors['nombre'] = nombreError;
    }

    final precioError = validatePrecio(precioStr);
    if (precioError != null) {
      errors['precio'] = precioError;
    }

    return errors;
  }

  // Convertir Modulo a Map
  Map<String, dynamic> toMap() {
    return {
      'idModulo': idModulo,
      'nombre': nombre.trim(),
      'precio': precio,
      'imagenUrl': imagenUrl,
      'activo': activo,
      'descripcion': descripcion,
      'fechaCreacion': fechaCreacion.toIso8601String(),
    };
  }

  // Convertir a formato Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'idModulo': idModulo,
      'nombre': nombre.trim(),
      'precio': precio,
      'imagenUrl': imagenUrl,
      'activo': activo,
      'descripcion': descripcion,
      'fechaCreacion': Timestamp.fromDate(fechaCreacion),
    };
  }

  // Crear Modulo desde un Map (al recuperar datos) con validación
  factory Modulo.fromMap(Map<String, dynamic> map) {
    try {
      return Modulo(
        idModulo: map['IdModulo'] ?? map['idModulo'] ?? "",
        nombre: map['Nombre'] ?? map['nombre'] ?? "",
        precio: (map['Precio'] is int)
            ? (map['Precio'] as int).toDouble()
            : (map['Precio'] as double? ?? map['precio']?.toDouble() ?? 0.0),
        imagenUrl: map['imagenUrl'],
        activo: map['activo'] ?? true,
        descripcion: map['descripcion'],
        fechaCreacion: map['fechaCreacion'] != null
            ? DateTime.parse(map['fechaCreacion'])
            : DateTime.now(),
      );
    } catch (e) {
      throw FormatException('Error al crear Módulo desde Map: $e');
    }
  }

  // Crear Modulo desde Firestore
  factory Modulo.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return Modulo(
      idModulo: data['idModulo'] ?? doc.id,
      nombre: data['nombre'] ?? '',
      precio: (data['precio'] ?? 0).toDouble(),
      imagenUrl: data['imagenUrl'],
      activo: data['activo'] ?? true,
      descripcion: data['descripcion'],
      fechaCreacion: data['fechaCreacion'] != null
          ? (data['fechaCreacion'] as Timestamp).toDate()
          : DateTime.now(),
    );
  }

  // Método copyWith para crear copias modificadas
  Modulo copyWith({
    String? idModulo,
    String? nombre,
    double? precio,
    String? imagenUrl,
  }) {
    return Modulo(
      idModulo: idModulo ?? this.idModulo,
      nombre: nombre ?? this.nombre,
      precio: precio ?? this.precio,
      imagenUrl: imagenUrl ?? this.imagenUrl,
    );
  }

  // Método toString para debugging
  @override
  String toString() {
    return 'Modulo{idModulo: $idModulo, nombre: $nombre, precio: $precio}';
  }

  // Métodos de comparación
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Modulo &&
        other.idModulo == idModulo &&
        other.nombre == nombre &&
        other.precio == precio;
  }

  @override
  int get hashCode {
    return idModulo.hashCode ^ nombre.hashCode ^ precio.hashCode;
  }

  // Método para formatear precio como string
  String get precioFormateado {
    return '\$${precio.toStringAsFixed(2)}';
  }

  // Método para obtener nombre normalizado (para búsquedas)
  String get nombreNormalizado {
    return nombre.trim().toLowerCase();
  }
}
