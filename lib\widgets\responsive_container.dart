import 'package:flutter/material.dart';
import '../utils/responsive_utils.dart';

/// Widget contenedor que se adapta automáticamente a diferentes tamaños de pantalla
/// Proporciona padding, ancho máximo y centrado responsive
class ResponsiveContainer extends StatelessWidget {
  final Widget child;
  final double? maxWidth;
  final EdgeInsets? padding;
  final bool centerContent;
  final Color? backgroundColor;
  final BoxDecoration? decoration;

  const ResponsiveContainer({
    super.key,
    required this.child,
    this.maxWidth,
    this.padding,
    this.centerContent = true,
    this.backgroundColor,
    this.decoration,
  });

  @override
  Widget build(BuildContext context) {
    final adaptivePadding = padding ?? ResponsiveUtils.getAdaptivePadding(context);
    final contentMaxWidth = maxWidth ?? ResponsiveUtils.getMaxContentWidth(context);

    Widget content = Container(
      width: double.infinity,
      constraints: BoxConstraints(maxWidth: contentMaxWidth),
      padding: adaptivePadding,
      decoration: decoration,
      child: child,
    );

    if (centerContent && !ResponsiveUtils.isMobile(context)) {
      content = Center(child: content);
    }

    if (backgroundColor != null && decoration == null) {
      content = Container(
        color: backgroundColor,
        child: content,
      );
    }

    return content;
  }
}

/// Widget para grids responsive que se adaptan automáticamente
class ResponsiveGrid extends StatelessWidget {
  final List<Widget> children;
  final int? mobileColumns;
  final int? tabletColumns;
  final int? desktopColumns;
  final double? crossAxisSpacing;
  final double? mainAxisSpacing;
  final double? childAspectRatio;
  final EdgeInsets? padding;
  final bool shrinkWrap;
  final ScrollPhysics? physics;

  const ResponsiveGrid({
    super.key,
    required this.children,
    this.mobileColumns,
    this.tabletColumns,
    this.desktopColumns,
    this.crossAxisSpacing,
    this.mainAxisSpacing,
    this.childAspectRatio,
    this.padding,
    this.shrinkWrap = false,
    this.physics,
  });

  @override
  Widget build(BuildContext context) {
    final columns = ResponsiveUtils.getGridColumns(
      context,
      mobileColumns: mobileColumns,
      tabletColumns: tabletColumns,
      desktopColumns: desktopColumns,
    );

    final spacing = ResponsiveUtils.getAdaptiveSpacing(context);
    final aspectRatio = childAspectRatio ?? ResponsiveUtils.getCardAspectRatio(context);

    return GridView.count(
      crossAxisCount: columns,
      crossAxisSpacing: crossAxisSpacing ?? spacing,
      mainAxisSpacing: mainAxisSpacing ?? spacing,
      childAspectRatio: aspectRatio,
      padding: padding ?? ResponsiveUtils.getAdaptivePadding(context),
      shrinkWrap: shrinkWrap,
      physics: physics,
      children: children,
    );
  }
}

/// Widget para texto responsive que ajusta su tamaño automáticamente
class ResponsiveText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final double? mobileFontSize;
  final double? tabletFontSize;
  final double? desktopFontSize;
  final FontWeight? fontWeight;
  final Color? color;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;

  const ResponsiveText(
    this.text, {
    super.key,
    this.style,
    this.mobileFontSize,
    this.tabletFontSize,
    this.desktopFontSize,
    this.fontWeight,
    this.color,
    this.textAlign,
    this.maxLines,
    this.overflow,
  });

  @override
  Widget build(BuildContext context) {
    final adaptiveFontSize = ResponsiveUtils.getAdaptiveFontSize(
      context,
      mobile: mobileFontSize,
      tablet: tabletFontSize,
      desktop: desktopFontSize,
    );

    final textStyle = (style ?? const TextStyle()).copyWith(
      fontSize: adaptiveFontSize,
      fontWeight: fontWeight,
      color: color,
    );

    return Text(
      text,
      style: textStyle,
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }
}

/// Widget para espaciado responsive
class ResponsiveSpacing extends StatelessWidget {
  final double? mobile;
  final double? tablet;
  final double? desktop;
  final bool isHorizontal;

  const ResponsiveSpacing({
    super.key,
    this.mobile,
    this.tablet,
    this.desktop,
    this.isHorizontal = false,
  });

  const ResponsiveSpacing.horizontal({
    super.key,
    this.mobile,
    this.tablet,
    this.desktop,
  }) : isHorizontal = true;

  @override
  Widget build(BuildContext context) {
    final spacing = ResponsiveUtils.getAdaptiveSpacing(
      context,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );

    return SizedBox(
      width: isHorizontal ? spacing : null,
      height: isHorizontal ? null : spacing,
    );
  }
}

/// Widget para botones responsive
class ResponsiveButton extends StatelessWidget {
  final Widget child;
  final VoidCallback? onPressed;
  final ButtonStyle? style;
  final double? mobileHeight;
  final double? tabletHeight;
  final double? desktopHeight;
  final double? mobileFontSize;
  final double? tabletFontSize;
  final double? desktopFontSize;
  final bool isFullWidth;

  const ResponsiveButton({
    super.key,
    required this.child,
    this.onPressed,
    this.style,
    this.mobileHeight,
    this.tabletHeight,
    this.desktopHeight,
    this.mobileFontSize,
    this.tabletFontSize,
    this.desktopFontSize,
    this.isFullWidth = false,
  });

  @override
  Widget build(BuildContext context) {
    final adaptiveHeight = ResponsiveUtils.getAdaptiveHeight(
      context,
      mobile: mobileHeight,
      tablet: tabletHeight,
      desktop: desktopHeight,
    );

    final adaptiveFontSize = ResponsiveUtils.getAdaptiveFontSize(
      context,
      mobile: mobileFontSize,
      tablet: tabletFontSize,
      desktop: desktopFontSize,
    );

    final buttonStyle = (style ?? ElevatedButton.styleFrom()).copyWith(
      minimumSize: WidgetStateProperty.all(
        Size(isFullWidth ? double.infinity : 0, adaptiveHeight),
      ),
      textStyle: WidgetStateProperty.all(
        TextStyle(fontSize: adaptiveFontSize),
      ),
    );

    Widget button = ElevatedButton(
      onPressed: onPressed,
      style: buttonStyle,
      child: child,
    );

    if (isFullWidth) {
      button = SizedBox(
        width: double.infinity,
        child: button,
      );
    }

    return button;
  }
}

/// Widget para diálogos responsive
class ResponsiveDialog extends StatelessWidget {
  final Widget child;
  final String? title;
  final List<Widget>? actions;
  final EdgeInsets? contentPadding;
  final bool scrollable;

  const ResponsiveDialog({
    super.key,
    required this.child,
    this.title,
    this.actions,
    this.contentPadding,
    this.scrollable = false,
  });

  @override
  Widget build(BuildContext context) {
    final isMobile = ResponsiveUtils.isMobile(context);
    final adaptivePadding = ResponsiveUtils.getAdaptivePadding(context);

    if (isMobile) {
      // En móvil, usar diálogo de pantalla completa o bottom sheet
      return Dialog.fullscreen(
        child: Scaffold(
          appBar: AppBar(
            title: title != null ? Text(title!) : null,
            leading: IconButton(
              icon: const Icon(Icons.close),
              onPressed: () => Navigator.of(context).pop(),
            ),
            actions: actions,
          ),
          body: Padding(
            padding: contentPadding ?? adaptivePadding,
            child: scrollable ? SingleChildScrollView(child: child) : child,
          ),
        ),
      );
    } else {
      // En tablet/desktop, usar diálogo normal con tamaño adaptativo
      return Dialog(
        child: Container(
          constraints: BoxConstraints(
            maxWidth: ResponsiveUtils.getMaxContentWidth(context) * 0.8,
            maxHeight: MediaQuery.of(context).size.height * 0.8,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (title != null)
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          title!,
                          style: Theme.of(context).textTheme.headlineSmall,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.of(context).pop(),
                      ),
                    ],
                  ),
                ),
              Flexible(
                child: Padding(
                  padding: contentPadding ?? adaptivePadding,
                  child: scrollable ? SingleChildScrollView(child: child) : child,
                ),
              ),
              if (actions != null)
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: actions!,
                  ),
                ),
            ],
          ),
        ),
      );
    }
  }
}
